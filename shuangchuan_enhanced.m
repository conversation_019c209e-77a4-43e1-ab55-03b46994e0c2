%% 增强版单体船 vs 双体船效率对比分析
% 包含多种现实约束条件：容量限制、船舶性能差异、泊位调度、装载时间变化等
% 10艘单体船 vs 5艘双体船运送20台风机

clear all;
close all;
clc;
rng(42);

%% 问题参数设置
TURBINE_COUNT = 20;        % 风机总数
SINGLE_VESSEL_COUNT = 10;  % 单体船数量
DUAL_VESSEL_COUNT = 5;     % 双体船数量
BERTH_COUNT = 5;           % 泊位数量

% 风机类型参数（混合大小型风机）
SMALL_TURBINE_RATIO = 0.4; % 40%小型风机
SMALL_TURBINE_POWER = 3.5; % MW
LARGE_TURBINE_POWER = 8.0; % MW
SMALL_TURBINE_PROCESS_TIMES = [20, 12, 10, 30]; % 小型风机工序时间
LARGE_TURBINE_PROCESS_TIMES = [30, 18, 15, 45]; % 大型风机工序时间

% 船舶性能参数
SHIP_ASSEMBLY_TIME = 35;   % 船上组装时间
FINAL_INSTALLATION_TIME = 12; % 最终安装时间
WELDING_TIME = 4;          % 拼接时间

% 距离和基础参数
PORT_TO_FARM_DISTANCE = 80; % km
BASE_VESSEL_SPEED = 15;     % km/h
BASE_LOADING_TIME = 6;      % hours
MAX_CAPACITY = 2;           % 每船最大风机容量

%% 增强的数据结构初始化
% 初始化风机（混合类型）
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'type', cell(1, TURBINE_COUNT), ...
                 'power', cell(1, TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT), ...
                 'weight', cell(1, TURBINE_COUNT), ...
                 'complexity', cell(1, TURBINE_COUNT));

for i = 1:TURBINE_COUNT
    if rand() < SMALL_TURBINE_RATIO
        turbines(i).type = 1; % 小型风机
        turbines(i).power = SMALL_TURBINE_POWER;
        base_times = SMALL_TURBINE_PROCESS_TIMES;
        turbines(i).weight = 150 + 50*rand(); % 150-200吨
        turbines(i).complexity = 0.8 + 0.3*rand(); % 复杂度因子
    else
        turbines(i).type = 2; % 大型风机
        turbines(i).power = LARGE_TURBINE_POWER;
        base_times = LARGE_TURBINE_PROCESS_TIMES;
        turbines(i).weight = 300 + 100*rand(); % 300-400吨
        turbines(i).complexity = 1.0 + 0.4*rand(); % 复杂度因子
    end
    
    % 工序时间受复杂度影响
    variation = 0.9 + 0.2*rand(1, length(base_times));
    turbines(i).processes = base_times .* variation * turbines(i).complexity;
end

% 初始化单体船（性能差异）
single_vessels = struct('id', num2cell(1:SINGLE_VESSEL_COUNT), ...
                       'type', num2cell(ones(1, SINGLE_VESSEL_COUNT)), ...
                       'speed', cell(1, SINGLE_VESSEL_COUNT), ...
                       'capacity', cell(1, SINGLE_VESSEL_COUNT), ...
                       'loading_time', cell(1, SINGLE_VESSEL_COUNT), ...
                       'deck_space', cell(1, SINGLE_VESSEL_COUNT), ...
                       'crane_capacity', cell(1, SINGLE_VESSEL_COUNT), ...
                       'fuel_consumption', cell(1, SINGLE_VESSEL_COUNT));

for i = 1:SINGLE_VESSEL_COUNT
    single_vessels(i).speed = BASE_VESSEL_SPEED * (0.8 + 0.4*rand()); % 12-20 km/h
    single_vessels(i).capacity = randi([1, MAX_CAPACITY]); % 1-2台风机
    single_vessels(i).loading_time = BASE_LOADING_TIME * (0.8 + 0.4*rand()); % 4.8-8.4小时
    single_vessels(i).deck_space = 800 + 400*rand(); % 800-1200 m²
    single_vessels(i).crane_capacity = 200 + 100*rand(); % 200-300吨
    single_vessels(i).fuel_consumption = 50 + 20*rand(); % 50-70 L/h
end

% 初始化双体船（更强性能）
dual_vessels = struct('id', num2cell(1:DUAL_VESSEL_COUNT), ...
                     'type', num2cell(ones(1, DUAL_VESSEL_COUNT) * 2), ...
                     'speed', cell(1, DUAL_VESSEL_COUNT), ...
                     'capacity', cell(1, DUAL_VESSEL_COUNT), ...
                     'loading_time', cell(1, DUAL_VESSEL_COUNT), ...
                     'deck_space', cell(1, DUAL_VESSEL_COUNT), ...
                     'crane_capacity', cell(1, DUAL_VESSEL_COUNT), ...
                     'fuel_consumption', cell(1, DUAL_VESSEL_COUNT), ...
                     'welding_time', cell(1, DUAL_VESSEL_COUNT), ...
                     'assembly_efficiency', cell(1, DUAL_VESSEL_COUNT));

for i = 1:DUAL_VESSEL_COUNT
    dual_vessels(i).speed = BASE_VESSEL_SPEED * (0.9 + 0.3*rand()); % 13.5-18 km/h
    dual_vessels(i).capacity = randi([2, 4]); % 2-4台风机
    dual_vessels(i).loading_time = BASE_LOADING_TIME * (0.7 + 0.3*rand()); % 4.2-6小时
    dual_vessels(i).deck_space = 2000 + 1000*rand(); % 2000-3000 m²
    dual_vessels(i).crane_capacity = 400 + 200*rand(); % 400-600吨
    dual_vessels(i).fuel_consumption = 80 + 30*rand(); % 80-110 L/h
    dual_vessels(i).welding_time = WELDING_TIME * (0.8 + 0.4*rand()); % 3.2-5.6小时
    dual_vessels(i).assembly_efficiency = 0.8 + 0.3*rand(); % 组装效率因子
end

% 初始化泊位（不同性能）
berths = struct('id', num2cell(1:BERTH_COUNT), ...
               'capacity', cell(1, BERTH_COUNT), ...
               'crane_capacity', cell(1, BERTH_COUNT), ...
               'assembly_space', cell(1, BERTH_COUNT));

for i = 1:BERTH_COUNT
    berths(i).capacity = randi([1, 3]); % 1-3艘船同时使用
    berths(i).crane_capacity = 300 + 200*rand(); % 300-500吨
    berths(i).assembly_space = 1500 + 1000*rand(); % 1500-2500 m²
end

%% 执行对比分析
fprintf('=== 增强版海上风机安装效率对比分析 ===\n');
fprintf('项目规模：%d台风机 (%.0f%%小型%.1fMW + %.0f%%大型%.1fMW)\n', ...
    TURBINE_COUNT, SMALL_TURBINE_RATIO*100, SMALL_TURBINE_POWER, ...
    (1-SMALL_TURBINE_RATIO)*100, LARGE_TURBINE_POWER);
fprintf('船舶配置：%d艘单体船 vs %d艘双体船\n', SINGLE_VESSEL_COUNT, DUAL_VESSEL_COUNT);
fprintf('泊位限制：%d个泊位\n', BERTH_COUNT);

%% 场景1：单体船方案
fprintf('\n=== 场景1：单体船方案 ===\n');
tic;
single_schedule = generateEnhancedSingleSchedule(turbines, single_vessels, berths);
single_time = toc;
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时 (计算耗时: %.3f秒)\n', single_makespan, single_time);

%% 场景2：双体船方案
fprintf('\n=== 场景2：双体船方案 ===\n');
tic;
dual_schedule = generateEnhancedDualSchedule(turbines, dual_vessels, berths);
dual_time = toc;
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时 (计算耗时: %.3f秒)\n', dual_makespan, dual_time);

%% 综合分析
analyzeEnhancedComparison(single_schedule, dual_schedule, single_makespan, dual_makespan, ...
                         turbines, single_vessels, dual_vessels, berths);

%% 绘制增强对比图
plotEnhancedComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                      single_makespan, dual_makespan, turbines, berths);

%% 增强的单体船调度函数
function schedule = generateEnhancedSingleSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', [], 'efficiency_factor', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 智能分配：根据风机重量和船舶容量
    turbine_assignments = assignTurbinesToVessels(turbines, vessels);
    
    for assignment_idx = 1:length(turbine_assignments)
        assignment = turbine_assignments(assignment_idx);
        turbine_idx = assignment.turbine_id;
        vessel_idx = assignment.vessel_id;
        
        % 选择最适合的泊位
        berth_idx = selectOptimalBerth(turbines(turbine_idx), vessels(vessel_idx), berths, berth_avail_time);
        
        % 计算效率因子
        efficiency_factor = calculateEfficiencyFactor(turbines(turbine_idx), vessels(vessel_idx), 1);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), berth_avail_time(berth_idx));
        loading_duration = vessels(vessel_idx).loading_time * (1 + (turbines(turbine_idx).weight - 150) / 300);
        loading_end = loading_start + loading_duration;
        berth_avail_time(berth_idx) = loading_end;
        
        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, ...
                                  0, berth_idx, 1, efficiency_factor);
        
        % 航行到风场
        travel_time = 80 / vessels(vessel_idx).speed;
        travel_start = loading_end;
        travel_end = travel_start + travel_time;
        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, ...
                                  -1, 0, 1, efficiency_factor);
        
        % 安装工序
        current_time = travel_end;
        process_times = turbines(turbine_idx).processes;
        
        for p = 1:length(process_times)
            % 检查起重机容量约束
            if turbines(turbine_idx).weight > vessels(vessel_idx).crane_capacity
                process_duration = process_times(p) * 1.5; % 需要额外时间
            else
                process_duration = process_times(p) / efficiency_factor;
            end
            
            process_start = current_time;
            process_end = process_start + process_duration;
            schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, process_start, process_end, ...
                                      p, 0, 1, efficiency_factor);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + travel_time;
        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, ...
                                  -2, 0, 1, efficiency_factor);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 增强的双体船调度函数
function schedule = generateEnhancedDualSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', [], 'efficiency_factor', []);

    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);

    % 拼接阶段（并行进行）
    max_welding_time = 0;
    for v = 1:vessel_count
        welding_time = vessels(v).welding_time;
        vessel_avail_time(v) = welding_time;
        max_welding_time = max(max_welding_time, welding_time);

        schedule = addEnhancedTask(schedule, 0, v, 0, welding_time, -3, 0, 2, 1.0);
    end

    % 智能分配：优先分配大型风机给双体船
    turbine_assignments = assignTurbinesToVessels(turbines, vessels);

    for assignment_idx = 1:length(turbine_assignments)
        assignment = turbine_assignments(assignment_idx);
        turbine_idx = assignment.turbine_id;
        vessel_idx = assignment.vessel_id;

        % 选择最适合的泊位
        berth_idx = selectOptimalBerth(turbines(turbine_idx), vessels(vessel_idx), berths, berth_avail_time);

        % 计算效率因子
        efficiency_factor = calculateEfficiencyFactor(turbines(turbine_idx), vessels(vessel_idx), 2);

        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), berth_avail_time(berth_idx));
        loading_duration = vessels(vessel_idx).loading_time * (1 + (turbines(turbine_idx).weight - 150) / 400);
        loading_end = loading_start + loading_duration;

        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, ...
                                  0, berth_idx, 2, efficiency_factor);

        % 船上组装
        assembly_start = loading_end;
        assembly_duration = 35 * turbines(turbine_idx).complexity / vessels(vessel_idx).assembly_efficiency;
        assembly_end = assembly_start + assembly_duration;
        berth_avail_time(berth_idx) = assembly_end;

        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, assembly_start, assembly_end, ...
                                  5, berth_idx, 2, efficiency_factor);

        % 航行到风场
        travel_time = 80 / vessels(vessel_idx).speed;
        travel_start = assembly_end;
        travel_end = travel_start + travel_time;
        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, ...
                                  -1, 0, 2, efficiency_factor);

        % 最终安装
        final_start = travel_end;
        final_duration = 12 * turbines(turbine_idx).complexity / efficiency_factor;
        final_end = final_start + final_duration;
        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, final_start, final_end, ...
                                  6, 0, 2, efficiency_factor);

        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + travel_time;
        schedule = addEnhancedTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, ...
                                  -2, 0, 2, efficiency_factor);

        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 智能风机-船舶分配函数
function assignments = assignTurbinesToVessels(turbines, vessels)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    assignments = [];

    % 按风机重量排序（大型风机优先）
    turbine_weights = zeros(1, turbine_count);
    for i = 1:turbine_count
        turbine_weights(i) = turbines(i).weight;
    end
    [~, sorted_indices] = sort(turbine_weights, 'descend');

    vessel_loads = zeros(1, vessel_count);
    vessel_turbine_counts = zeros(1, vessel_count);

    for i = 1:turbine_count
        turbine_idx = sorted_indices(i);

        % 找到最适合的船舶
        best_vessel = 1;
        best_score = -inf;

        for v = 1:vessel_count
            % 检查容量约束
            if vessel_turbine_counts(v) >= vessels(v).capacity
                continue;
            end

            % 检查重量约束
            if turbines(turbine_idx).weight > vessels(v).crane_capacity
                continue;
            end

            % 计算适配分数
            score = calculateMatchingScore(turbines(turbine_idx), vessels(v));
            score = score - vessel_loads(v) * 0.1; % 负载均衡

            if score > best_score
                best_score = score;
                best_vessel = v;
            end
        end

        % 分配风机到最佳船舶
        assignments(end+1).turbine_id = turbine_idx;
        assignments(end).vessel_id = best_vessel;
        vessel_loads(best_vessel) = vessel_loads(best_vessel) + turbines(turbine_idx).weight;
        vessel_turbine_counts(best_vessel) = vessel_turbine_counts(best_vessel) + 1;
    end
end

%% 计算匹配分数
function score = calculateMatchingScore(turbine, vessel)
    % 基础分数
    score = 100;

    % 重量匹配
    weight_ratio = turbine.weight / vessel.crane_capacity;
    if weight_ratio > 0.8
        score = score - 20; % 接近极限
    elseif weight_ratio < 0.3
        score = score - 10; % 资源浪费
    end

    % 类型匹配（双体船更适合大型风机）
    if turbine.type == 2 && vessel.type == 2
        score = score + 30; % 大型风机 + 双体船
    elseif turbine.type == 1 && vessel.type == 1
        score = score + 20; % 小型风机 + 单体船
    end

    % 甲板空间匹配
    space_needed = turbine.weight * 2; % 估算所需空间
    if space_needed > vessel.deck_space
        score = score - 50; % 空间不足
    end
end

%% 选择最优泊位
function berth_idx = selectOptimalBerth(turbine, vessel, berths, berth_avail_time)
    berth_count = length(berths);
    best_berth = 1;
    best_score = -inf;

    for b = 1:berth_count
        % 检查起重机容量
        if turbine.weight > berths(b).crane_capacity
            continue;
        end

        % 检查组装空间
        space_needed = vessel.deck_space * 0.8;
        if space_needed > berths(b).assembly_space
            continue;
        end

        % 计算分数（优先选择可用时间最早的）
        score = 1000 - berth_avail_time(b);

        if score > best_score
            best_score = score;
            best_berth = b;
        end
    end

    berth_idx = best_berth;
end

%% 计算效率因子
function factor = calculateEfficiencyFactor(turbine, vessel, vessel_type)
    factor = 1.0;

    % 基于风机-船舶匹配
    if turbine.type == 1 && vessel_type == 1 % 小型风机 + 单体船
        factor = 1.1;
    elseif turbine.type == 2 && vessel_type == 2 % 大型风机 + 双体船
        factor = 1.3;
    elseif turbine.type == 2 && vessel_type == 1 % 大型风机 + 单体船
        factor = 0.8;
    else % 小型风机 + 双体船
        factor = 0.9;
    end

    % 基于起重机容量
    weight_ratio = turbine.weight / vessel.crane_capacity;
    if weight_ratio > 0.8
        factor = factor * 0.9; % 接近极限，效率下降
    elseif weight_ratio < 0.5
        factor = factor * 1.05; % 有余量，效率提升
    end
end

%% 添加增强任务函数
function schedule = addEnhancedTask(schedule, turbine_id, vessel_id, start_time, end_time, ...
                                   process_id, berth_id, vessel_type, efficiency_factor)
    schedule.turbine_id(end+1) = turbine_id;
    schedule.vessel_id(end+1) = vessel_id;
    schedule.start_time(end+1) = start_time;
    schedule.end_time(end+1) = end_time;
    schedule.process_id(end+1) = process_id;
    schedule.berth_id(end+1) = berth_id;
    schedule.vessel_type(end+1) = vessel_type;
    schedule.efficiency_factor(end+1) = efficiency_factor;
end

%% 增强的分析函数
function analyzeEnhancedComparison(single_schedule, dual_schedule, single_makespan, dual_makespan, ...
                                  turbines, single_vessels, dual_vessels, berths)

    fprintf('\n=== 详细性能分析 ===\n');

    % 基础对比
    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;
    fprintf('总工期对比：\n');
    fprintf('  单体船方案：%.2f 小时\n', single_makespan);
    fprintf('  双体船方案：%.2f 小时\n', dual_makespan);
    if efficiency > 0
        fprintf('  双体船效率提升：%.2f%%\n', efficiency);
    else
        fprintf('  单体船效率更高：%.2f%%\n', -efficiency);
    end

    % 风机类型分析
    small_turbines = sum([turbines.type] == 1);
    large_turbines = sum([turbines.type] == 2);
    fprintf('\n风机类型分布：\n');
    fprintf('  小型风机：%d台 (%.1f%%)\n', small_turbines, small_turbines/length(turbines)*100);
    fprintf('  大型风机：%d台 (%.1f%%)\n', large_turbines, large_turbines/length(turbines)*100);

    % 船舶利用率分析
    fprintf('\n船舶利用率分析：\n');
    analyzeVesselUtilization(single_schedule, single_vessels, single_makespan, '单体船');
    analyzeVesselUtilization(dual_schedule, dual_vessels, dual_makespan, '双体船');

    % 泊位利用率分析
    fprintf('\n泊位利用率分析：\n');
    analyzeBerthUtilization(single_schedule, dual_schedule, berths, single_makespan, dual_makespan);

    % 约束违反检查
    fprintf('\n约束检查：\n');
    checkConstraintViolations(single_schedule, single_vessels, turbines, '单体船');
    checkConstraintViolations(dual_schedule, dual_vessels, turbines, '双体船');

    % 效率因子分析
    fprintf('\n效率因子分析：\n');
    analyzeEfficiencyFactors(single_schedule, dual_schedule);
end

%% 船舶利用率分析
function analyzeVesselUtilization(schedule, vessels, makespan, vessel_type)
    vessel_count = length(vessels);
    vessel_work_time = zeros(1, vessel_count);

    for i = 1:length(schedule.vessel_id)
        if schedule.process_id(i) ~= -3 % 排除拼接时间
            vessel_id = schedule.vessel_id(i);
            duration = schedule.end_time(i) - schedule.start_time(i);
            vessel_work_time(vessel_id) = vessel_work_time(vessel_id) + duration;
        end
    end

    utilization = vessel_work_time / makespan * 100;
    avg_utilization = mean(utilization);

    fprintf('  %s平均利用率：%.2f%%\n', vessel_type, avg_utilization);
    fprintf('  %s利用率范围：%.2f%% - %.2f%%\n', vessel_type, min(utilization), max(utilization));
end

%% 泊位利用率分析
function analyzeBerthUtilization(single_schedule, dual_schedule, berths, single_makespan, dual_makespan)
    berth_count = length(berths);

    % 单体船泊位利用
    single_berth_time = zeros(1, berth_count);
    for i = 1:length(single_schedule.berth_id)
        berth_id = single_schedule.berth_id(i);
        if berth_id > 0
            duration = single_schedule.end_time(i) - single_schedule.start_time(i);
            single_berth_time(berth_id) = single_berth_time(berth_id) + duration;
        end
    end

    % 双体船泊位利用
    dual_berth_time = zeros(1, berth_count);
    for i = 1:length(dual_schedule.berth_id)
        berth_id = dual_schedule.berth_id(i);
        if berth_id > 0
            duration = dual_schedule.end_time(i) - dual_schedule.start_time(i);
            dual_berth_time(berth_id) = dual_berth_time(berth_id) + duration;
        end
    end

    single_util = single_berth_time / single_makespan * 100;
    dual_util = dual_berth_time / dual_makespan * 100;

    fprintf('  单体船泊位平均利用率：%.2f%%\n', mean(single_util));
    fprintf('  双体船泊位平均利用率：%.2f%%\n', mean(dual_util));
end

%% 约束违反检查
function checkConstraintViolations(schedule, vessels, turbines, vessel_type)
    violations = 0;

    % 检查容量约束
    for v = 1:length(vessels)
        vessel_turbines = unique(schedule.turbine_id(schedule.vessel_id == v & schedule.turbine_id > 0));
        if length(vessel_turbines) > vessels(v).capacity
            violations = violations + 1;
            fprintf('    警告：%s%d超载 (%d > %d)\n', vessel_type, v, length(vessel_turbines), vessels(v).capacity);
        end
    end

    % 检查重量约束
    for i = 1:length(schedule.turbine_id)
        if schedule.turbine_id(i) > 0
            turbine_id = schedule.turbine_id(i);
            vessel_id = schedule.vessel_id(i);
            if turbines(turbine_id).weight > vessels(vessel_id).crane_capacity
                violations = violations + 1;
                fprintf('    警告：%s%d起重机容量不足 (%.1f > %.1f吨)\n', ...
                    vessel_type, vessel_id, turbines(turbine_id).weight, vessels(vessel_id).crane_capacity);
            end
        end
    end

    if violations == 0
        fprintf('  %s方案：无约束违反\n', vessel_type);
    else
        fprintf('  %s方案：发现%d个约束违反\n', vessel_type, violations);
    end
end

%% 效率因子分析
function analyzeEfficiencyFactors(single_schedule, dual_schedule)
    if isfield(single_schedule, 'efficiency_factor')
        single_avg_eff = mean(single_schedule.efficiency_factor(single_schedule.efficiency_factor > 0));
        fprintf('  单体船平均效率因子：%.3f\n', single_avg_eff);
    end

    if isfield(dual_schedule, 'efficiency_factor')
        dual_avg_eff = mean(dual_schedule.efficiency_factor(dual_schedule.efficiency_factor > 0));
        fprintf('  双体船平均效率因子：%.3f\n', dual_avg_eff);
    end
end

%% 增强的甘特图绘制函数
function plotEnhancedComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                               single_makespan, dual_makespan, turbines, berths)

    figure('Position', [50, 50, 1800, 1200], 'Color', 'white');

    % 颜色方案
    colors = [
        0.8500, 0.3250, 0.0980;  % 装载
        0.0000, 0.4470, 0.7410;  % 前往风场
        0.4660, 0.6740, 0.1880;  % 基础安装
        0.9290, 0.6940, 0.1250;  % 塔筒安装
        0.4940, 0.1840, 0.5560;  % 机舱安装
        0.6350, 0.0780, 0.1840;  % 叶片安装
        0.3010, 0.7450, 0.9330;  % 船上组装
        0.8500, 0.3250, 0.0980;  % 最终安装
        0.2500, 0.2500, 0.2500;  % 返回港口
        0.7500, 0.7500, 0.7500;  % 拼接焊接
    ];

    %% 子图1：单体船甘特图
    subplot(3, 2, 1);
    hold on;

    vessel_count = length(single_vessels);

    for i = 1:length(single_schedule.turbine_id)
        vessel_id = single_schedule.vessel_id(i);
        start_time = single_schedule.start_time(i);
        end_time = single_schedule.end_time(i);
        duration = end_time - start_time;
        process_id = single_schedule.process_id(i);
        berth_id = single_schedule.berth_id(i);
        turbine_id = single_schedule.turbine_id(i);

        color_idx = getColorIndex(process_id);

        % 根据风机类型调整边框
        if turbine_id > 0
            if turbines(turbine_id).type == 1
                edge_color = [0.2, 0.6, 0.9]; % 小型风机蓝色边框
                edge_width = 1.0;
            else
                edge_color = [0.9, 0.2, 0.2]; % 大型风机红色边框
                edge_width = 1.5;
            end
        else
            edge_color = 'k';
            edge_width = 0.8;
        end

        % 根据泊位使用调整样式
        if berth_id > 0
            line_style = '-';
        else
            line_style = '--';
        end

        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', colors(color_idx,:), 'EdgeColor', edge_color, ...
            'LineWidth', edge_width, 'LineStyle', line_style);

        % 添加标签
        if duration > 2
            if turbine_id > 0
                turbine_type_str = '';
                if turbines(turbine_id).type == 1
                    turbine_type_str = 'S';
                else
                    turbine_type_str = 'L';
                end

                if berth_id > 0
                    label_text = sprintf('T%d%s-B%d', turbine_id, turbine_type_str, berth_id);
                else
                    if process_id >= 1 && process_id <= 4
                        process_names = {'基础', '塔筒', '机舱', '叶片'};
                        label_text = sprintf('T%d%s-%s', turbine_id, turbine_type_str, process_names{process_id});
                    else
                        label_text = sprintf('T%d%s', turbine_id, turbine_type_str);
                    end
                end
            else
                if process_id == -1
                    label_text = '→风场';
                elseif process_id == -2
                    label_text = '←港口';
                else
                    label_text = '';
                end
            end

            if ~isempty(label_text)
                text(start_time + duration/2, vessel_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加船舶标签和容量信息
    for v = 1:vessel_count
        text(-max(single_makespan, dual_makespan)*0.02, v, ...
            sprintf('船%d(容量%d)', v, single_vessels(v).capacity), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title(sprintf('单体船方案 - %d艘船舶 (总工期: %.2f小时)', vessel_count, single_makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('船舶编号', 'FontSize', 10);
    ylim([0, vessel_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图2：单体船泊位利用
    subplot(3, 2, 2);
    hold on;

    berth_count = length(berths);

    for i = 1:length(single_schedule.turbine_id)
        berth_id = single_schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            start_time = single_schedule.start_time(i);
            end_time = single_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = single_schedule.process_id(i);
            turbine_id = single_schedule.turbine_id(i);
            vessel_id = single_schedule.vessel_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, berth_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 1.0);

            if turbine_id > 0 && duration > 3
                turbine_type_str = '';
                if turbines(turbine_id).type == 1
                    turbine_type_str = 'S';
                else
                    turbine_type_str = 'L';
                end

                label_text = sprintf('V%d-T%d%s', vessel_id, turbine_id, turbine_type_str);
                text(start_time + duration/2, berth_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加泊位标签和容量信息
    for b = 1:berth_count
        text(-max(single_makespan, dual_makespan)*0.02, b, ...
            sprintf('泊位%d(容量%d)', b, berths(b).capacity), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title('单体船方案 - 泊位利用情况', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('泊位编号', 'FontSize', 10);
    ylim([0, berth_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图3：双体船甘特图
    subplot(3, 2, 3);
    hold on;

    dual_vessel_count = length(dual_vessels);

    for i = 1:length(dual_schedule.turbine_id)
        vessel_id = dual_schedule.vessel_id(i);
        start_time = dual_schedule.start_time(i);
        end_time = dual_schedule.end_time(i);
        duration = end_time - start_time;
        process_id = dual_schedule.process_id(i);
        berth_id = dual_schedule.berth_id(i);
        turbine_id = dual_schedule.turbine_id(i);

        color_idx = getColorIndex(process_id);

        % 根据风机类型调整边框
        if turbine_id > 0
            if turbines(turbine_id).type == 1
                edge_color = [0.2, 0.6, 0.9]; % 小型风机蓝色边框
                edge_width = 1.0;
            else
                edge_color = [0.9, 0.2, 0.2]; % 大型风机红色边框
                edge_width = 1.5;
            end
        else
            edge_color = 'k';
            edge_width = 0.8;
        end

        % 根据泊位使用调整样式
        if berth_id > 0
            line_style = '-';
        else
            line_style = '--';
        end

        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', colors(color_idx,:), 'EdgeColor', edge_color, ...
            'LineWidth', edge_width, 'LineStyle', line_style);

        % 添加标签
        if duration > 1.5
            if turbine_id > 0
                turbine_type_str = '';
                if turbines(turbine_id).type == 1
                    turbine_type_str = 'S';
                else
                    turbine_type_str = 'L';
                end

                if berth_id > 0
                    if process_id == 0
                        label_text = sprintf('T%d%s装载', turbine_id, turbine_type_str);
                    elseif process_id == 5
                        label_text = sprintf('T%d%s组装', turbine_id, turbine_type_str);
                    else
                        label_text = sprintf('T%d%s-B%d', turbine_id, turbine_type_str, berth_id);
                    end
                else
                    if process_id == 6
                        label_text = sprintf('T%d%s最终', turbine_id, turbine_type_str);
                    else
                        label_text = sprintf('T%d%s', turbine_id, turbine_type_str);
                    end
                end
            elseif process_id == -3
                label_text = '拼接';
            elseif process_id == -1
                label_text = '→风场';
            elseif process_id == -2
                label_text = '←港口';
            else
                label_text = '';
            end

            if ~isempty(label_text)
                text_color = 'white';
                if process_id == -3
                    text_color = 'black';
                end
                text(start_time + duration/2, vessel_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', text_color);
            end
        end
    end

    % 添加船舶标签和容量信息
    for v = 1:dual_vessel_count
        text(-max(single_makespan, dual_makespan)*0.02, v, ...
            sprintf('双船%d(容量%d)', v, dual_vessels(v).capacity), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title(sprintf('双体船方案 - %d艘船舶 (总工期: %.2f小时)', dual_vessel_count, dual_makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('船舶编号', 'FontSize', 10);
    ylim([0, dual_vessel_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图4：双体船泊位利用
    subplot(3, 2, 4);
    hold on;

    for i = 1:length(dual_schedule.turbine_id)
        berth_id = dual_schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            start_time = dual_schedule.start_time(i);
            end_time = dual_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = dual_schedule.process_id(i);
            turbine_id = dual_schedule.turbine_id(i);
            vessel_id = dual_schedule.vessel_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, berth_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 1.0);

            if turbine_id > 0 && duration > 2
                turbine_type_str = '';
                if turbines(turbine_id).type == 1
                    turbine_type_str = 'S';
                else
                    turbine_type_str = 'L';
                end

                if process_id == 0
                    label_text = sprintf('V%d-T%d%s装载', vessel_id, turbine_id, turbine_type_str);
                elseif process_id == 5
                    label_text = sprintf('V%d-T%d%s组装', vessel_id, turbine_id, turbine_type_str);
                else
                    label_text = sprintf('V%d-T%d%s', vessel_id, turbine_id, turbine_type_str);
                end

                text(start_time + duration/2, berth_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加泊位标签和容量信息
    for b = 1:berth_count
        text(-max(single_makespan, dual_makespan)*0.02, b, ...
            sprintf('泊位%d(容量%d)', b, berths(b).capacity), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title('双体船方案 - 泊位利用情况', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('泊位编号', 'FontSize', 10);
    ylim([0, berth_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图5-6：资源利用率对比
    subplot(3, 2, [5, 6]);

    % 计算并显示资源利用率对比
    plotResourceUtilization(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                           berths, single_makespan, dual_makespan);

    %% 图例
    legend_handles = [];
    legend_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', ...
                   '叶片安装', '船上组装', '最终安装', '返回港口', '拼接焊接'};

    for i = 1:length(legend_names)
        legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 8, 'Color', colors(i,:));
    end

    % 添加边框样式图例
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', [0.2, 0.6, 0.9]);
    legend_names{end+1} = '小型风机';
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', [0.9, 0.2, 0.2]);
    legend_names{end+1} = '大型风机';
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '-');
    legend_names{end+1} = '使用泊位';
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '--');
    legend_names{end+1} = '海上作业';

    legend(legend_handles, legend_names, 'Location', 'southoutside', ...
        'Orientation', 'horizontal', 'FontSize', 8, 'NumColumns', 7);

    % 总标题
    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;
    if efficiency > 0
        sgtitle(sprintf('增强版对比分析 - 双体船效率提升%.2f%% (包含多重约束)', efficiency), ...
            'FontSize', 16, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
    else
        sgtitle(sprintf('增强版对比分析 - 单体船效率更高%.2f%% (包含多重约束)', -efficiency), ...
            'FontSize', 16, 'FontWeight', 'bold', 'Color', [0.8, 0, 0]);
    end

    hold off;
end

%% 资源利用率对比图
function plotResourceUtilization(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                                 berths, single_makespan, dual_makespan)

    % 计算船舶利用率
    single_vessel_util = calculateVesselUtilization(single_schedule, single_vessels, single_makespan);
    dual_vessel_util = calculateVesselUtilization(dual_schedule, dual_vessels, dual_makespan);

    % 计算泊位利用率
    single_berth_util = calculateBerthUtilization(single_schedule, berths, single_makespan);
    dual_berth_util = calculateBerthUtilization(dual_schedule, berths, dual_makespan);

    % 绘制对比图
    x_pos = [1:length(single_vessels), length(single_vessels)+2:length(single_vessels)+1+length(dual_vessels)];
    all_vessel_util = [single_vessel_util, dual_vessel_util];

    bar_colors = [repmat([0.2, 0.6, 0.8], length(single_vessels), 1); ...
                  repmat([0.8, 0.4, 0.2], length(dual_vessels), 1)];

    bar_handle = bar(x_pos, all_vessel_util, 'FaceColor', 'flat');
    bar_handle.CData = bar_colors;

    hold on;

    % 添加泊位利用率（右侧）
    berth_x_pos = max(x_pos) + 2 + (1:length(berths));
    bar(berth_x_pos, single_berth_util, 0.3, 'FaceColor', [0.6, 0.8, 0.6], 'DisplayName', '单体船泊位');
    bar(berth_x_pos + 0.3, dual_berth_util, 0.3, 'FaceColor', [0.8, 0.6, 0.4], 'DisplayName', '双体船泊位');

    % 添加标签
    xlabel('资源编号', 'FontSize', 10);
    ylabel('利用率 (%)', 'FontSize', 10);
    title('船舶和泊位利用率对比', 'FontSize', 12, 'FontWeight', 'bold');

    % 添加分组标签
    text(mean(1:length(single_vessels)), -10, '单体船', 'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    text(mean(length(single_vessels)+2:length(single_vessels)+1+length(dual_vessels)), -10, '双体船', 'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    text(mean(berth_x_pos) + 0.15, -10, '泊位', 'HorizontalAlignment', 'center', 'FontWeight', 'bold');

    grid on;
    ylim([0, 120]);

    % 添加平均利用率信息
    text(max(x_pos)*0.7, 100, sprintf('船舶平均利用率:\n单体船: %.1f%%\n双体船: %.1f%%', ...
        mean(single_vessel_util), mean(dual_vessel_util)), ...
        'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black');
end

%% 计算船舶利用率
function utilization = calculateVesselUtilization(schedule, vessels, makespan)
    vessel_count = length(vessels);
    vessel_work_time = zeros(1, vessel_count);

    for i = 1:length(schedule.vessel_id)
        if schedule.process_id(i) ~= -3 % 排除拼接时间
            vessel_id = schedule.vessel_id(i);
            duration = schedule.end_time(i) - schedule.start_time(i);
            vessel_work_time(vessel_id) = vessel_work_time(vessel_id) + duration;
        end
    end

    utilization = vessel_work_time / makespan * 100;
end

%% 计算泊位利用率
function utilization = calculateBerthUtilization(schedule, berths, makespan)
    berth_count = length(berths);
    berth_work_time = zeros(1, berth_count);

    for i = 1:length(schedule.berth_id)
        berth_id = schedule.berth_id(i);
        if berth_id > 0
            duration = schedule.end_time(i) - schedule.start_time(i);
            berth_work_time(berth_id) = berth_work_time(berth_id) + duration;
        end
    end

    utilization = berth_work_time / makespan * 100;
end

%% 颜色索引函数
function color_idx = getColorIndex(process_id)
    switch process_id
        case 0, color_idx = 1;   % 装载
        case -1, color_idx = 2;  % 前往风场
        case 1, color_idx = 3;   % 基础安装
        case 2, color_idx = 4;   % 塔筒安装
        case 3, color_idx = 5;   % 机舱安装
        case 4, color_idx = 6;   % 叶片安装
        case 5, color_idx = 7;   % 船上组装
        case 6, color_idx = 8;   % 最终安装
        case -2, color_idx = 9;  % 返回港口
        case -3, color_idx = 10; % 拼接焊接
        otherwise, color_idx = 1;
    end
end
