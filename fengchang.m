%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with intelligent turbine-vessel matching.
% Enhanced with dual-hull vessel method and sea condition considerations.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count

% 风机类型参数
TURBINE_TYPES = [1, 2]; % 1=小型风机, 2=大型风机
SMALL_TURBINE_POWER = 3.5; % MW
LARGE_TURBINE_POWER = 8.0; % MW
SMALL_TURBINE_RATIO = 0.6; % 小型风机占比60%

% 为不同类型风机定义不同的工序时间
% Process times: [foundation installation, tower installation, nacelle installation, blade installation]
SMALL_TURBINE_PROCESS_TIMES = [18, 10, 8, 30]; % 小型风机工序时间更短
LARGE_TURBINE_PROCESS_TIMES = [25, 15, 12, 45]; % 大型风机工序时间更长

% Ship assembly times for different assembly levels
SHIP_ASSEMBLY_TIMES = struct('partial', 25, 'complete', 40); % Hours for ship assembly
% Final installation time for completely assembled turbines
FINAL_INSTALLATION_TIME = 15; % Hours for final installation of completely assembled turbines
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE FINAL_INSTALLATION_TIME; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 50;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.19;  % PSO mutation rate
INERTIA_WEIGHT = 0.78;     % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention



%% Data Structures
% Define turbine installation tasks with enhanced properties including turbine types
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'type', cell(1, TURBINE_COUNT), ... % 新增：风机类型 (1=小型, 2=大型)
                 'power', cell(1, TURBINE_COUNT), ... % 新增：风机功率
                 'processes', cell(1, TURBINE_COUNT), ...
                 'assembly_level', cell(1, TURBINE_COUNT), ... % 0=none, 1=partial, 2=complete
                 'ship_assembly_time', cell(1, TURBINE_COUNT), ...
                 'preassembly_time', cell(1, TURBINE_COUNT), ...
                 'is_preassembled', cell(1, TURBINE_COUNT));

% Initialize turbine properties
for i = 1:TURBINE_COUNT
    % 随机分配风机类型，按照设定比例
    if rand() < SMALL_TURBINE_RATIO
        turbines(i).type = 1; % 小型风机
        turbines(i).power = SMALL_TURBINE_POWER;
        base_process_times = SMALL_TURBINE_PROCESS_TIMES;
    else
        turbines(i).type = 2; % 大型风机
        turbines(i).power = LARGE_TURBINE_POWER;
        base_process_times = LARGE_TURBINE_PROCESS_TIMES;
    end

    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(base_process_times));
    turbines(i).processes = base_process_times .* variation;

    % Initialize assembly level to 0 (no assembly)
    % This will be set based on vessel assignment later
    turbines(i).assembly_level = 0;

    % Initialize ship assembly times for different levels
    % These will be used if the turbine is assigned to a vessel that can do assembly
    turbines(i).ship_assembly_time = struct(...
        'partial', SHIP_ASSEMBLY_TIMES.partial * (0.9 + 0.2*rand()), ...
        'complete', SHIP_ASSEMBLY_TIMES.complete * (0.9 + 0.2*rand()));

    % Initialize preassembly time (default to 0, will be set during scheduling)
    turbines(i).preassembly_time = 0;

    % Initialize is_preassembled flag (default to false)
    turbines(i).is_preassembled = false;
end

% Define vessels with type (single-hull or dual-hull)
% Randomly assign vessel types: 1 for single-hull, 2 for dual-hull
VESSEL_TYPES = randi([1, 2], 1, VESSEL_COUNT);
% Ensure we have at least 3 of each type
if sum(VESSEL_TYPES == 1) < 3
    idx = find(VESSEL_TYPES == 2);
    VESSEL_TYPES(idx(1:3)) = 1;
elseif sum(VESSEL_TYPES == 2) < 3
    idx = find(VESSEL_TYPES == 1);
    VESSEL_TYPES(idx(1:3)) = 2;
end

% Define vessel properties based on type
MAX_ASSEMBLY_LEVEL = zeros(1, VESSEL_COUNT);

for i = 1:VESSEL_COUNT
    if VESSEL_TYPES(i) == 1 % Single-hull vessel
        MAX_ASSEMBLY_LEVEL(i) = 0; % Cannot do assembly
    else % Dual-hull vessel
        MAX_ASSEMBLY_LEVEL(i) = 2; % Can do complete assembly
    end
end

% Define vessels with simplified properties (no sea condition factors)
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'type', num2cell(VESSEL_TYPES), ... % 1: single-hull, 2: dual-hull
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES), ...
                'max_assembly_level', num2cell(MAX_ASSEMBLY_LEVEL)); % Maximum possible assembly level

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Turbine Installation Scheduling
fprintf('生成海上风机安装调度计划...\n');

% 显示风机类型统计
small_turbine_count = sum([turbines.type] == 1);
large_turbine_count = sum([turbines.type] == 2);
fprintf('风机配置: %d台小型风机(%.1fMW), %d台大型风机(%.1fMW)\n', ...
    small_turbine_count, SMALL_TURBINE_POWER, large_turbine_count, LARGE_TURBINE_POWER);

% 显示船舶类型统计
single_hull_count = sum([vessels.type] == 1);
dual_hull_count = sum([vessels.type] == 2);
fprintf('船舶配置: %d艘单体船, %d艘双体船\n', single_hull_count, dual_hull_count);

% 生成优化调度计划
schedule = hybridGAPSO(turbines, vessels, berths);
makespan = calculateEnhancedMakespan(schedule, turbines, vessels);
fprintf('优化调度工期: %.2f 小时\n', makespan);

%% Plot Results
% Plot Gantt chart
plotGanttChart(schedule, '海上风机安装调度计划', turbines, vessels, []);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;

    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end

    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);

    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);

    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end

    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);

    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end

    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);

    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate enhanced makespan with turbine-vessel matching
            fitness(i) = calculateEnhancedMakespan(schedule_i, turbines, vessels);

            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end

        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end

        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;

        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end

        % Check diversity
        diversity = calculateDiversity(fitness);

        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);

            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);

                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);

                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);

                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));

                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end

                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;

                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateEnhancedMakespan(schedule_i, turbines, vessels);

                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end

                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);

            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};

            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end

                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end

                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end

                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end

                new_population{i} = child;
            end

            population = new_population;
        end

        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end

    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);

    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE
    global PORT_TO_FARM_DISTANCE FINAL_INSTALLATION_TIME;

    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;

    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);

    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'assembly_level', []);

    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end

    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);

        % Get vessel properties
        vessel_type = vessels(vessel_idx).type; % 1=single-hull, 2=dual-hull
        vessel_max_assembly = vessels(vessel_idx).max_assembly_level;

        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);

        % 根据风机类型和船舶类型智能确定组装级别
        turbine_type = turbines(turbine_idx).type; % 1=小型, 2=大型
        vessel_type = vessels(vessel_idx).type;    % 1=单体船, 2=双体船

        [assembly_level, efficiency_factor] = determineOptimalAssembly(turbine_type, vessel_type);

        % 存储组装级别
        turbines(turbine_idx).assembly_level = assembly_level;

        % Handle berth assignment for loading
        [earliest_berth_time, berth_idx] = min(berth_avail_time);

        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;

        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;

        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        schedule.assembly_level(end+1) = assembly_level;

        % 添加船上组装过程（仅对双体船）
        if assembly_level == 2
            % 获取完全组装时间
            ship_assembly_time = turbines(turbine_idx).ship_assembly_time.complete;

            ship_assembly_start = loading_end;
            ship_assembly_end = ship_assembly_start + ship_assembly_time;

            % 添加船上组装到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = ship_assembly_start;
            schedule.end_time(end+1) = ship_assembly_end;
            schedule.process_id(end+1) = 5; % 5表示船上组装
            schedule.berth_id(end+1) = berth_idx; % 组装在泊位进行
            schedule.assembly_level(end+1) = assembly_level;

            % 更新装载结束时间
            loading_end = ship_assembly_end;
        end

        % Travel to farm
        travel_start = loading_end;
        travel_time = port_to_farm_time(vessel_idx);
        travel_end = travel_start + travel_time;

        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        schedule.assembly_level(end+1) = assembly_level;

        % 处理安装任务
        current_time = travel_end;

        if assembly_level == 0 % 无组装
            % 根据匹配效率调整工序时间
            adjusted_process_times = process_times / efficiency_factor;

            % 执行所有常规工序
            for p = 1:num_processes
                process_start = current_time;
                process_end = process_start + adjusted_process_times(p);

                % 添加工序到调度
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = process_start;
                schedule.end_time(end+1) = process_end;
                schedule.process_id(end+1) = p;
                schedule.berth_id(end+1) = 0;  % 0表示无泊位
                schedule.assembly_level(end+1) = assembly_level;

                current_time = process_end;
            end
        else % 完全组装
            % 对于完全组装，我们用一个最终安装工序替代所有常规工序
            final_installation_time = FINAL_INSTALLATION_TIME * (0.9 + 0.2*rand());

            % 应用匹配效率
            final_installation_time = final_installation_time / efficiency_factor;

            process_start = current_time;
            process_end = process_start + final_installation_time;

            % 添加最终安装工序到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = 6; % 6表示最终安装（完全组装）
            schedule.berth_id(end+1) = 0;  % 0表示无泊位
            schedule.assembly_level(end+1) = assembly_level;

            current_time = process_end;
        end

        % Travel back to port
        travel_back_start = current_time;
        travel_back_time = port_to_farm_time(vessel_idx);
        travel_back_end = travel_back_start + travel_back_time;

        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        schedule.assembly_level(end+1) = assembly_level;

        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

%% 智能匹配函数：根据风机类型和船舶类型确定最优组装级别和效率因子
function [assembly_level, efficiency_factor] = determineOptimalAssembly(turbine_type, vessel_type)
    if turbine_type == 1 % 小型风机
        if vessel_type == 1 % 单体船
            assembly_level = 0; % 无组装，效率最优
            efficiency_factor = 1.0; % 基准效率
        else % 双体船
            assembly_level = 0; % 对小型风机，双体船也不组装
            efficiency_factor = 0.9; % 双体船对小型风机效率略低
        end
    else % 大型风机
        if vessel_type == 1 % 单体船
            assembly_level = 0; % 无组装
            efficiency_factor = 0.8; % 单体船对大型风机效率较低
        else % 双体船
            assembly_level = 2; % 完全组装，效率最优
            efficiency_factor = 1.2; % 双体船对大型风机效率提升20%
        end
    end
end

%% 增强的适应度函数：考虑风机-船舶匹配效率
function fitness = calculateEnhancedMakespan(schedule, turbines, vessels)
    % 基础工期
    base_makespan = max(schedule.end_time);

    % 计算匹配效率惩罚
    mismatch_penalty = 0;
    total_tasks = 0;

    for i = 1:length(schedule.turbine_id)
        if schedule.process_id(i) > 0 && schedule.process_id(i) <= 6 % 安装工序
            turbine_id = schedule.turbine_id(i);
            vessel_id = schedule.vessel_id(i);

            if turbine_id > 0 && turbine_id <= length(turbines)
                turbine_type = turbines(turbine_id).type;
                vessel_type = vessels(vessel_id).type;

                total_tasks = total_tasks + 1;

                % 计算匹配度惩罚
                if turbine_type == 1 && vessel_type == 2 % 小型风机用双体船
                    mismatch_penalty = mismatch_penalty + 2; % 轻微惩罚
                elseif turbine_type == 2 && vessel_type == 1 % 大型风机用单体船
                    mismatch_penalty = mismatch_penalty + 8; % 较大惩罚
                end
            end
        end
    end

    % 归一化惩罚
    if total_tasks > 0
        normalized_penalty = (mismatch_penalty / total_tasks) * base_makespan * 0.1;
    else
        normalized_penalty = 0;
    end

    fitness = base_makespan + normalized_penalty;
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);

    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));

    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);

    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));

    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));

    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);

    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;

    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % Create a professional-looking Gantt chart for wind turbine installation scheduling

    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    if isempty(schedule) || isempty(schedule.turbine_id)
        text(0.5, 0.5, '没有调度数据可显示', 'HorizontalAlignment', 'center', 'FontSize', 14);
        title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
        return;
    end

    % Check if the schedule is empty
    if isempty(schedule) || isempty(schedule.turbine_id)
        text(0.5, 0.5, '没有调度数据可显示', 'HorizontalAlignment', 'center', 'FontSize', 14);
        title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
        return;
    end

    % 使用柔和的颜色调色板
    process_colors = [
        0.9290, 0.6940, 0.3250;  % 装载 (process 0) - 柔和橙色
        0.4660, 0.7740, 0.8880;  % 前往风场 (process -1) - 柔和蓝色
        0.6350, 0.5040, 0.7410;  % 基础安装 (process 1) - 柔和紫色
        0.4660, 0.7410, 0.3880;  % 塔筒安装 (process 2) - 柔和绿色
        0.8500, 0.3250, 0.3980;  % 机舱安装 (process 3) - 柔和红色
        0.9290, 0.6940, 0.1250;  % 叶片安装 (process 4) - 柔和黄色
        0.4660, 0.7740, 0.8880;  % 返回港口 (process -2) - 柔和蓝色
        1.0000, 0.3000, 0.3000;  % 维修 (process -3) - 醒目但不太亮的红色
        0.3010, 0.7450, 0.5330;  % 船上组装 (process 5) - 青色
        0.4940, 0.1840, 0.5560;  % 最终安装 (process 6) - 紫色
    ];

    % 工序名称（用于图例）
    process_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', '叶片安装', '返回港口', '维修', '船上组装', '最终安装'};

    % Create dummy objects for the legend
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end

    % Create dummy objects for vessel type legend
    vessel_type_handles = zeros(1, 2);
    vessel_type_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '-');
    vessel_type_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '--');
    vessel_type_names = {'单体船', '双体船'};

    % Create dummy objects for turbine type legend
    turbine_type_handles = zeros(1, 2);
    turbine_type_handles(1) = plot(NaN, NaN, 'LineWidth', 4, 'Color', [0.2, 0.6, 0.9], 'LineStyle', '-');
    turbine_type_handles(2) = plot(NaN, NaN, 'LineWidth', 4, 'Color', [0.9, 0.2, 0.2], 'LineStyle', '-');
    turbine_type_names = {'小型风机(3.5MW)', '大型风机(8.0MW)'};

    % Draw vessel timelines
    hold on;

    % If there is a disruption, add a disruption marker line
    if ~isempty(disruption) && isstruct(disruption) && isfield(disruption, 'time')
        % Draw a vertical line at the time of disruption
        line([disruption.time, disruption.time], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);

        % Add disruption marker text
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end

    % Pre-identify repair tasks
    repair_indices = [];
    affected_vessel = -1;

    if ~isempty(disruption) && isstruct(disruption)
        % Try to find repair tasks based on turbine_id = 0 (no specific turbine)
        repair_by_turbine = find(schedule.turbine_id == 0);

        % Try to find repair tasks based on process_id = -3
        repair_by_process = find(schedule.process_id == -3);

        % Combine the results, prioritizing process_id = -3
        if ~isempty(repair_by_process)
            repair_indices = repair_by_process;
        elseif ~isempty(repair_by_turbine)
            repair_indices = repair_by_turbine;
        end

        % If we have a disruption structure with affected_vessel field
        if isfield(disruption, 'affected_vessel') && ~isempty(disruption.affected_vessel)
            affected_vessel = disruption.affected_vessel;

            % If we still don't have repair tasks, try to find them by vessel and time
            if isempty(repair_indices) && affected_vessel > 0
                % Find tasks for the affected vessel near the disruption time
                potential_repair = find(schedule.vessel_id == affected_vessel & ...
                                       abs(schedule.start_time - disruption.time) < 5);
                if ~isempty(potential_repair)
                    repair_indices = [repair_indices, potential_repair];
                    repair_indices = unique(repair_indices); % Remove duplicates
                end
            end
        end
    end

    % Process tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        if isempty(vessel_tasks)
            continue;  % Skip if no tasks for this vessel
        end

        y_pos = v;

        % Sort tasks by start time to ensure proper sequence
        [~, sorted_idx] = sort(schedule.start_time(vessel_tasks));
        vessel_tasks = vessel_tasks(sorted_idx);

        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;

            % Skip zero-duration tasks
            if duration <= 0
                continue;
            end

            % Determine if this is a repair task using multiple criteria
            is_repair = false;

            % Check if this task index is in our pre-identified repair indices
            if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                is_repair = true;
            end

            % Check if process_id indicates repair
            if schedule.process_id(task_idx) == -3
                is_repair = true;
            end

            % Check if this is a zero turbine_id task for affected vessel
            if schedule.turbine_id(task_idx) == 0 && v == affected_vessel
                is_repair = true;
            end

            % Check if this task starts at or very near the disruption time on the affected vessel
            if ~isempty(disruption) && isfield(disruption, 'time') && ...
               v == affected_vessel && abs(start_time - disruption.time) < 2
                is_repair = true;
            end

            % Set color and label based on task type
            if is_repair
                % Force this task to be marked as repair
                color_idx = 8;  % Repair color
                label_text = '维修';
            else
                % Determine regular process type
                process_id = schedule.process_id(task_idx);

                % Map process_id to color index
                if process_id == 0      % Loading
                    color_idx = 1;
                    process_label = 'L';
                elseif process_id == -1  % Travel to farm
                    color_idx = 2;
                    process_label = 'TF';
                elseif process_id == -2  % Return to port
                    color_idx = 7;
                    process_label = 'TB';
                elseif process_id == 5   % 船上组装
                    color_idx = 9;      % 使用船上组装颜色
                    process_label = 'SA';
                elseif process_id == 6   % 最终安装（完全组装）
                    color_idx = 10;     % 使用最终安装颜色
                    process_label = 'FI';
                elseif process_id > 0 && process_id <= 4  % Regular process
                    color_idx = min(process_id + 2, size(process_colors, 1)); % Prevent index out of bounds
                    process_label = ['P', num2str(process_id)];
                else                    % Handle unexpected cases
                    color_idx = 1;      % Default to loading color
                    process_label = '?';
                end

                % Create label with enhanced turbine type information
                if schedule.turbine_id(task_idx) > 0
                    turbine_type_str = '';
                    turbine_id = schedule.turbine_id(task_idx);
                    if isfield(turbines, 'type') && turbine_id <= length(turbines)
                        if turbines(turbine_id).type == 1
                            turbine_type_str = '[S]'; % Small - 用方括号突出显示
                        else
                            turbine_type_str = '[L]'; % Large - 用方括号突出显示
                        end
                    end
                    label_text = sprintf('T%d%s-%s', turbine_id, turbine_type_str, process_label);
                else
                    % If turbine_id is 0 but not repair, just show process label
                    label_text = process_label;
                end
            end

            % 根据风机类型确定边框颜色和样式
            turbine_id = schedule.turbine_id(task_idx);
            edge_color = 'k'; % 默认黑色
            edge_width = 0.5; % 默认线宽

            if turbine_id > 0 && turbine_id <= length(turbines) && isfield(turbines, 'type')
                if turbines(turbine_id).type == 1 % 小型风机
                    edge_color = [0.2, 0.6, 0.9]; % 蓝色边框
                    edge_width = 1.5; % 较粗边框
                else % 大型风机
                    edge_color = [0.9, 0.2, 0.2]; % 红色边框
                    edge_width = 2.0; % 更粗边框
                end
            end

            % 绘制任务条
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', edge_color, 'LineWidth', edge_width);

            % Add sea condition indicator if available
            if isfield(schedule, 'sea_condition') && length(schedule.sea_condition) >= task_idx
                sea_impact = schedule.sea_condition(task_idx);
                if sea_impact > 0 % Only show for tasks with sea impact
                    % Determine sea condition color (green for good, yellow for medium, red for bad)
                    if sea_impact < 0.33
                        sea_color = [0, 0.7, 0]; % Green
                    elseif sea_impact < 0.66
                        sea_color = [0.9, 0.6, 0]; % Yellow/Orange
                    else
                        sea_color = [0.8, 0, 0]; % Red
                    end

                    % Draw a small line at the top of the task bar to indicate sea condition
                    line([start_time, start_time + duration], [y_pos+0.4, y_pos+0.4], ...
                        'Color', sea_color, 'LineWidth', 2);
                end
            end

            % 使用统一的较小字体大小
            font_size = 7;

            % For very short durations, use rotated or external labels
            if duration < 5
                % External label with line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw small line connecting to block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end

        % Add vessel label on y-axis with vessel type
        % If this is the affected vessel, add marker
        vessel_type_str = '';
        vessel_deck_str = '';
        if isfield(vessels, 'type')
            if vessels(v).type == 1
                vessel_type_str = '单体船';
            else
                vessel_type_str = '双体船';
            end

            % Add deck space info
            if isfield(vessels, 'deck_space')
                vessel_deck_str = sprintf('甲板%.1f', vessels(v).deck_space);
            end
        end

        if v == affected_vessel
            text(-20, y_pos, sprintf('船舶 %d (%s, %s, 故障)', v, vessel_type_str, vessel_deck_str), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r', 'FontSize', 8);
        else
            text(-20, y_pos, sprintf('船舶 %d (%s, %s)', v, vessel_type_str, vessel_deck_str), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 8);
        end
    end

    % Add berth timelines - ensure no repair tasks are displayed
    berth_count = max(max(schedule.berth_id), 0);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;  % Skip if no tasks for this berth
            end

            y_pos = y_start + b;

            % Sort by start time
            [~, sorted_idx] = sort(schedule.start_time(berth_tasks));
            berth_tasks = berth_tasks(sorted_idx);

            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);

                % Skip repair tasks in berth display
                if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                    continue;
                end

                if schedule.process_id(task_idx) == -3
                    continue;
                end

                if schedule.turbine_id(task_idx) == 0 && ...
                   (schedule.vessel_id(task_idx) == affected_vessel || ...
                    (~isempty(disruption) && isfield(disruption, 'time') && ...
                    abs(schedule.start_time(task_idx) - disruption.time) < 2))
                    continue;
                end

                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;

                % Skip zero-duration tasks
                if duration <= 0
                    continue;
                end

                % Draw task bar (loading is always process_id 0)
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);

                % 使用统一的较小字体大小
                font_size = 7;

                % Create label with turbine type information
                if schedule.turbine_id(task_idx) > 0
                    turbine_type_str = '';
                    turbine_id = schedule.turbine_id(task_idx);
                    if isfield(turbines, 'type') && turbine_id <= length(turbines)
                        if turbines(turbine_id).type == 1
                            turbine_type_str = '[S]'; % Small
                        else
                            turbine_type_str = '[L]'; % Large
                        end
                    end
                    label_text = sprintf('T%d%s-L', turbine_id, turbine_type_str);
                else
                    label_text = 'L';
                end

                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end

            % Add berth label on y-axis
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 8);
        end
    end

    % 添加图例，包括工序、船舶类型和风机类型
    % 创建组合图例
    all_handles = [dummy_handles, vessel_type_handles, turbine_type_handles];
    all_names = [process_names, vessel_type_names, turbine_type_names];

    % Create the legend with a smaller font size to fit all items
    legend(all_handles, all_names, 'Location', 'southoutside', 'Orientation', 'horizontal', 'FontSize', 6);

    % Set plot properties
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    % No y-axis label needed as we have custom labels

    % Calculate and display makespan
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 10, 'FontWeight', 'bold');

    % Calculate and display statistics about vessel types and turbine types
    if isfield(vessels, 'type')
        % Find single-hull and dual-hull vessels
        single_hull_indices = find([vessels.type] == 1);
        dual_hull_indices = find([vessels.type] == 2);

        % 初始化不同船舶类型的数组
        single_hull_times = [];
        dual_hull_times = [];

        % 收集按船舶类型的安装时间
        for i = 1:length(schedule.process_id)
            % 考虑安装工序（常规工序和最终安装）
            if (schedule.process_id(i) > 0 && schedule.process_id(i) <= 4) || schedule.process_id(i) == 6
                vessel_idx = schedule.vessel_id(i);
                process_time = schedule.end_time(i) - schedule.start_time(i);

                % 按船舶类型收集
                if ismember(vessel_idx, single_hull_indices)
                    single_hull_times = [single_hull_times, process_time];
                elseif ismember(vessel_idx, dual_hull_indices)
                    dual_hull_times = [dual_hull_times, process_time];
                end
            end
        end

        % 计算平均时间（如果数组不为空）
        if ~isempty(single_hull_times)
            avg_single_hull = mean(single_hull_times);
        else
            avg_single_hull = NaN;
        end

        if ~isempty(dual_hull_times)
            avg_dual_hull = mean(dual_hull_times);
        else
            avg_dual_hull = NaN;
        end

        % Display statistics
        y_offset = length(vessels) + berth_count + 3;

        % Display vessel type statistics
        if ~isnan(avg_single_hull)
            text(20, y_offset, ...
                sprintf('单体船平均工序时间: %.2f 小时', avg_single_hull), ...
                'FontSize', 8);
        else
            text(20, y_offset, ...
                '单体船平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        if ~isnan(avg_dual_hull)
            text(20, y_offset + 1, ...
                sprintf('双体船平均工序时间: %.2f 小时', avg_dual_hull), ...
                'FontSize', 8);
        else
            text(20, y_offset + 1, ...
                '双体船平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        % 只有当两种船舶都有有效数据且单体船时间不为0时才计算效率提升
        if ~isnan(avg_single_hull) && ~isnan(avg_dual_hull) && avg_single_hull > 0
            text(20, y_offset + 2, ...
                sprintf('双体船完全组装效率提升: %.2f%%', (1 - avg_dual_hull/avg_single_hull) * 100), ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isnan(avg_dual_hull) && isnan(avg_single_hull)
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无单体船数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif isnan(avg_dual_hull) && ~isnan(avg_single_hull)
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无双体船数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        else
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无足够数据计算', ...
                'FontSize', 8, 'FontWeight', 'bold');
        end

        % 添加风机类型效率统计
        if isfield(turbines, 'type')
            % 初始化不同风机类型的数组
            small_turbine_times = [];
            large_turbine_times = [];

            % 收集按风机类型的安装时间
            for i = 1:length(schedule.process_id)
                % 考虑安装工序（常规工序和最终安装）
                if (schedule.process_id(i) > 0 && schedule.process_id(i) <= 4) || schedule.process_id(i) == 6
                    turbine_id = schedule.turbine_id(i);
                    process_time = schedule.end_time(i) - schedule.start_time(i);

                    if turbine_id > 0 && turbine_id <= length(turbines)
                        if turbines(turbine_id).type == 1
                            small_turbine_times = [small_turbine_times, process_time];
                        else
                            large_turbine_times = [large_turbine_times, process_time];
                        end
                    end
                end
            end

            % 计算平均时间
            if ~isempty(small_turbine_times)
                avg_small_turbine = mean(small_turbine_times);
                text(20, y_offset + 4, ...
                    sprintf('小型风机平均安装时间: %.2f 小时', avg_small_turbine), ...
                    'FontSize', 8);
            else
                text(20, y_offset + 4, ...
                    '小型风机平均安装时间: 无数据', ...
                    'FontSize', 8);
            end

            if ~isempty(large_turbine_times)
                avg_large_turbine = mean(large_turbine_times);
                text(20, y_offset + 5, ...
                    sprintf('大型风机平均安装时间: %.2f 小时', avg_large_turbine), ...
                    'FontSize', 8);
            else
                text(20, y_offset + 5, ...
                    '大型风机平均安装时间: 无数据', ...
                    'FontSize', 8);
            end

            % 显示风机类型匹配效率
            if ~isempty(small_turbine_times) && ~isempty(large_turbine_times)
                text(20, y_offset + 6, ...
                    sprintf('大型风机安装时间比小型风机多: %.2f%%', ...
                    (avg_large_turbine/avg_small_turbine - 1) * 100), ...
                    'FontSize', 8, 'FontWeight', 'bold');
            end
        end

        % 显示船舶-风机匹配效率统计
        text(800, y_offset, ...
            '船舶-风机匹配策略:', ...
            'FontSize', 8, 'FontWeight', 'bold');
        text(800, y_offset + 1, ...
            '• 小型风机 + 单体船: 最优效率', ...
            'FontSize', 8, 'Color', [0.2, 0.6, 0.9]);
        text(800, y_offset + 2, ...
            '• 大型风机 + 双体船: 最优效率', ...
            'FontSize', 8, 'Color', [0.9, 0.2, 0.2]);
        text(800, y_offset + 3, ...
            '• 其他组合: 效率降低', ...
            'FontSize', 8, 'Color', [0.5, 0.5, 0.5]);
    end

    % Set axis limits - corrected part to ensure display of critical time range
    % Find critical time points
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Show tasks before and after disruption
        start_view = max(0, disruption.time - 100); % Show 100 hours before disruption
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % For initial schedule, maintain original view
        xlim([0, makespan * 1.05]);
    end

    % 确保合理的y轴范围，为统计信息留出额外空间
    if isfield(vessels, 'type') && isfield(turbines, 'type')
        ylim([0, length(vessels) + max(berth_count, 1) + 10]); % 为风机类型和匹配策略统计留出空间
    elseif isfield(vessels, 'type')
        ylim([0, length(vessels) + max(berth_count, 1) + 6]); % 为船舶类型统计信息留出额外空间
    else
        ylim([0, length(vessels) + max(berth_count, 1) + 3]);
    end

    % Add clearer time grid lines
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Add grid lines near disruption time
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % Major grid line every 20 hours
    end

    set(gca, 'YTick', []);  % Remove y-tick labels as we have custom labels

    hold off;
end