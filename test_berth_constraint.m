%% 测试泊位约束影响
% 简化版本测试2个泊位对单体船和双体船方案的影响

clear all;
close all;
clc;

%% 参数设置
TURBINE_COUNT = 20;
VESSEL_COUNT = 10;
BERTH_COUNT = 2;  % 2个泊位约束

% 时间参数
LOADING_TIME = 8;
ASSEMBLY_TIME = 35;
FINAL_INSTALL_TIME = 12;
WELDING_TIME = 6;
TRAVEL_TIME = 80/15; % 5.33小时
PROCESS_TIMES = [30, 20, 15, 45]; % 总计110小时

fprintf('=== 泊位约束影响分析 ===\n');
fprintf('项目规模：%d台风机，%d条船舶，%d个泊位\n', TURBINE_COUNT, VESSEL_COUNT, BERTH_COUNT);

%% 场景1：单体船方案分析
fprintf('\n--- 单体船方案 ---\n');

% 计算单体船的泊位需求
single_berth_time_per_turbine = LOADING_TIME; % 每台风机占用泊位时间
total_single_berth_demand = TURBINE_COUNT * single_berth_time_per_turbine;
single_berth_capacity = BERTH_COUNT * 1000; % 假设足够长的时间窗口

fprintf('每台风机泊位占用时间：%.2f小时\n', single_berth_time_per_turbine);
fprintf('总泊位需求：%.2f小时\n', total_single_berth_demand);

% 简化计算单体船工期
% 考虑泊位约束的排队时间
berth_queue_time = total_single_berth_demand / BERTH_COUNT;
single_vessel_cycle_time = LOADING_TIME + 2*TRAVEL_TIME + sum(PROCESS_TIMES);
single_makespan = max(berth_queue_time, single_vessel_cycle_time * TURBINE_COUNT / VESSEL_COUNT);

fprintf('单体船周期时间：%.2f小时\n', single_vessel_cycle_time);
fprintf('泊位排队时间：%.2f小时\n', berth_queue_time);
fprintf('单体船总工期：%.2f小时\n', single_makespan);

%% 场景2：双体船方案分析
fprintf('\n--- 双体船方案 ---\n');

dual_vessel_count = VESSEL_COUNT / 2; % 5条双体船
dual_berth_time_per_turbine = LOADING_TIME + ASSEMBLY_TIME; % 每台风机占用泊位时间
total_dual_berth_demand = TURBINE_COUNT * dual_berth_time_per_turbine;

fprintf('每台风机泊位占用时间：%.2f小时（装载+组装）\n', dual_berth_time_per_turbine);
fprintf('总泊位需求：%.2f小时\n', total_dual_berth_demand);

% 考虑拼接时间和泊位约束
dual_berth_queue_time = total_dual_berth_demand / BERTH_COUNT;
dual_vessel_cycle_time = LOADING_TIME + ASSEMBLY_TIME + 2*TRAVEL_TIME + FINAL_INSTALL_TIME;
dual_makespan = max(WELDING_TIME + dual_berth_queue_time, dual_vessel_cycle_time * TURBINE_COUNT / dual_vessel_count);

fprintf('双体船周期时间：%.2f小时\n', dual_vessel_cycle_time);
fprintf('泊位排队时间：%.2f小时\n', dual_berth_queue_time);
fprintf('拼接时间：%.2f小时\n', WELDING_TIME);
fprintf('双体船总工期：%.2f小时\n', dual_makespan);

%% 对比分析
fprintf('\n=== 对比分析 ===\n');
efficiency = (single_makespan - dual_makespan) / single_makespan * 100;

if efficiency > 0
    fprintf('✓ 双体船效率提升：%.2f%%\n', efficiency);
    fprintf('✓ 节省时间：%.2f小时\n', single_makespan - dual_makespan);
else
    fprintf('✗ 单体船效率更高：%.2f%%\n', -efficiency);
    fprintf('✗ 双体船额外时间：%.2f小时\n', dual_makespan - single_makespan);
end

%% 泊位利用率分析
fprintf('\n=== 泊位利用率分析 ===\n');

single_berth_util = (total_single_berth_demand / BERTH_COUNT) / single_makespan * 100;
dual_berth_util = (total_dual_berth_demand / BERTH_COUNT) / dual_makespan * 100;

fprintf('单体船泊位利用率：%.2f%%\n', single_berth_util);
fprintf('双体船泊位利用率：%.2f%%\n', dual_berth_util);

if dual_berth_util > 100
    fprintf('⚠️  双体船泊位利用率超过100%%，存在严重瓶颈！\n');
    fprintf('   建议增加泊位数量或优化组装流程\n');
end

%% 关键因素分析
fprintf('\n=== 关键因素分析 ===\n');

% 海上作业时间对比
single_sea_time = sum(PROCESS_TIMES);
dual_sea_time = FINAL_INSTALL_TIME;
sea_time_saving = (single_sea_time - dual_sea_time) / single_sea_time * 100;

fprintf('海上作业时间对比：\n');
fprintf('  单体船：%.2f小时/台\n', single_sea_time);
fprintf('  双体船：%.2f小时/台\n', dual_sea_time);
fprintf('  节省：%.2f%%\n', sea_time_saving);

% 港口作业时间对比
single_port_time = LOADING_TIME;
dual_port_time = LOADING_TIME + ASSEMBLY_TIME;
port_time_increase = (dual_port_time - single_port_time) / single_port_time * 100;

fprintf('\n港口作业时间对比：\n');
fprintf('  单体船：%.2f小时/台\n', single_port_time);
fprintf('  双体船：%.2f小时/台\n', dual_port_time);
fprintf('  增加：%.2f%%\n', port_time_increase);

% 拼接投资回报
total_sea_saving = TURBINE_COUNT * (single_sea_time - dual_sea_time);
total_welding_cost = dual_vessel_count * WELDING_TIME;
roi = total_sea_saving / total_welding_cost;

fprintf('\n拼接投资回报分析：\n');
fprintf('  总海上时间节省：%.2f小时\n', total_sea_saving);
fprintf('  总拼接时间投入：%.2f小时\n', total_welding_cost);
fprintf('  投资回报率：%.2f倍\n', roi);

%% 可视化对比
figure('Position', [100, 100, 1200, 600], 'Color', 'white');

% 子图1：工期对比
subplot(2, 3, 1);
bar([single_makespan, dual_makespan], 'FaceColor', [0.3, 0.6, 0.8]);
set(gca, 'XTickLabel', {'单体船', '双体船'});
ylabel('总工期 (小时)');
title('总工期对比');
grid on;

% 子图2：泊位利用率对比
subplot(2, 3, 2);
bar([single_berth_util, dual_berth_util], 'FaceColor', [0.8, 0.4, 0.2]);
set(gca, 'XTickLabel', {'单体船', '双体船'});
ylabel('泊位利用率 (%)');
title('泊位利用率对比');
grid on;
ylim([0, max(120, max(single_berth_util, dual_berth_util)*1.1)]);

% 子图3：海上作业时间对比
subplot(2, 3, 3);
bar([single_sea_time, dual_sea_time], 'FaceColor', [0.2, 0.8, 0.4]);
set(gca, 'XTickLabel', {'单体船', '双体船'});
ylabel('海上作业时间 (小时/台)');
title('海上作业时间对比');
grid on;

% 子图4：港口作业时间对比
subplot(2, 3, 4);
bar([single_port_time, dual_port_time], 'FaceColor', [0.8, 0.6, 0.2]);
set(gca, 'XTickLabel', {'单体船', '双体船'});
ylabel('港口作业时间 (小时/台)');
title('港口作业时间对比');
grid on;

% 子图5：时间构成分析
subplot(2, 3, 5);
categories = {'港口', '航行', '海上作业'};
single_components = [single_port_time, 2*TRAVEL_TIME, single_sea_time];
dual_components = [dual_port_time, 2*TRAVEL_TIME, dual_sea_time];

x = 1:length(categories);
width = 0.35;
bar(x - width/2, single_components, width, 'DisplayName', '单体船');
hold on;
bar(x + width/2, dual_components, width, 'DisplayName', '双体船');

set(gca, 'XTickLabel', categories);
ylabel('时间 (小时)');
title('单台风机时间构成');
legend({'港口', '航行', '海上作业'}, 'Location', 'best');
grid on;

% 子图6：效率提升分析
subplot(2, 3, 6);
improvements = [efficiency, sea_time_saving, roi*10]; % ROI缩放10倍便于显示
bar(improvements, 'FaceColor', [0.6, 0.2, 0.8]);
set(gca, 'XTickLabel', {'总效率', '海上时间', 'ROI×10'});
ylabel('提升百分比 (%)');
title('双体船优势分析');
grid on;

sgtitle(sprintf('泊位约束分析：%d个泊位对双体船技术的影响', BERTH_COUNT), ...
    'FontSize', 14, 'FontWeight', 'bold');

fprintf('\n=== 分析完成 ===\n');
if dual_berth_util > 100
    fprintf('结论：2个泊位对双体船方案形成严重约束，建议增加泊位或优化流程\n');
elseif efficiency > 0
    fprintf('结论：即使在2个泊位约束下，双体船仍有%.2f%%的效率优势\n', efficiency);
else
    fprintf('结论：2个泊位约束下，单体船方案更优，优势%.2f%%\n', -efficiency);
end
