%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with weather conditions, disruption handling and rescheduling.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters 
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours)
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 100;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.17;  % PSO mutation rate
INERTIA_WEIGHT = 0.65;     % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 100;    % Time of disruption (hours)
REPAIR_TIME = 48;         % Repair time (hours)
AFFECTED_VESSEL = 3;      % Affected vessel ID

%% Weather Data Parameters - UPDATED
% Create weather data for 30 days (720 hours)
DAYS_IN_MONTH = 30;
HOURS_PER_DAY = 24;
MONTH_HOURS = DAYS_IN_MONTH * HOURS_PER_DAY;

% Create global weather data variable
global WEATHER_DATA;

% Generate sample weather data (1 = good weather, 0 = bad weather)
% We'll create a realistic weather pattern with some consecutive bad weather days
WEATHER_DATA = ones(1, MONTH_HOURS); % Initialize all as good weather

% Define bad weather periods (e.g., storms lasting 12-48 hours)
% Add several weather events throughout the month
bad_weather_starts = [2*24, 8*24, 15*24, 22*24, 27*24]; % Days 2, 8, 15, 22, 27
bad_weather_durations = [36, 24, 48, 18, 30]; % Hours

% Mark bad weather periods
for i = 1:length(bad_weather_starts)
    start_hour = bad_weather_starts(i);
    end_hour = min(start_hour + bad_weather_durations(i) - 1, MONTH_HOURS);
    WEATHER_DATA(start_hour+1:end_hour+1) = 0; % 0 indicates bad weather
end

% Calculate percentage of good weather days
good_weather_percentage = sum(WEATHER_DATA) / length(WEATHER_DATA) * 100;
fprintf('Weather data generated: %.1f%% good weather days in the month.\n', good_weather_percentage);

%% Data Structures
% Define turbine installation tasks
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;
end

% Define vessels
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES));

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

%% Right-Shift Rescheduling
fprintf('Applying right-shift rescheduling...\n');
right_shift_schedule = rightShiftRescheduling(initial_schedule, disruption);
makespan_right_shift = calculateMakespan(right_shift_schedule);
fprintf('Right-shift schedule makespan: %.2f hours\n', makespan_right_shift);

%% Complete Rescheduling
fprintf('Applying complete rescheduling...\n');
complete_schedule = completeRescheduling(initial_schedule, disruption, ...
    turbines, vessels, berths);
makespan_complete = calculateMakespan(complete_schedule);
fprintf('Complete rescheduling makespan: %.2f hours\n', makespan_complete);

%% Plot Results
% Plot three Gantt charts
plotGanttChart(initial_schedule, 'Initial Schedule', turbines, vessels, []);
plotGanttChart(right_shift_schedule, 'Right-Shift Rescheduling', turbines, vessels, disruption);
plotGanttChart(complete_schedule, 'Complete Rescheduling', turbines, vessels, disruption);

% Plot weather data
plotWeatherData();

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);
    
    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, local_pop_size);
    
    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);
    
    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Check diversity
        diversity = calculateDiversity(fitness);
        
        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end
    
    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);
    
    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE and WEATHER_DATA
    global PORT_TO_FARM_DISTANCE WEATHER_DATA;
    
    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        
        % Loading can occur regardless of weather (it's in port)
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm - CORRECTED: Travel is affected by weather
        travel_start = loading_end;
        travel_duration = port_to_farm_time(vessel_idx);
        
        % Find next feasible time window for travel
        [travel_start_actual, travel_end] = findNextFeasibleWindow(travel_start, travel_duration);
        
        % If travel was delayed due to weather, update the start time
        if travel_start_actual > travel_start
            % Add waiting task (waiting for good weather)
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = travel_start;
            schedule.end_time(end+1) = travel_start_actual;
            schedule.process_id(end+1) = -4; % -4 for weather waiting
            schedule.berth_id(end+1) = 0;  % 0 for no berth
        end
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start_actual;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks - CORRECTED: Each task must check weather
        current_time = travel_end;
        for p = 1:num_processes
            % Find next feasible time window for this process
            process_duration = process_times(p);
            [process_start, process_end] = findNextFeasibleWindow(current_time, process_duration);
            
            % If process was delayed due to weather, add waiting task
            if process_start > current_time
                % Add waiting task (waiting for good weather)
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = current_time;
                schedule.end_time(end+1) = process_start;
                schedule.process_id(end+1) = -4; % -4 for weather waiting
                schedule.berth_id(end+1) = 0;  % 0 for no berth
            end
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port - CORRECTED: Travel is affected by weather
        travel_back_start = current_time;
        travel_back_duration = port_to_farm_time(vessel_idx);
        
        % Find next feasible time window for return travel
        [travel_back_start_actual, travel_back_end] = findNextFeasibleWindow(travel_back_start, travel_back_duration);
        
        % If return travel was delayed due to weather, add waiting task
        if travel_back_start_actual > travel_back_start
            % Add waiting task (waiting for good weather)
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = travel_back_start;
            schedule.end_time(end+1) = travel_back_start_actual;
            schedule.process_id(end+1) = -4; % -4 for weather waiting
            schedule.berth_id(end+1) = 0;  % 0 for no berth
        end
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start_actual;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

% IMPROVED: Function to find next feasible time window considering weather
function [start_time, end_time] = findNextFeasibleWindow(initial_start, duration)
    global WEATHER_DATA;
    
    % Initialize start time
    start_time = initial_start;
    
    % If we're beyond the weather data timeframe, assume good weather
    if start_time >= length(WEATHER_DATA)
        end_time = start_time + duration;
        return;
    end
    
    % Look for a continuous window of good weather that can fit the task
    while true
        % Round start_time to next hour if not already at an hour boundary
        hour_start = ceil(start_time);
        
        % Check if we've gone beyond weather data
        if hour_start >= length(WEATHER_DATA)
            end_time = start_time + duration;
            return;
        end
        
        % Check if current hour has good weather
        if WEATHER_DATA(hour_start + 1) == 1  % +1 because MATLAB indexing starts at 1
            % Initialize continuous good weather counter
            continuous_good_hours = 0;
            
            % Check for continuous good weather window
            for h = hour_start:min(hour_start + ceil(duration) - 1, length(WEATHER_DATA) - 1)
                if h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 1
                    continuous_good_hours = continuous_good_hours + 1;
                else
                    break;
                end
            end
            
            % If we found enough continuous good weather hours
            if continuous_good_hours >= duration
                end_time = start_time + duration;
                return;
            else
                % Skip ahead to after the bad weather
                h = hour_start + continuous_good_hours;
                while h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 0
                    h = h + 1;
                end
                start_time = h;
            end
        else
            % Current hour has bad weather, find next good weather hour
            h = hour_start;
            while h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 0
                h = h + 1;
            end
            start_time = h;
        end
        
        % If we're beyond weather data, assume good weather
        if start_time >= length(WEATHER_DATA)
            end_time = start_time + duration;
            return;
        end
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);
    
    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));
    
    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);
    
    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));
    
    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));
    
    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);
    
    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;
    
    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function right_shift_schedule = rightShiftRescheduling(schedule, disruption)
    % Weather-aware right-shift rescheduling with fixed disruption handling
    global WEATHER_DATA;
    
    % Initialize right-shift schedule - first copy original schedule
    right_shift_schedule = schedule;
    
    affected_vessel = disruption.affected_vessel;
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    
    % Find tasks affected by the vessel failure
    % 1. Find tasks in progress when failure occurs
    ongoing_indices = find(schedule.vessel_id == affected_vessel & ...
                          schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);
    
    % 2. Find subsequent tasks of the failed vessel
    future_indices = find(schedule.vessel_id == affected_vessel & ...
                         schedule.start_time >= disruption_time);
    
    % 3. Find berth competition tasks
    % First identify berths used by the failed vessel
    affected_berths = unique(schedule.berth_id(schedule.vessel_id == affected_vessel & ...
                                              schedule.berth_id > 0));
    
    % Find other vessels' tasks using the same berths starting after failure time
    berth_conflict_indices = [];
    for b = 1:length(affected_berths)
        berth_indices = find(schedule.berth_id == affected_berths(b) & ...
                             schedule.vessel_id ~= affected_vessel & ...
                             schedule.start_time >= disruption_time);
        berth_conflict_indices = [berth_conflict_indices, berth_indices];
    end
    
    % Add repair task to the schedule
    repair_task_idx = length(right_shift_schedule.turbine_id) + 1;
    right_shift_schedule.turbine_id(repair_task_idx) = 0; % 0 means no specific turbine
    right_shift_schedule.vessel_id(repair_task_idx) = affected_vessel;
    right_shift_schedule.start_time(repair_task_idx) = disruption_time;
    right_shift_schedule.end_time(repair_task_idx) = disruption_time + repair_time;
    right_shift_schedule.process_id(repair_task_idx) = -3; % -3 means repair process
    right_shift_schedule.berth_id(repair_task_idx) = 0;  % repairs don't use a berth
    
    % 1. Adjust tasks in progress - only extend end time
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        remaining_time = schedule.end_time(idx) - disruption_time;
        
        % For tasks on the failed vessel, add repair time directly
        if schedule.vessel_id(idx) == affected_vessel
            right_shift_schedule.end_time(idx) = disruption_time + repair_time + remaining_time;
        end
    end
    
    % 2. Adjust future tasks - with weather consideration
    for i = 1:length(future_indices)
        idx = future_indices(i);
        orig_duration = schedule.end_time(idx) - schedule.start_time(idx);
        
        % Adjust start time to account for repair
        new_start = schedule.start_time(idx) + repair_time;
        
        % Find feasible window considering weather
        [new_start_actual, new_end] = findNextFeasibleWindow(new_start, orig_duration);
        
        right_shift_schedule.start_time(idx) = new_start_actual;
        right_shift_schedule.end_time(idx) = new_end;
        
        % If there was a weather delay, insert a waiting task
        if new_start_actual > new_start
            % Insert a new weather waiting task
            right_shift_schedule.turbine_id(end+1) = schedule.turbine_id(idx);
            right_shift_schedule.vessel_id(end+1) = schedule.vessel_id(idx);
            right_shift_schedule.start_time(end+1) = new_start;
            right_shift_schedule.end_time(end+1) = new_start_actual;
            right_shift_schedule.process_id(end+1) = -4; % -4 for weather waiting
            right_shift_schedule.berth_id(end+1) = 0;
        end
    end
    
    % 3. Adjust berth conflict tasks
    for i = 1:length(berth_conflict_indices)
        idx = berth_conflict_indices(i);
        orig_duration = schedule.end_time(idx) - schedule.start_time(idx);
        
        % Shift tasks that compete for berths
        new_start = schedule.start_time(idx) + repair_time;
        
        % For berth loading, weather isn't a factor as it's in port
        right_shift_schedule.start_time(idx) = new_start;
        right_shift_schedule.end_time(idx) = new_start + orig_duration;
    end
    
    % 4. Resolve conflicts - ensure tasks of the same vessel don't overlap
    adjusted = true;
    while adjusted
        adjusted = false;
        
        % Check each vessel
        for v = 1:length(unique(right_shift_schedule.vessel_id))
            vessel_tasks = find(right_shift_schedule.vessel_id == v);
            if isempty(vessel_tasks)
                continue;
            end
            
            % Sort by start time
            [~, sorted_idx] = sort(right_shift_schedule.start_time(vessel_tasks));
            tasks_by_time = vessel_tasks(sorted_idx);
            
            % Check for overlaps between adjacent tasks
            for i = 1:length(tasks_by_time)-1
                current_task = tasks_by_time(i);
                next_task = tasks_by_time(i+1);
                
                % Check for overlap
                if right_shift_schedule.end_time(current_task) > right_shift_schedule.start_time(next_task)
                    % Special case: if current task is repair, don't move it
                    if right_shift_schedule.process_id(current_task) == -3
                        % Push next task forward
                        shift = right_shift_schedule.end_time(current_task) - right_shift_schedule.start_time(next_task);
                        
                        % For loading tasks (in port), no weather check needed
                        if right_shift_schedule.process_id(next_task) == 0
                            new_start = right_shift_schedule.start_time(next_task) + shift;
                            new_end = new_start + (right_shift_schedule.end_time(next_task) - right_shift_schedule.start_time(next_task));
                        else
                            % For other tasks, check weather
                            orig_duration = right_shift_schedule.end_time(next_task) - right_shift_schedule.start_time(next_task);
                            [new_start, new_end] = findNextFeasibleWindow(right_shift_schedule.start_time(next_task) + shift, orig_duration);
                        end
                        
                        right_shift_schedule.start_time(next_task) = new_start;
                        right_shift_schedule.end_time(next_task) = new_end;
                        adjusted = true;
                    else
                        % Normal case: maintain time order
                        orig_duration = right_shift_schedule.end_time(next_task) - right_shift_schedule.start_time(next_task);
                        
                        % For loading tasks (in port), no weather check needed
                        if right_shift_schedule.process_id(next_task) == 0
                            new_start = right_shift_schedule.end_time(current_task);
                            new_end = new_start + orig_duration;
                        else
                            % For other tasks, check weather
                            [new_start, new_end] = findNextFeasibleWindow(right_shift_schedule.end_time(current_task), orig_duration);
                        end
                        
                        right_shift_schedule.start_time(next_task) = new_start;
                        right_shift_schedule.end_time(next_task) = new_end;
                        adjusted = true;
                    end
                    
                    % If there was a weather delay, insert a waiting task
                    if right_shift_schedule.process_id(next_task) ~= 0  
                       new_start > right_shift_schedule.end_time(current_task)
                        % Insert a new weather waiting task
                        right_shift_schedule.turbine_id(end+1) = right_shift_schedule.turbine_id(next_task);
                        right_shift_schedule.vessel_id(end+1) = right_shift_schedule.vessel_id(next_task);
                        right_shift_schedule.start_time(end+1) = right_shift_schedule.end_time(current_task);
                        right_shift_schedule.end_time(end+1) = new_start;
                        right_shift_schedule.process_id(end+1) = -4; % -4 for weather waiting
                        right_shift_schedule.berth_id(end+1) = 0;
                        adjusted = true;
                    end
                end
            end
        end
        
        % Check berth conflicts
        for b = 1:max(right_shift_schedule.berth_id)
            berth_tasks = find(right_shift_schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;
            end
            
            % Sort by start time
            [~, sorted_idx] = sort(right_shift_schedule.start_time(berth_tasks));
            tasks_by_time = berth_tasks(sorted_idx);
            
            % Check for overlaps between adjacent tasks
            for i = 1:length(tasks_by_time)-1
                current_task = tasks_by_time(i);
                next_task = tasks_by_time(i+1);
                
                % Check for overlap
                if right_shift_schedule.end_time(current_task) > right_shift_schedule.start_time(next_task)
                    duration = right_shift_schedule.end_time(next_task) - right_shift_schedule.start_time(next_task);
                    
                    right_shift_schedule.start_time(next_task) = right_shift_schedule.end_time(current_task);
                    right_shift_schedule.end_time(next_task) = right_shift_schedule.start_time(next_task) + duration;
                    adjusted = true;
                end
            end
        end
        
        % Check process precedence
        for t = 1:max(right_shift_schedule.turbine_id)
            turbine_tasks = find(right_shift_schedule.turbine_id == t);
            if isempty(turbine_tasks)
                continue;
            end
            
            for i = turbine_tasks
                process_i = right_shift_schedule.process_id(i);
                for j = turbine_tasks
                    if i ~= j
                        process_j = right_shift_schedule.process_id(j);
                        % If process j should come before process i
                        if process_j < process_i && right_shift_schedule.start_time(i) < right_shift_schedule.end_time(j)
                            duration = right_shift_schedule.end_time(i) - right_shift_schedule.start_time(i);
                            
                            % For loading tasks (in port), no weather check needed
                            if process_i == 0
                                new_start = right_shift_schedule.end_time(j);
                                new_end = new_start + duration;
                            else
                                % For other tasks, check weather
                                [new_start, new_end] = findNextFeasibleWindow(right_shift_schedule.end_time(j), duration);
                            end
                            
                            right_shift_schedule.start_time(i) = new_start;
                            right_shift_schedule.end_time(i) = new_end;
                            adjusted = true;
                            
                            % If there was a weather delay, insert a waiting task
                            if process_i ~= 0 && new_start > right_shift_schedule.end_time(j)
                                % Insert a new weather waiting task
                                right_shift_schedule.turbine_id(end+1) = right_shift_schedule.turbine_id(i);
                                right_shift_schedule.vessel_id(end+1) = right_shift_schedule.vessel_id(i);
                                right_shift_schedule.start_time(end+1) = right_shift_schedule.end_time(j);
                                right_shift_schedule.end_time(end+1) = new_start;
                                right_shift_schedule.process_id(end+1) = -4; % -4 for weather waiting
                                right_shift_schedule.berth_id(end+1) = 0;
                                adjusted = true;
                            end
                        end
                    end
                end
            end
        end
    end
end

function complete_schedule = completeRescheduling(schedule, disruption, turbines, vessels, berths)
    % Weather-aware complete rescheduling with fixed disruption handling
    global WEATHER_DATA;
    
    % Get disruption parameters
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    affected_vessel = disruption.affected_vessel;
    
    % 1. Identify completed tasks (end time <= disruption time)
    completed_indices = find(schedule.end_time <= disruption_time);
    
    % 2. Identify tasks in progress during failure
    ongoing_indices = find(schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);
    
    % 3. Identify tasks not yet started
    future_indices = find(schedule.start_time >= disruption_time);
    
    % Copy completed tasks from original schedule
    complete_schedule = struct('turbine_id', schedule.turbine_id(completed_indices), ...
                              'vessel_id', schedule.vessel_id(completed_indices), ...
                              'start_time', schedule.start_time(completed_indices), ...
                              'end_time', schedule.end_time(completed_indices), ...
                              'process_id', schedule.process_id(completed_indices), ...
                              'berth_id', schedule.berth_id(completed_indices));
    
    % Add repair task to the schedule
    repair_task_idx = length(complete_schedule.turbine_id) + 1;
    complete_schedule.turbine_id(repair_task_idx) = 0; % 0 means no specific turbine
    complete_schedule.vessel_id(repair_task_idx) = affected_vessel;
    complete_schedule.start_time(repair_task_idx) = disruption_time;
    complete_schedule.end_time(repair_task_idx) = disruption_time + repair_time;
    complete_schedule.process_id(repair_task_idx) = -3; % -3 means repair process
    complete_schedule.berth_id(repair_task_idx) = 0;  % repairs don't use a berth
    
    % Handle tasks in progress - adjust their end times
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        remaining_time = schedule.end_time(idx) - disruption_time;
        
        % For failed vessel tasks, add repair time and check weather
        if schedule.vessel_id(idx) == affected_vessel
            new_start = disruption_time + repair_time;
            
            % For loading (in port), no weather check
            if schedule.process_id(idx) == 0
                new_end = new_start + remaining_time;
            else
                % For other tasks, check weather
                [~, new_end] = findNextFeasibleWindow(new_start, remaining_time);
            end
        else
            % For other vessels, keep original end time
            new_start = schedule.start_time(idx);
            new_end = schedule.end_time(idx);
        end
        
        
        % Add to the new schedule
        complete_schedule.turbine_id(end+1) = schedule.turbine_id(idx);
        complete_schedule.vessel_id(end+1) = schedule.vessel_id(idx);
        complete_schedule.start_time(end+1) = new_start;
        complete_schedule.end_time(end+1) = new_end;
        complete_schedule.process_id(end+1) = schedule.process_id(idx);
        complete_schedule.berth_id(end+1) = schedule.berth_id(idx);
    end
    
    % Extract remaining turbines
    remaining_turbines = [];
    
    % Create a set of completed tasks for quick lookup
    completed_tasks = zeros(length(turbines), length(turbines(1).processes) + 3); % +3 for loading, travel to/from
    
    % Mark completed and in-progress tasks
    % Fix: Ensure both index vectors are column vectors before concatenating
    all_indices = [completed_indices(:); ongoing_indices(:)];
    
    for i = 1:length(all_indices)
        idx = all_indices(i);
        t_id = schedule.turbine_id(idx);
        
        % Skip repair tasks (turbine_id is 0)
        if t_id == 0
            continue;
        end
        
        p_id = schedule.process_id(idx);
        
        % Map process_id to the correct index
        if p_id == 0       % loading
            p_idx = 1;
        elseif p_id == -1  % travel to farm
            p_idx = 2;
        elseif p_id == -2  % return to port
            p_idx = length(turbines(1).processes) + 3;
        else               % normal process
            p_idx = p_id + 2;
        end
        
        completed_tasks(t_id, p_idx) = 1;
    end
    
    % Extract turbines with remaining tasks
    for t = 1:length(turbines)
        for p = 1:(length(turbines(1).processes) + 3)
            if completed_tasks(t, p) == 0
                if ~ismember(t, remaining_turbines)
                    remaining_turbines = [remaining_turbines, t];
                end
                break;
            end
        end
    end
    
    % Fix: Correctly create simplified_turbines struct array
    simplified_turbines = struct('id', {}, 'processes', {});
    
    % Now it's safe to add elements
    for i = 1:length(remaining_turbines)
        t_id = remaining_turbines(i);
        % Use struct field copying instead of direct assignment
        new_turbine = struct('id', turbines(t_id).id, 'processes', turbines(t_id).processes);
        simplified_turbines(end+1) = new_turbine;
    end
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, length(vessels));
    berth_avail_time = zeros(1, length(berths));
    
    % Update vessel and berth availability times
    for i = 1:length(complete_schedule.end_time)
        v_id = complete_schedule.vessel_id(i);
        vessel_avail_time(v_id) = max(vessel_avail_time(v_id), complete_schedule.end_time(i));
        
        b_id = complete_schedule.berth_id(i);
        if b_id > 0
            berth_avail_time(b_id) = max(berth_avail_time(b_id), complete_schedule.end_time(i));
        end
    end
    
    % Special handling for the failed vessel's availability time
    vessel_avail_time(affected_vessel) = max(vessel_avail_time(affected_vessel), disruption_time + repair_time);
    
    % If there are remaining tasks, call hybrid GA-PSO for optimization
    if ~isempty(simplified_turbines)
        % Create a new optimization problem and set the start time to disruption_time
        remaining_schedule = generateRemainingSchedule(simplified_turbines, vessels, berths, ...
                                                     vessel_avail_time, disruption_time);
        
        % Merge completed/in-progress tasks with newly optimized tasks
        complete_schedule.turbine_id = [complete_schedule.turbine_id, remaining_schedule.turbine_id];
        complete_schedule.vessel_id = [complete_schedule.vessel_id, remaining_schedule.vessel_id];
        complete_schedule.start_time = [complete_schedule.start_time, remaining_schedule.start_time];
        complete_schedule.end_time = [complete_schedule.end_time, remaining_schedule.end_time];
        complete_schedule.process_id = [complete_schedule.process_id, remaining_schedule.process_id];
        complete_schedule.berth_id = [complete_schedule.berth_id, remaining_schedule.berth_id];
    end
end

function remaining_schedule = generateRemainingSchedule(turbines, vessels, berths, vessel_avail_time, start_time)
    % Generate schedule for remaining tasks with weather awareness
    % Access global variables including weather data
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD WEATHER_DATA;
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Use smaller population size and iterations for faster rescheduling
    local_pop_size = max(20, min(POP_SIZE, 30));
    local_max_gen = max(50, min(MAX_GEN, 80));
    
    % Initialize population
    population = cell(1, local_pop_size);
    fitness = zeros(1, local_pop_size);
    
    % Generate initial population
    for i = 1:local_pop_size
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, local_pop_size);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, local_pop_size);
    
    % Initialize velocities
    for i = 1:local_pop_size
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Main loop
    for gen = 1:local_max_gen
        % Evaluate population
        for i = 1:local_pop_size
            % Decode chromosome to schedule, considering vessel initial availability times
            schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                turbines, vessels, berths, vessel_avail_time, start_time);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Monitor convergence
        if mod(gen, 10) == 0
            fprintf('Rescheduling Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Calculate population diversity
        diversity = calculateDiversity(fitness);
        
        % If diversity is below threshold, apply PSO
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected during rescheduling at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for local search
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:local_pop_size
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                        turbines, vessels, berths, vessel_avail_time, start_time);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, local_pop_size);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:local_pop_size
                % Tournament selection
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination condition
        if gen >= local_max_gen
            break;
        end
    end
    
    % Convert best solution to schedule
    remaining_schedule = decodeChromosomeWithStartTime(gbest, ...
        turbines, vessels, berths, vessel_avail_time, start_time);
end

function schedule = decodeChromosomeWithStartTime(chromosome, turbines, vessels, berths, vessel_avail_time, start_time)
    % Decodes chromosome to schedule with weather awareness, using initial availability times
    global PORT_TO_FARM_DISTANCE WEATHER_DATA;
    
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize berth availability times
    berth_avail_time = ones(1, berth_count) * start_time;
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbines(i).id;  % Use correct turbine ID
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(i).processes;  % Use turbines(i) not turbine_idx
        num_processes = length(process_times);
        
        % Handle berth assignment
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm - CORRECTED: Travel is affected by weather
        travel_start = loading_end;
        travel_duration = port_to_farm_time(vessel_idx);
        
        % Find next feasible time window for travel
        [travel_start_actual, travel_end] = findNextFeasibleWindow(travel_start, travel_duration);
        
        % If travel was delayed due to weather, add waiting task
        if travel_start_actual > travel_start
            % Add waiting task (waiting for good weather)
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = travel_start;
            schedule.end_time(end+1) = travel_start_actual;
            schedule.process_id(end+1) = -4; % -4 for weather waiting
            schedule.berth_id(end+1) = 0;  % 0 for no berth
        end
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start_actual;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks - CORRECTED: Each task must check weather
        current_time = travel_end;
        for p = 1:num_processes
            % Find next feasible time window for this process
            process_duration = process_times(p);
            [process_start, process_end] = findNextFeasibleWindow(current_time, process_duration);
            
            % If process was delayed due to weather, add waiting task
            if process_start > current_time
                % Add waiting task (waiting for good weather)
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = current_time;
                schedule.end_time(end+1) = process_start;
                schedule.process_id(end+1) = -4; % -4 for weather waiting
                schedule.berth_id(end+1) = 0;  % 0 for no berth
            end
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port - CORRECTED: Travel is affected by weather
        travel_back_start = current_time;
        travel_back_duration = port_to_farm_time(vessel_idx);
        
        % Find next feasible time window for return travel
        [travel_back_start_actual, travel_back_end] = findNextFeasibleWindow(travel_back_start, travel_back_duration);
        
               % If return travel was delayed due to weather, add waiting task
        if travel_back_start_actual > travel_back_start
            % Add waiting task (waiting for good weather)
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = travel_back_start;
            schedule.end_time(end+1) = travel_back_start_actual;
            schedule.process_id(end+1) = -4; % -4 for weather waiting
            schedule.berth_id(end+1) = 0;  % 0 for no berth
        end
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start_actual;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % Create professional-looking Gantt chart with weather visualization
    global WEATHER_DATA;
    
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    
    % Use softer color scheme
    process_colors = [
        0.9290, 0.6940, 0.3250;  % loading (process 0) - soft orange
        0.4660, 0.7740, 0.8880;  % travel to farm (process -1) - soft blue
        0.6350, 0.5040, 0.7410;  % process 1 - soft purple
        0.4660, 0.7410, 0.3880;  % process 2 - soft green
        0.8500, 0.3250, 0.3980;  % process 3 - soft red
        0.9290, 0.6940, 0.1250;  % process 4 - soft yellow
        0.4660, 0.7740, 0.8880;  % return to port (process -2) - soft blue
        1.0000, 0.3000, 0.3000;  % repair (added) - striking but not too harsh red
        0.7000, 0.7000, 0.7000;  % weather waiting (-4) - gray
    ];
    
    % Process names for legend, add repair and weather waiting
    process_names = {'Loading', 'Travel to Farm', 'Process 1', 'Process 2', 'Process 3', 'Process 4', 'Return to Port', 'Repair', 'Weather Wait'};
    
    % Create dummy objects for legend
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end
    
    % Draw vessel timelines
    hold on;
    
    % NEW: Add weather bands at the bottom of the chart
    if ~isempty(WEATHER_DATA)
        % Calculate the maximum makespan
        max_time = max(schedule.end_time);
        
        % Add weather bands for the visible range
        for i = 1:min(length(WEATHER_DATA), ceil(max_time))
            if WEATHER_DATA(i) == 0  % Bad weather
                % Draw a semi-transparent red band for bad weather periods
                rectangle('Position', [i-1, -2, 1, 1], ...
                          'FaceColor', [1, 0, 0, 0.2], ... % Red with 20% opacity
                          'EdgeColor', 'none');
            end
        end
        
        % Create a label for the weather bands
        text(-20, -1.5, 'Weather', 'HorizontalAlignment', 'right', ...
             'FontWeight', 'bold');
    end
    
    % If there's a disruption, add disruption marker line
    if ~isempty(disruption)
        % Draw vertical line at disruption time
        line([disruption.time, disruption.time], [-2, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);
        
        % Add disruption marker text
        text(disruption.time, length(vessels)+2.5, 'Disruption', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end
    
    % Organize tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        y_pos = v;
        
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;
            
            % Determine process type
            process_id = schedule.process_id(task_idx);
            
            % Map process_id to color index
            if process_id == 0  % loading
                color_idx = 1;
                process_label = 'L';
            elseif process_id == -1  % travel to farm
                color_idx = 2;
                process_label = 'TF';
            elseif process_id == -2  % return to port
                color_idx = 7;
                process_label = 'TB';
            elseif process_id == -3  % repair
                color_idx = 8;
                process_label = 'R';
            elseif process_id == -4  % weather waiting
                color_idx = 9;
                process_label = 'W';
            else  % regular process
                color_idx = process_id + 2;
                process_label = ['P', num2str(process_id)];
            end
            
            % Draw task bar
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);
            
            % Adjust font size based on duration
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end
            
            % Create label
            if process_id == -3  % repair
                label_text = 'Repair';
            elseif process_id == -4  % weather waiting
                label_text = 'Weather';
            else
                if schedule.turbine_id(task_idx) == 0
                    label_text = process_label;
                else
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                end
            end
            
            % For very short durations, use rotated or external label
            if duration < 5
                % Use external label with line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw small line connecting to the block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end
        
        % Add vessel label on y-axis
        % If this is the affected vessel, add marker
        if ~isempty(disruption) && v == disruption.affected_vessel
            text(-20, y_pos, sprintf('Vessel %d (Failed)', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r');
        else
            text(-20, y_pos, sprintf('Vessel %d', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Add berth timelines
    berth_count = max(schedule.berth_id);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            y_pos = y_start + b;
            
            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;
                
                % Draw task bar (loading is always process_id 0)
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
                
                % Add appropriately scaled text label
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end
                
                % Create label
                label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                
                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end
            
            % Add berth label on y-axis
            text(-20, y_pos, sprintf('Berth %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Fix: Add legend with only valid handles (no weather_dummy)
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');
    
    % Set plot properties
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('Time (hours)', 'FontSize', 12);
    % No y-label needed as we have custom labels
    
    % Calculate makespan and display
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('Makespan: %.2f hours', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    
    % Set axis limits - fixed section, ensure key time range is shown
    % Find key time points
    if ~isempty(disruption)
        % Show tasks before and after disruption
        start_view = max(0, disruption.time - 100); % Show 100 hours before disruption
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % For initial schedule, keep original view
        xlim([0, makespan * 1.05]);
    end
    ylim([-2.5, length(vessels) + berth_count + 3]);
    
    % Add clearer time grid lines
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');  
    if ~isempty(disruption)
        % Add grid lines near disruption time
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % Major grid line every 20 hours
    end
    
    set(gca, 'YTick', []);  % Remove y tick labels as we have custom labels
    
    hold off;
end

% Function to plot weather data
function plotWeatherData()
    global WEATHER_DATA;
    
    % Create a new figure for weather data
    figure('Position', [100, 100, 800, 300], 'Color', 'white');
    
    % Plot weather data
    subplot(2,1,1);
    
    % Create bar chart showing weather conditions
    for i = 1:length(WEATHER_DATA)
        if WEATHER_DATA(i) == 1  % Good weather
            bar(i-1, 1, 'FaceColor', [0.3, 0.6, 0.9], 'EdgeColor', 'none');
            hold on;
        else  % Bad weather
            bar(i-1, 1, 'FaceColor', [0.9, 0.3, 0.3], 'EdgeColor', 'none');
            hold on;
        end
    end
    
    % Set properties
    title('Weather Conditions Over Time', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('Weather Condition');
    xlabel('Time (hours)');
    ylim([0, 1.2]);
    xlim([0, length(WEATHER_DATA)]);
    
    % Add custom y-ticks
    set(gca, 'YTick', [0, 1], 'YTickLabel', {'Bad', 'Good'});
    grid on;
    
    % Add day markers
    hold on;
    for day = 1:floor(length(WEATHER_DATA)/24)
        line([day*24, day*24], [0, 1.2], 'Color', [0.5, 0.5, 0.5], 'LineStyle', ':', 'LineWidth', 1);
        text(day*24, 1.1, sprintf('Day %d', day), 'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    
    % Add a subplot to show weather distribution
    subplot(2,1,2);
    weather_stats = [sum(WEATHER_DATA == 1), sum(WEATHER_DATA == 0)];
    bar_h = bar(weather_stats, 0.4, 'FaceColor', [0.4, 0.6, 0.8]);
    set(gca, 'XTick', [1, 2], 'XTickLabel', {'Good Weather', 'Bad Weather'});
    ylabel('Hours');
    title('Weather Distribution', 'FontSize', 10);
    grid on;
    
    % Add percentage labels on bars
    good_pct = weather_stats(1) / sum(weather_stats) * 100;
    bad_pct = weather_stats(2) / sum(weather_stats) * 100;
    
    text(1, weather_stats(1)/2, sprintf('%.1f%%', good_pct), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'w');
    text(2, weather_stats(2)/2, sprintf('%.1f%%', bad_pct), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'w');
    
    % Add total hour count in text
    text(1, weather_stats(1)+5, sprintf('%d hrs', weather_stats(1)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
    text(2, weather_stats(2)+5, sprintf('%d hrs', weather_stats(2)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
    
    % Add informative annotation
    annotation('textbox', [0.15, 0.01, 0.7, 0.05], 'String', ...
        'Note: Installation, travel, and other offshore tasks cannot be performed during bad weather periods', ...
        'EdgeColor', 'none', 'HorizontalAlignment', 'center', 'FontSize', 8);
end