%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with disruption handling and rescheduling.
% Enhanced with dual-hull vessel method and sea condition considerations.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count
% Process times: [foundation installation, tower installation, nacelle installation, blade installation]
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours) for conventional installation
% Ship assembly times for different assembly levels
SHIP_ASSEMBLY_TIMES = struct('partial', 25, 'complete', 40); % Hours for ship assembly
% Final installation time for completely assembled turbines
FINAL_INSTALLATION_TIME = 15; % Hours for final installation of completely assembled turbines
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE SEA_CONDITIONS FINAL_INSTALLATION_TIME; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Sea condition parameters
global SEA_CONDITIONS;
SEA_CONDITIONS = struct('wave_height', [], 'wind_speed', [], 'current_speed', []);
% Generate random sea conditions (significant wave height in meters)
SEA_CONDITIONS.wave_height = 0.5 + 3.5 * rand(1, 500); % 0.5-4m wave height
% Smooth the wave heights to create realistic patterns
SEA_CONDITIONS.wave_height = smoothdata(SEA_CONDITIONS.wave_height, 'gaussian', 10);
% Wind speed (m/s)
SEA_CONDITIONS.wind_speed = 2 + 13 * rand(1, 500); % 2-15 m/s wind speed
SEA_CONDITIONS.wind_speed = smoothdata(SEA_CONDITIONS.wind_speed, 'gaussian', 10);
% Current speed (knots)
SEA_CONDITIONS.current_speed = 0.2 + 1.8 * rand(1, 500); % 0.2-2 knots current
SEA_CONDITIONS.current_speed = smoothdata(SEA_CONDITIONS.current_speed, 'gaussian', 10);

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 50;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.19;  % PSO mutation rate
INERTIA_WEIGHT = 0.78;     % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 100;    % Time of disruption (hours) - changed to occur earlier
REPAIR_TIME = 50;         % Repair time (hours)
AFFECTED_VESSEL = 3;      % Affected vessel ID

%% Data Structures
% Define turbine installation tasks with enhanced properties
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT), ...
                 'assembly_level', cell(1, TURBINE_COUNT), ... % 0=none, 1=partial, 2=complete
                 'ship_assembly_time', cell(1, TURBINE_COUNT), ...
                 'sea_condition_index', cell(1, TURBINE_COUNT), ...
                 'preassembly_time', cell(1, TURBINE_COUNT), ...
                 'is_preassembled', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;

    % Initialize assembly level to 0 (no assembly)
    % This will be set based on vessel assignment later
    turbines(i).assembly_level = 0;    % Initialize ship assembly times for different levels
    % These will be used if the turbine is assigned to a vessel that can do assembly
    turbines(i).ship_assembly_time = struct(...
        'partial', SHIP_ASSEMBLY_TIMES.partial * (0.9 + 0.2*rand()), ...
        'complete', SHIP_ASSEMBLY_TIMES.complete * (0.9 + 0.2*rand()));

    % Initialize preassembly time (default to 0, will be set during scheduling)
    turbines(i).preassembly_time = 0;

    % Initialize is_preassembled flag (default to false)
    turbines(i).is_preassembled = false;

    % Assign random sea condition index for this turbine's location
    % This will be used to look up sea conditions during installation
    turbines(i).sea_condition_index = randi([1, length(SEA_CONDITIONS.wave_height)]);
end

% Define vessels with type (single-hull or dual-hull)
% Randomly assign vessel types: 1 for single-hull, 2 for dual-hull
VESSEL_TYPES = randi([1, 2], 1, VESSEL_COUNT);
% Ensure we have at least 3 of each type
if sum(VESSEL_TYPES == 1) < 3
    idx = find(VESSEL_TYPES == 2);
    VESSEL_TYPES(idx(1:3)) = 1;
elseif sum(VESSEL_TYPES == 2) < 3
    idx = find(VESSEL_TYPES == 1);
    VESSEL_TYPES(idx(1:3)) = 2;
end

% Define vessel properties based on type
DECK_SPACE = zeros(1, VESSEL_COUNT);
STABILITY = zeros(1, VESSEL_COUNT);
SEA_CONDITION_EFFICIENCY = zeros(1, VESSEL_COUNT);
MAX_ASSEMBLY_LEVEL = zeros(1, VESSEL_COUNT);

for i = 1:VESSEL_COUNT
    if VESSEL_TYPES(i) == 1 % Single-hull vessel
        DECK_SPACE(i) = 1.0 + 0.2 * rand(); % Base deck space
        STABILITY(i) = 0.4 + 0.3 * rand(); % 0.4-0.7 stability
        SEA_CONDITION_EFFICIENCY(i) = 0.5 + 0.3 * rand(); % 0.5-0.8 efficiency
        MAX_ASSEMBLY_LEVEL(i) = 1; % Can only do partial assembly
    else % Dual-hull vessel
        DECK_SPACE(i) = 2.5 + 0.5 * rand(); % 2.5-3x deck space
        STABILITY(i) = 0.7 + 0.25 * rand(); % 0.7-0.95 stability
        SEA_CONDITION_EFFICIENCY(i) = 0.7 + 0.25 * rand(); % 0.7-0.95 efficiency
        MAX_ASSEMBLY_LEVEL(i) = 2; % Can do complete assembly
    end
end

% Define vessels with enhanced properties
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'type', num2cell(VESSEL_TYPES), ... % 1: single-hull, 2: dual-hull
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES), ...
                'deck_space', num2cell(DECK_SPACE), ... % Deck space for assembly
                'stability', num2cell(STABILITY), ... % Stability in rough seas
                'sea_efficiency', num2cell(SEA_CONDITION_EFFICIENCY), ... % Efficiency in rough seas
                'max_assembly_level', num2cell(MAX_ASSEMBLY_LEVEL)); % Maximum possible assembly level

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

% 右移重调度功能已移除

%% Complete Rescheduling
fprintf('Applying complete rescheduling...\n');
complete_schedule = completeRescheduling(initial_schedule, disruption, ...
    turbines, vessels, berths);
makespan_complete = calculateMakespan(complete_schedule);
fprintf('Complete rescheduling makespan: %.2f hours\n', makespan_complete);

%% Plot Results
% Plot Gantt charts
plotGanttChart(initial_schedule, '初始调度计划', turbines, vessels, []);
plotGanttChart(complete_schedule, '完全重调度计划', turbines, vessels, disruption);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;

    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end

    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);

    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);

    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end

    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);

    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end

    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);

    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);

            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end

        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end

        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;

        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end

        % Check diversity
        diversity = calculateDiversity(fitness);

        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);

            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);

                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);

                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);

                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));

                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end

                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;

                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);

                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end

                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);

            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};

            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end

                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end

                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end

                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end

                new_population{i} = child;
            end

            population = new_population;
        end

        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end

    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);

    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE and SEA_CONDITIONS
    global PORT_TO_FARM_DISTANCE SEA_CONDITIONS FINAL_INSTALLATION_TIME;

    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;

    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);

    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'sea_condition', [], 'assembly_level', []);

    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end

    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);

        % Get vessel properties
        vessel_type = vessels(vessel_idx).type; % 1=single-hull, 2=dual-hull
        vessel_stability = vessels(vessel_idx).stability;
        vessel_sea_efficiency = vessels(vessel_idx).sea_efficiency;
        vessel_max_assembly = vessels(vessel_idx).max_assembly_level;

        % Get sea condition for this turbine's location
        sea_idx = turbines(turbine_idx).sea_condition_index;
        wave_height = SEA_CONDITIONS.wave_height(sea_idx);
        wind_speed = SEA_CONDITIONS.wind_speed(sea_idx);
        current_speed = SEA_CONDITIONS.current_speed(sea_idx);

        % Calculate sea condition impact factor (higher means worse conditions)
        sea_impact = (wave_height/4) + (wind_speed/15) + (current_speed/2);
        sea_impact = min(max(sea_impact, 0), 1); % Normalize to 0-1

        % Apply vessel stability to reduce the impact
        effective_sea_impact = sea_impact * (1 - vessel_stability);

        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);

        % 根据船舶类型确定组装级别
        % 获取船舶类型 (1=单体船, 2=双体船)
        vessel_type = vessels(vessel_idx).type;

        if vessel_type == 1
            % 单体船不使用组装
            assembly_level = 0;
        else
            % 双体船使用完全组装
            assembly_level = 2;
        end

        % 存储组装级别
        turbines(turbine_idx).assembly_level = assembly_level;

        % Handle berth assignment for loading
        [earliest_berth_time, berth_idx] = min(berth_avail_time);

        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;

        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;

        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        schedule.sea_condition(end+1) = 0; % No sea impact at berth
        schedule.assembly_level(end+1) = assembly_level;

        % 添加船上组装过程（仅对双体船）
        if assembly_level == 2
            % 获取完全组装时间
            ship_assembly_time = turbines(turbine_idx).ship_assembly_time.complete;

            ship_assembly_start = loading_end;
            ship_assembly_end = ship_assembly_start + ship_assembly_time;

            % 添加船上组装到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = ship_assembly_start;
            schedule.end_time(end+1) = ship_assembly_end;
            schedule.process_id(end+1) = 5; % 5表示船上组装
            schedule.berth_id(end+1) = berth_idx; % 组装在泊位进行
            schedule.sea_condition(end+1) = 0; % 泊位没有海况影响
            schedule.assembly_level(end+1) = assembly_level;

            % 更新装载结束时间
            loading_end = ship_assembly_end;
        end

        % Travel to farm - affected by sea conditions
        travel_start = loading_end;
        % Adjust travel time based on sea conditions
        travel_time = port_to_farm_time(vessel_idx) * (1 + effective_sea_impact * 0.3);
        travel_end = travel_start + travel_time;

        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        schedule.sea_condition(end+1) = sea_impact;
        schedule.assembly_level(end+1) = assembly_level;

        % 处理安装任务
        current_time = travel_end;

        if assembly_level == 0 % 单体船 - 无组装
            % 根据海况调整工序时间
            time_increase = 1 + effective_sea_impact * 0.5; % 最多增加50%
            adjusted_process_times = process_times * time_increase;

            % 执行所有常规工序
            for p = 1:num_processes
                process_start = current_time;
                process_end = process_start + adjusted_process_times(p);

                % 添加工序到调度
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = process_start;
                schedule.end_time(end+1) = process_end;
                schedule.process_id(end+1) = p;
                schedule.berth_id(end+1) = 0;  % 0表示无泊位
                schedule.sea_condition(end+1) = sea_impact;
                schedule.assembly_level(end+1) = assembly_level;

                current_time = process_end;
            end
        else % 双体船 - 完全组装
            % 对于完全组装，我们用一个最终安装工序替代所有常规工序
            final_installation_time = FINAL_INSTALLATION_TIME * (0.9 + 0.2*rand());

            % 应用海况影响（但比常规工序影响小）
            final_installation_time = final_installation_time * (1 + effective_sea_impact * 0.2);

            process_start = current_time;
            process_end = process_start + final_installation_time;

            % 添加最终安装工序到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = 6; % 6表示最终安装（完全组装）
            schedule.berth_id(end+1) = 0;  % 0表示无泊位
            schedule.sea_condition(end+1) = sea_impact;
            schedule.assembly_level(end+1) = assembly_level;

            current_time = process_end;
        end

        % Travel back to port - affected by sea conditions
        travel_back_start = current_time;
        travel_back_time = port_to_farm_time(vessel_idx) * (1 + effective_sea_impact * 0.3);
        travel_back_end = travel_back_start + travel_back_time;

        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        schedule.sea_condition(end+1) = sea_impact;
        schedule.assembly_level(end+1) = assembly_level;

        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);

    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));

    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);

    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));

    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));

    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);

    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;

    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

% 右移重调度功能已移除

function complete_schedule = completeRescheduling(schedule, disruption, turbines, vessels, berths)
    % 基于未施工工序的完全重调度
    % 将未开始的工序重新调度为新的优化问题
    global SHIP_ASSEMBLY_TIMES FINAL_INSTALLATION_TIME;

    % 获取故障时间点和修复时间
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    affected_vessel = disruption.affected_vessel;

    % 1. 识别已完成的任务（结束时间 < 故障时间）
    completed_indices = find(schedule.end_time <= disruption_time);

    % 2. 识别故障时正在进行的任务
    ongoing_indices = find(schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);

    % 3. 识别还未开始的任务
    future_indices = find(schedule.start_time >= disruption_time);

    % 从原始调度计划复制已完成的任务
    complete_schedule = struct('turbine_id', schedule.turbine_id(completed_indices), ...
                              'vessel_id', schedule.vessel_id(completed_indices), ...
                              'start_time', schedule.start_time(completed_indices), ...
                              'end_time', schedule.end_time(completed_indices), ...
                              'process_id', schedule.process_id(completed_indices), ...
                              'berth_id', schedule.berth_id(completed_indices), ...
                              'sea_condition', schedule.sea_condition(completed_indices), ...
                              'assembly_level', schedule.assembly_level(completed_indices));

    % 添加维修任务到调度计划中
    repair_task_idx = length(complete_schedule.turbine_id) + 1;
    complete_schedule.turbine_id(repair_task_idx) = 0; % 0表示非特定涡轮机
    complete_schedule.vessel_id(repair_task_idx) = affected_vessel;
    complete_schedule.start_time(repair_task_idx) = disruption_time;
    complete_schedule.end_time(repair_task_idx) = disruption_time + repair_time;
    complete_schedule.process_id(repair_task_idx) = -3; % -3 表示维修过程
    complete_schedule.berth_id(repair_task_idx) = 0;  % 维修不占用泊位
    complete_schedule.sea_condition(repair_task_idx) = 0; % 维修不受海况影响
    complete_schedule.assembly_level(repair_task_idx) = 0; % 维修没有组装级别

    % 处理正在进行的任务 - 调整其结束时间
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        remaining_time = schedule.end_time(idx) - disruption_time;

        new_end_time = disruption_time;
        if schedule.vessel_id(idx) == affected_vessel
            % 如果是故障船舶的任务，需要加上修复时间
            new_end_time = new_end_time + repair_time;
        end
        new_end_time = new_end_time + remaining_time;

        % 添加到新的调度计划
        complete_schedule.turbine_id(end+1) = schedule.turbine_id(idx);
        complete_schedule.vessel_id(end+1) = schedule.vessel_id(idx);
        complete_schedule.start_time(end+1) = schedule.start_time(idx);
        complete_schedule.end_time(end+1) = new_end_time;
        complete_schedule.process_id(end+1) = schedule.process_id(idx);
        complete_schedule.berth_id(end+1) = schedule.berth_id(idx);
        complete_schedule.sea_condition(end+1) = schedule.sea_condition(idx);
        complete_schedule.assembly_level(end+1) = schedule.assembly_level(idx);
    end

    % 提取未完成任务相关的涡轮机
    remaining_turbines = [];

    % 创建已完成任务的集合，用于快速查找
    completed_tasks = zeros(length(turbines), length(turbines(1).processes) + 3); % +3 for loading, travel to/from

    % 标记已完成和正在进行的任务
    % 修复: 确保将两个索引向量转换为列向量后再连接
    all_indices = [completed_indices(:); ongoing_indices(:)];

    for i = 1:length(all_indices)
        idx = all_indices(i);
        t_id = schedule.turbine_id(idx);

        % 跳过维修任务（turbine_id为0）
        if t_id == 0
            continue;
        end

        p_id = schedule.process_id(idx);

        % 将process_id映射到正确的索引
        if p_id == 0       % 装载
            p_idx = 1;
        elseif p_id == -1  % 前往风场
            p_idx = 2;
        elseif p_id == -2  % 返回港口
            p_idx = length(turbines(1).processes) + 3;
        else               % 正常工序
            p_idx = p_id + 2;
        end

        completed_tasks(t_id, p_idx) = 1;
    end

    % 提取未完成任务相关的涡轮机
    for t = 1:length(turbines)
        for p = 1:(length(turbines(1).processes) + 3)
            if completed_tasks(t, p) == 0
                if ~ismember(t, remaining_turbines)
                    remaining_turbines = [remaining_turbines, t];
                end
                break;
            end
        end
    end

    % 修复：正确创建simplified_turbines结构体数组，确保包含所有必要字段
    simplified_turbines = struct('id', {}, 'processes', {}, 'sea_condition_index', {}, ...
                                'assembly_level', {}, 'ship_assembly_time', {});

    % 现在可以安全地添加元素
    for i = 1:length(remaining_turbines)
        t_id = remaining_turbines(i);
        % 复制所有必要的字段
        new_turbine = struct('id', turbines(t_id).id, ...
                            'processes', turbines(t_id).processes, ...
                            'sea_condition_index', turbines(t_id).sea_condition_index);

        % 复制可选字段（如果存在）
        if isfield(turbines, 'assembly_level') && isfield(turbines(t_id), 'assembly_level')
            new_turbine.assembly_level = turbines(t_id).assembly_level;
        else
            new_turbine.assembly_level = 0; % 默认为无组装
        end

        % 复制ship_assembly_time字段（如果存在）
        if isfield(turbines, 'ship_assembly_time') && isfield(turbines(t_id), 'ship_assembly_time')
            new_turbine.ship_assembly_time = turbines(t_id).ship_assembly_time;
        else
            % 创建默认的ship_assembly_time
            new_turbine.ship_assembly_time = struct(...
                'partial', SHIP_ASSEMBLY_TIMES.partial * (0.9 + 0.2*rand()), ...
                'complete', SHIP_ASSEMBLY_TIMES.complete * (0.9 + 0.2*rand()));
        end
        simplified_turbines(end+1) = new_turbine;
    end

    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, length(vessels));
    berth_avail_time = zeros(1, length(berths));

    % 更新船舶和泊位可用时间
    for i = 1:length(complete_schedule.end_time)
        v_id = complete_schedule.vessel_id(i);
        vessel_avail_time(v_id) = max(vessel_avail_time(v_id), complete_schedule.end_time(i));

        b_id = complete_schedule.berth_id(i);
        if b_id > 0
            berth_avail_time(b_id) = max(berth_avail_time(b_id), complete_schedule.end_time(i));
        end
    end

    % 特别处理故障船舶的可用时间
    vessel_avail_time(affected_vessel) = max(vessel_avail_time(affected_vessel), disruption_time + repair_time);

    % 如果还有未完成的任务，调用hybridGAPSO进行优化
    if ~isempty(simplified_turbines)
        % 创建新的优化问题，并设置起始时间为disruption_time
        remaining_schedule = generateRemainingSchedule(simplified_turbines, vessels, berths, ...
                                                     vessel_avail_time, disruption_time);

        % 合并已完成/正在进行的任务和新优化的任务
        complete_schedule.turbine_id = [complete_schedule.turbine_id, remaining_schedule.turbine_id];
        complete_schedule.vessel_id = [complete_schedule.vessel_id, remaining_schedule.vessel_id];
        complete_schedule.start_time = [complete_schedule.start_time, remaining_schedule.start_time];
        complete_schedule.end_time = [complete_schedule.end_time, remaining_schedule.end_time];
        complete_schedule.process_id = [complete_schedule.process_id, remaining_schedule.process_id];
        complete_schedule.berth_id = [complete_schedule.berth_id, remaining_schedule.berth_id];
        complete_schedule.sea_condition = [complete_schedule.sea_condition, remaining_schedule.sea_condition];
        complete_schedule.assembly_level = [complete_schedule.assembly_level, remaining_schedule.assembly_level];
    end
end
function remaining_schedule = generateRemainingSchedule(turbines, vessels, berths, vessel_avail_time, start_time)
    % 使用修改后的hybridGAPSO为剩余任务生成调度计划

    % 获取问题规模
    turbine_count = length(turbines);
    vessel_count = length(vessels);

    % 获取全局参数
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD...
           SHIP_ASSEMBLY_TIMES FINAL_INSTALLATION_TIME;

    % 定义较小的种群规模和迭代次数，加快重调度速度
    local_pop_size = max(20, min(POP_SIZE, 30));
    local_max_gen = max(50, min(MAX_GEN, 80));

    % 初始化种群
    population = cell(1, local_pop_size);
    fitness = zeros(1, local_pop_size);

    % 生成初始种群
    for i = 1:local_pop_size
        % 涡轮机安装顺序随机排列
        turbine_order = randperm(turbine_count);
        % 船舶分配随机选择
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end

    % 初始化PSO参数
    pbest = population;
    pbest_fitness = Inf(1, local_pop_size);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, local_pop_size);

    % 初始化速度
    for i = 1:local_pop_size
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end

    % 主循环
    for gen = 1:local_max_gen
        % 评估种群
        for i = 1:local_pop_size
            % 将染色体解码为调度计划，考虑船舶初始可用时间
            schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                turbines, vessels, berths, vessel_avail_time, start_time);
            % 计算工期
            fitness(i) = calculateMakespan(schedule_i);

            % 更新个体最优
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end

        % 更新全局最优
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end

        % 监控收敛性
        if mod(gen, 10) == 0
            fprintf('重调度Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end

        % 计算种群多样性
        diversity = calculateDiversity(fitness);

        % 如果多样性低于阈值，应用PSO
        if diversity < DIVERSITY_THRESHOLD
            fprintf('重调度过程中检测到低多样性，在第%d代应用PSO...\n', gen);

            % 应用PSO进行局部搜索
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:local_pop_size
                    % 更新速度
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);

                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);

                    % 更新位置
                    % 对于涡轮机顺序（排列），使用特殊方法
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % 确保它仍然是有效的排列
                    [~, new_turbine_order] = sort(new_turbine_order);

                    % 对于船舶分配
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % 确保有效（在1和vessel_count之间）
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));

                    % 以较高概率应用变异
                    if rand() < MUTATION_RATE_PSO
                        % 在涡轮机顺序中交换两个位置
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end

                    % 更新种群
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;

                    % 评估新解
                    schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                        turbines, vessels, berths, vessel_avail_time, start_time);
                    fitness(i) = calculateMakespan(schedule_i);

                    % 更新个体最优
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end

                % 更新全局最优
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA操作（选择、交叉、变异）
            new_population = cell(1, local_pop_size);

            % 精英选择 - 保留最佳解
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};

            % 对剩余个体进行选择、交叉和变异
            for i = 2:local_pop_size
                % 锦标赛选择
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end

                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end

                % 交叉
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end

                % 变异
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end

                new_population{i} = child;
            end

            population = new_population;
        end

        % 检查终止条件
        if gen >= local_max_gen
            break;
        end
    end

    % 将最佳解转换为调度计划
    remaining_schedule = decodeChromosomeWithStartTime(gbest, ...
        turbines, vessels, berths, vessel_avail_time, start_time);
end

function schedule = decodeChromosomeWithStartTime(chromosome, turbines, vessels, berths, vessel_avail_time, start_time)
    % 与decodeChromosome类似，但考虑初始可用时间
    global PORT_TO_FARM_DISTANCE SEA_CONDITIONS SHIP_ASSEMBLY_TIMES FINAL_INSTALLATION_TIME;

    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;

    % 初始化调度数据结构
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);

    % 初始化泊位可用时间
    berth_avail_time = ones(1, berth_count) * start_time;

    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'sea_condition', [], 'assembly_level', []);

    % 港口到风场的航行时间
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end

    % 处理每个涡轮机
    for i = 1:turbine_count
        turbine_idx = turbines(i).id;  % 使用正确的涡轮机ID
        vessel_idx = vessel_assignment(i);

        % 获取船舶属性
        vessel_type = vessels(vessel_idx).type; % 1=单体船，2=双体船
        vessel_stability = vessels(vessel_idx).stability;
        vessel_sea_efficiency = vessels(vessel_idx).sea_efficiency;
        vessel_max_assembly = vessels(vessel_idx).max_assembly_level;

        % 获取此涡轮机位置的海况
        sea_idx = turbines(i).sea_condition_index;
        wave_height = SEA_CONDITIONS.wave_height(sea_idx);
        wind_speed = SEA_CONDITIONS.wind_speed(sea_idx);
        current_speed = SEA_CONDITIONS.current_speed(sea_idx);

        % 计算海况影响因子（越高表示条件越差）
        sea_impact = (wave_height/4) + (wind_speed/15) + (current_speed/2);
        sea_impact = min(max(sea_impact, 0), 1); % 归一化到0-1

        % 应用船舶稳定性来减少影响
        effective_sea_impact = sea_impact * (1 - vessel_stability);

        % 获取此涡轮机的处理时间
        process_times = turbines(i).processes;  % 使用turbines(i)而不是turbine_idx
        num_processes = length(process_times);

        % 根据船舶类型确定组装级别
        % 获取船舶类型 (1=单体船, 2=双体船)
        vessel_type = vessels(vessel_idx).type;

        if vessel_type == 1
            % 单体船不使用组装
            assembly_level = 0;
        else
            % 双体船使用完全组装
            assembly_level = 2;
        end

        % 存储组装级别
        turbines(i).assembly_level = assembly_level;

        % 处理泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);

        % 计算装载开始时间
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;

        % 更新泊位可用性
        berth_avail_time(berth_idx) = loading_end;

        % 添加装载操作到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0表示装载
        schedule.berth_id(end+1) = berth_idx;
        schedule.sea_condition(end+1) = 0; % 泊位没有海况影响
        schedule.assembly_level(end+1) = assembly_level;

        % 添加船上组装过程（仅对双体船）
        if assembly_level == 2
            % 获取完全组装时间
            ship_assembly_time = turbines(i).ship_assembly_time.complete;

            ship_assembly_start = loading_end;
            ship_assembly_end = ship_assembly_start + ship_assembly_time;

            % 添加船上组装到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = ship_assembly_start;
            schedule.end_time(end+1) = ship_assembly_end;
            schedule.process_id(end+1) = 5; % 5表示船上组装
            schedule.berth_id(end+1) = berth_idx; % 组装在泊位进行
            schedule.sea_condition(end+1) = 0; % 泊位没有海况影响
            schedule.assembly_level(end+1) = assembly_level;

            % 更新装载结束时间
            loading_end = ship_assembly_end;
        end

        % 前往风场 - 受海况影响
        travel_start = loading_end;
        % 根据海况调整航行时间
        travel_time = port_to_farm_time(vessel_idx) * (1 + effective_sea_impact * 0.3);
        travel_end = travel_start + travel_time;

        % 添加航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1表示前往风场
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        schedule.sea_condition(end+1) = sea_impact;
        schedule.assembly_level(end+1) = assembly_level;

        % 处理安装任务
        current_time = travel_end;

        if assembly_level == 0 % 单体船 - 无组装
            % 根据海况调整工序时间
            time_increase = 1 + effective_sea_impact * 0.5; % 最多增加50%
            adjusted_process_times = process_times * time_increase;

            % 执行所有常规工序
            for p = 1:num_processes
                process_start = current_time;
                process_end = process_start + adjusted_process_times(p);

                % 添加工序到调度
                schedule.turbine_id(end+1) = turbine_idx;
                schedule.vessel_id(end+1) = vessel_idx;
                schedule.start_time(end+1) = process_start;
                schedule.end_time(end+1) = process_end;
                schedule.process_id(end+1) = p;
                schedule.berth_id(end+1) = 0;  % 0表示无泊位
                schedule.sea_condition(end+1) = sea_impact;
                schedule.assembly_level(end+1) = assembly_level;

                current_time = process_end;
            end
        else % 双体船 - 完全组装
            % 对于完全组装，我们用一个最终安装工序替代所有常规工序
            final_installation_time = FINAL_INSTALLATION_TIME * (0.9 + 0.2*rand());

            % 应用海况影响（但比常规工序影响小）
            final_installation_time = final_installation_time * (1 + effective_sea_impact * 0.2);

            process_start = current_time;
            process_end = process_start + final_installation_time;

            % 添加最终安装工序到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = 6; % 6表示最终安装（完全组装）
            schedule.berth_id(end+1) = 0;  % 0表示无泊位
            schedule.sea_condition(end+1) = sea_impact;
            schedule.assembly_level(end+1) = assembly_level;

            current_time = process_end;
        end

        % 返回港口 - 受海况影响
        travel_back_start = current_time;
        travel_back_time = port_to_farm_time(vessel_idx) * (1 + effective_sea_impact * 0.3);
        travel_back_end = travel_back_start + travel_back_time;

        % 添加返回航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2表示返回港口
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        schedule.sea_condition(end+1) = sea_impact;
        schedule.assembly_level(end+1) = assembly_level;

        % 更新船舶可用性
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % Create a professional-looking Gantt chart
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');

    % Check if the schedule is empty
    if isempty(schedule) || isempty(schedule.turbine_id)
        text(0.5, 0.5, '没有调度数据可显示', 'HorizontalAlignment', 'center', 'FontSize', 14);
        title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
        return;
    end

    % 使用柔和的颜色调色板
    process_colors = [
        0.9290, 0.6940, 0.3250;  % 装载 (process 0) - 柔和橙色
        0.4660, 0.7740, 0.8880;  % 前往风场 (process -1) - 柔和蓝色
        0.6350, 0.5040, 0.7410;  % 基础安装 (process 1) - 柔和紫色
        0.4660, 0.7410, 0.3880;  % 塔筒安装 (process 2) - 柔和绿色
        0.8500, 0.3250, 0.3980;  % 机舱安装 (process 3) - 柔和红色
        0.9290, 0.6940, 0.1250;  % 叶片安装 (process 4) - 柔和黄色
        0.4660, 0.7740, 0.8880;  % 返回港口 (process -2) - 柔和蓝色
        1.0000, 0.3000, 0.3000;  % 维修 (process -3) - 醒目但不太亮的红色
        0.3010, 0.7450, 0.5330;  % 船上组装 (process 5) - 青色
        0.4940, 0.1840, 0.5560;  % 最终安装 (process 6) - 紫色
    ];

    % 工序名称（用于图例）
    process_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', '叶片安装', '返回港口', '维修', '船上组装', '最终安装'};

    % Create dummy objects for the legend
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end

    % Create dummy objects for vessel type legend
    vessel_type_handles = zeros(1, 2);
    vessel_type_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '-');
    vessel_type_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '--');
    vessel_type_names = {'单体船', '双体船'};

    % Create dummy objects for sea condition legend
    sea_condition_handles = zeros(1, 3);
    sea_condition_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', [0, 0.7, 0], 'LineStyle', '-');
    sea_condition_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', [0.9, 0.6, 0], 'LineStyle', '-');
    sea_condition_handles(3) = plot(NaN, NaN, 'LineWidth', 2, 'Color', [0.8, 0, 0], 'LineStyle', '-');
    sea_condition_names = {'良好海况', '中等海况', '恶劣海况'};

    % 创建组装级别图例的虚拟对象
    assembly_level_handles = zeros(1, 3);
    assembly_level_handles(1) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', '-');
    assembly_level_handles(2) = plot(NaN, NaN, 'LineWidth', 2, 'Color', 'k', 'LineStyle', ':');
    assembly_level_handles(3) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '-');
    assembly_level_names = {'无组装', '部分组装', '完全组装'};

    % Draw vessel timelines
    hold on;

    % If there is a disruption, add a disruption marker line
    if ~isempty(disruption) && isstruct(disruption) && isfield(disruption, 'time')
        % Draw a vertical line at the time of disruption
        line([disruption.time, disruption.time], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);

        % Add disruption marker text
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end

    % Pre-identify repair tasks
    repair_indices = [];
    affected_vessel = -1;

    if ~isempty(disruption) && isstruct(disruption)
        % Try to find repair tasks based on turbine_id = 0 (no specific turbine)
        repair_by_turbine = find(schedule.turbine_id == 0);

        % Try to find repair tasks based on process_id = -3
        repair_by_process = find(schedule.process_id == -3);

        % Combine the results, prioritizing process_id = -3
        if ~isempty(repair_by_process)
            repair_indices = repair_by_process;
        elseif ~isempty(repair_by_turbine)
            repair_indices = repair_by_turbine;
        end

        % If we have a disruption structure with affected_vessel field
        if isfield(disruption, 'affected_vessel') && ~isempty(disruption.affected_vessel)
            affected_vessel = disruption.affected_vessel;

            % If we still don't have repair tasks, try to find them by vessel and time
            if isempty(repair_indices) && affected_vessel > 0
                % Find tasks for the affected vessel near the disruption time
                potential_repair = find(schedule.vessel_id == affected_vessel & ...
                                       abs(schedule.start_time - disruption.time) < 5);
                if ~isempty(potential_repair)
                    repair_indices = [repair_indices, potential_repair];
                    repair_indices = unique(repair_indices); % Remove duplicates
                end
            end
        end
    end

    % Process tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        if isempty(vessel_tasks)
            continue;  % Skip if no tasks for this vessel
        end

        y_pos = v;

        % Sort tasks by start time to ensure proper sequence
        [~, sorted_idx] = sort(schedule.start_time(vessel_tasks));
        vessel_tasks = vessel_tasks(sorted_idx);

        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;

            % Skip zero-duration tasks
            if duration <= 0
                continue;
            end

            % Determine if this is a repair task using multiple criteria
            is_repair = false;

            % Check if this task index is in our pre-identified repair indices
            if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                is_repair = true;
            end

            % Check if process_id indicates repair
            if schedule.process_id(task_idx) == -3
                is_repair = true;
            end

            % Check if this is a zero turbine_id task for affected vessel
            if schedule.turbine_id(task_idx) == 0 && v == affected_vessel
                is_repair = true;
            end

            % Check if this task starts at or very near the disruption time on the affected vessel
            if ~isempty(disruption) && isfield(disruption, 'time') && ...
               v == affected_vessel && abs(start_time - disruption.time) < 2
                is_repair = true;
            end

            % Set color and label based on task type
            if is_repair
                % Force this task to be marked as repair
                color_idx = 8;  % Repair color
                label_text = '维修';
            else
                % Determine regular process type
                process_id = schedule.process_id(task_idx);

                % Map process_id to color index
                if process_id == 0      % Loading
                    color_idx = 1;
                    process_label = 'L';
                elseif process_id == -1  % Travel to farm
                    color_idx = 2;
                    process_label = 'TF';
                elseif process_id == -2  % Return to port
                    color_idx = 7;
                    process_label = 'TB';
                elseif process_id == 5   % 船上组装
                    color_idx = 9;      % 使用船上组装颜色
                    process_label = 'SA';
                elseif process_id == 6   % 最终安装（完全组装）
                    color_idx = 10;     % 使用最终安装颜色
                    process_label = 'FI';
                elseif process_id > 0 && process_id <= 4  % Regular process
                    color_idx = min(process_id + 2, size(process_colors, 1)); % Prevent index out of bounds
                    process_label = ['P', num2str(process_id)];
                else                    % Handle unexpected cases
                    color_idx = 1;      % Default to loading color
                    process_label = '?';
                end

                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                else
                    % If turbine_id is 0 but not repair, just show process label
                    label_text = process_label;
                end
            end

            % Get assembly level if available
            assembly_level = 0; % Default: no assembly
            if isfield(schedule, 'assembly_level') && length(schedule.assembly_level) >= task_idx
                assembly_level = schedule.assembly_level(task_idx);
            end

            % 根据组装级别绘制任务条
            if assembly_level == 0 % 无组装 - 普通边框
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
            elseif assembly_level == 1 % 部分组装 - 虚线边框
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5, 'LineStyle', ':');
            else % 完全组装 - 粗边框
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(color_idx,:), ...
                    'EdgeColor', 'k', 'LineWidth', 1.5);
            end

            % Add sea condition indicator if available
            if isfield(schedule, 'sea_condition') && length(schedule.sea_condition) >= task_idx
                sea_impact = schedule.sea_condition(task_idx);
                if sea_impact > 0 % Only show for tasks with sea impact
                    % Determine sea condition color (green for good, yellow for medium, red for bad)
                    if sea_impact < 0.33
                        sea_color = [0, 0.7, 0]; % Green
                    elseif sea_impact < 0.66
                        sea_color = [0.9, 0.6, 0]; % Yellow/Orange
                    else
                        sea_color = [0.8, 0, 0]; % Red
                    end

                    % Draw a small line at the top of the task bar to indicate sea condition
                    line([start_time, start_time + duration], [y_pos+0.4, y_pos+0.4], ...
                        'Color', sea_color, 'LineWidth', 2);
                end
            end

            % 使用统一的较小字体大小
            font_size = 7;

            % For very short durations, use rotated or external labels
            if duration < 5
                % External label with line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw small line connecting to block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end

        % Add vessel label on y-axis with vessel type
        % If this is the affected vessel, add marker
        vessel_type_str = '';
        vessel_deck_str = '';
        if isfield(vessels, 'type')
            if vessels(v).type == 1
                vessel_type_str = '单体船';
            else
                vessel_type_str = '双体船';
            end

            % Add deck space info
            if isfield(vessels, 'deck_space')
                vessel_deck_str = sprintf('甲板%.1f', vessels(v).deck_space);
            end
        end

        if v == affected_vessel
            text(-20, y_pos, sprintf('船舶 %d (%s, %s, 故障)', v, vessel_type_str, vessel_deck_str), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r', 'FontSize', 8);
        else
            text(-20, y_pos, sprintf('船舶 %d (%s, %s)', v, vessel_type_str, vessel_deck_str), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 8);
        end
    end

    % Add berth timelines - ensure no repair tasks are displayed
    berth_count = max(max(schedule.berth_id), 0);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;  % Skip if no tasks for this berth
            end

            y_pos = y_start + b;

            % Sort by start time
            [~, sorted_idx] = sort(schedule.start_time(berth_tasks));
            berth_tasks = berth_tasks(sorted_idx);

            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);

                % Skip repair tasks in berth display
                if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                    continue;
                end

                if schedule.process_id(task_idx) == -3
                    continue;
                end

                if schedule.turbine_id(task_idx) == 0 && ...
                   (schedule.vessel_id(task_idx) == affected_vessel || ...
                    (~isempty(disruption) && isfield(disruption, 'time') && ...
                    abs(schedule.start_time(task_idx) - disruption.time) < 2))
                    continue;
                end

                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;

                % Skip zero-duration tasks
                if duration <= 0
                    continue;
                end

                % Draw task bar (loading is always process_id 0)
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);

                % 使用统一的较小字体大小
                font_size = 7;

                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                else
                    label_text = 'L';
                end

                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end

            % Add berth label on y-axis
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 8);
        end
    end

    % 添加图例，包括工序、船舶类型、海况和组装级别
    % 创建组合图例
    all_handles = [dummy_handles, vessel_type_handles, sea_condition_handles, assembly_level_handles];
    all_names = [process_names, vessel_type_names, sea_condition_names, assembly_level_names];

    % Create the legend with a smaller font size to fit all items
    legend(all_handles, all_names, 'Location', 'southoutside', 'Orientation', 'horizontal', 'FontSize', 6);

    % Set plot properties
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    % No y-axis label needed as we have custom labels

    % Calculate and display makespan
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 10, 'FontWeight', 'bold');

    % Calculate and display statistics about vessel types and assembly levels
    if isfield(vessels, 'type') && isfield(schedule, 'assembly_level')
        % Find single-hull and dual-hull vessels
        single_hull_indices = find([vessels.type] == 1);
        dual_hull_indices = find([vessels.type] == 2);

        % 初始化不同船舶类型的数组
        single_hull_times = [];
        dual_hull_times = [];

        % 收集按船舶类型的安装时间
        for i = 1:length(schedule.process_id)
            % 考虑安装工序（常规工序和最终安装）
            if (schedule.process_id(i) > 0 && schedule.process_id(i) <= 4) || schedule.process_id(i) == 6
                vessel_idx = schedule.vessel_id(i);
                process_time = schedule.end_time(i) - schedule.start_time(i);

                % 按船舶类型收集
                if ismember(vessel_idx, single_hull_indices)
                    single_hull_times = [single_hull_times, process_time];
                elseif ismember(vessel_idx, dual_hull_indices)
                    dual_hull_times = [dual_hull_times, process_time];
                end
            end
        end

        % 计算平均时间（如果数组不为空）
        if ~isempty(single_hull_times)
            avg_single_hull = mean(single_hull_times);
        else
            avg_single_hull = NaN;
        end

        if ~isempty(dual_hull_times)
            avg_dual_hull = mean(dual_hull_times);
        else
            avg_dual_hull = NaN;
        end

        % Display statistics
        y_offset = length(vessels) + berth_count + 3;

        % Display vessel type statistics
        if ~isnan(avg_single_hull)
            text(20, y_offset, ...
                sprintf('单体船平均工序时间: %.2f 小时', avg_single_hull), ...
                'FontSize', 8);
        else
            text(20, y_offset, ...
                '单体船平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        if ~isnan(avg_dual_hull)
            text(20, y_offset + 1, ...
                sprintf('双体船平均工序时间: %.2f 小时', avg_dual_hull), ...
                'FontSize', 8);
        else
            text(20, y_offset + 1, ...
                '双体船平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        % 只有当两种船舶都有有效数据且单体船时间不为0时才计算效率提升
        if ~isnan(avg_single_hull) && ~isnan(avg_dual_hull) && avg_single_hull > 0
            text(20, y_offset + 2, ...
                sprintf('双体船完全组装效率提升: %.2f%%', (1 - avg_dual_hull/avg_single_hull) * 100), ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isnan(avg_dual_hull) && isnan(avg_single_hull)
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无单体船数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif isnan(avg_dual_hull) && ~isnan(avg_single_hull)
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无双体船数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        else
            text(20, y_offset + 2, ...
                '双体船完全组装效率提升: 无足够数据计算', ...
                'FontSize', 8, 'FontWeight', 'bold');
        end

        % 不再显示涡轮机数量

        % Display sea condition impact
        good_sea_times = [];
        bad_sea_times = [];

        for i = 1:length(schedule.process_id)
            if (schedule.process_id(i) > 0 && schedule.process_id(i) <= 4) || schedule.process_id(i) == 6
                process_time = schedule.end_time(i) - schedule.start_time(i);
                sea_impact = schedule.sea_condition(i);

                if sea_impact < 0.33
                    good_sea_times = [good_sea_times, process_time];
                elseif sea_impact > 0.66
                    bad_sea_times = [bad_sea_times, process_time];
                end
            end
        end

        % 计算平均时间（如果数组不为空）
        if ~isempty(good_sea_times)
            avg_good_sea = mean(good_sea_times);
        else
            avg_good_sea = NaN;
        end

        if ~isempty(bad_sea_times)
            avg_bad_sea = mean(bad_sea_times);
        else
            avg_bad_sea = NaN;
        end

        % 显示海况统计信息
        if ~isnan(avg_good_sea)
            text(800, y_offset, ...
                sprintf('良好海况平均工序时间: %.2f 小时', avg_good_sea), ...
                'FontSize', 8);
        elseif ~isempty(good_sea_times)
            text(800, y_offset, ...
                '良好海况平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        if ~isnan(avg_bad_sea)
            text(800, y_offset + 1, ...
                sprintf('恶劣海况平均工序时间: %.2f 小时', avg_bad_sea), ...
                'FontSize', 8);
        elseif ~isempty(bad_sea_times)
            text(800, y_offset + 1, ...
                '恶劣海况平均工序时间: 无数据', ...
                'FontSize', 8);
        end

        % 显示海况影响
        if ~isnan(avg_good_sea) && ~isnan(avg_bad_sea) && avg_good_sea > 0
            text(800, y_offset + 2, ...
                sprintf('海况影响: %.2f%%', (avg_bad_sea/avg_good_sea - 1) * 100), ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isnan(avg_bad_sea) && isnan(avg_good_sea)
            text(800, y_offset + 2, ...
                '海况影响: 无良好海况数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif isnan(avg_bad_sea) && ~isnan(avg_good_sea)
            text(800, y_offset + 2, ...
                '海况影响: 无恶劣海况数据用于比较', ...
                'FontSize', 8, 'FontWeight', 'bold');
        elseif ~isempty(good_sea_times) || ~isempty(bad_sea_times)
            text(800, y_offset + 2, ...
                '海况影响: 无足够数据计算', ...
                'FontSize', 8, 'FontWeight', 'bold');
        end
    end

    % Set axis limits - corrected part to ensure display of critical time range
    % Find critical time points
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Show tasks before and after disruption
        start_view = max(0, disruption.time - 100); % Show 100 hours before disruption
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % For initial schedule, maintain original view
        xlim([0, makespan * 1.05]);
    end

    % 确保合理的y轴范围，为统计信息留出额外空间
    if isfield(vessels, 'type') && isfield(schedule, 'assembly_level')
        ylim([0, length(vessels) + max(berth_count, 1) + 8]); % 为详细统计信息留出额外空间
    elseif isfield(vessels, 'type')
        ylim([0, length(vessels) + max(berth_count, 1) + 6]); % 为船舶类型统计信息留出额外空间
    else
        ylim([0, length(vessels) + max(berth_count, 1) + 3]);
    end

    % Add clearer time grid lines
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Add grid lines near disruption time
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % Major grid line every 20 hours
    end

    set(gca, 'YTick', []);  % Remove y-tick labels as we have custom labels

    hold off;
end