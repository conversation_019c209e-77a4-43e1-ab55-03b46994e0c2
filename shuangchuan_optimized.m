%% 优化的单体船 vs 双体船效率对比分析
% 考虑实际项目规模和拼接策略优化

clear all;
close all;
clc;
rng(42);

%% 问题参数设置
TURBINE_COUNT = 100;       % 风机总数
SINGLE_VESSEL_COUNT = 50;  % 单体船数量
DUAL_VESSEL_COUNT = 25;    % 双体船数量
BERTH_COUNT = 10;          % 泊位数量

% 风机类型参数（全部大型风机）
TURBINE_POWER = 8.0;
TURBINE_PROCESS_TIMES = [28, 18, 14, 55]; % 更复杂的大型风机安装

% 船舶参数
SHIP_ASSEMBLY_TIME = 32;   % 船上组装时间
FINAL_INSTALLATION_TIME = 10; % 最终安装时间（体现双体船优势）
WELDING_TIME = 4;          % 优化的拼接时间（并行拼接）

% 距离和速度
PORT_TO_FARM_DISTANCE = 80;
VESSEL_SPEED = 15;
LOADING_TIME = 6;

%% 数据初始化
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'type', num2cell(ones(1, TURBINE_COUNT) * 2), ...
                 'power', num2cell(ones(1, TURBINE_COUNT) * TURBINE_POWER), ...
                 'processes', cell(1, TURBINE_COUNT));

for i = 1:TURBINE_COUNT
    variation = 0.9 + 0.2*rand(1, length(TURBINE_PROCESS_TIMES));
    turbines(i).processes = TURBINE_PROCESS_TIMES .* variation;
end

single_vessels = struct('id', num2cell(1:SINGLE_VESSEL_COUNT), ...
                       'type', num2cell(ones(1, SINGLE_VESSEL_COUNT)), ...
                       'speed', num2cell(ones(1, SINGLE_VESSEL_COUNT) * VESSEL_SPEED), ...
                       'loading_time', num2cell(ones(1, SINGLE_VESSEL_COUNT) * LOADING_TIME));

dual_vessels = struct('id', num2cell(1:DUAL_VESSEL_COUNT), ...
                     'type', num2cell(ones(1, DUAL_VESSEL_COUNT) * 2), ...
                     'speed', num2cell(ones(1, DUAL_VESSEL_COUNT) * VESSEL_SPEED), ...
                     'loading_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * LOADING_TIME), ...
                     'welding_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * WELDING_TIME));

berths = struct('id', num2cell(1:BERTH_COUNT));

%% 场景1：单体船调度
fprintf('=== 场景1：50条单体船运送100台风机 ===\n');
single_schedule = generateOptimizedSingleSchedule(turbines, single_vessels, berths);
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时\n', single_makespan);

%% 场景2：双体船调度（优化拼接策略）
fprintf('\n=== 场景2：25条双体船运送100台风机 ===\n');
dual_schedule = generateOptimizedDualSchedule(turbines, dual_vessels, berths);
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时\n', dual_makespan);

%% 详细分析
analyzeResults(single_schedule, dual_schedule, single_makespan, dual_makespan);

%% 绘制对比图
plotOptimizedComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                       single_makespan, dual_makespan);

%% 优化的单体船调度函数
function schedule = generateOptimizedSingleSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    travel_time = 80 / 15; % 港口到风场时间
    
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 1);
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 1);
        
        % 安装工序
        current_time = travel_end;
        process_times = turbines(turbine_idx).processes;
        
        for p = 1:length(process_times)
            process_start = current_time;
            process_end = process_start + process_times(p);
            schedule = addTask(schedule, turbine_idx, vessel_idx, process_start, process_end, p, 0, 1);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 1);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 优化的双体船调度函数
function schedule = generateOptimizedDualSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 优化拼接策略：并行拼接，减少总时间
    welding_time = vessels(1).welding_time;
    for v = 1:vessel_count
        vessel_avail_time(v) = welding_time;
        schedule = addTask(schedule, 0, v, 0, welding_time, -3, 0, 2);
    end
    
    travel_time = 80 / 15;
    
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 2);
        
        % 船上组装
        assembly_start = loading_end;
        assembly_end = assembly_start + 32; % SHIP_ASSEMBLY_TIME
        berth_avail_time(berth_idx) = assembly_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, assembly_start, assembly_end, 5, berth_idx, 2);
        
        % 前往风场
        travel_start = assembly_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 2);
        
        % 最终安装
        final_start = travel_end;
        final_end = final_start + 10; % FINAL_INSTALLATION_TIME
        schedule = addTask(schedule, turbine_idx, vessel_idx, final_start, final_end, 6, 0, 2);
        
        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 2);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 添加任务函数
function schedule = addTask(schedule, turbine_id, vessel_id, start_time, end_time, process_id, berth_id, vessel_type)
    schedule.turbine_id(end+1) = turbine_id;
    schedule.vessel_id(end+1) = vessel_id;
    schedule.start_time(end+1) = start_time;
    schedule.end_time(end+1) = end_time;
    schedule.process_id(end+1) = process_id;
    schedule.berth_id(end+1) = berth_id;
    schedule.vessel_type(end+1) = vessel_type;
end

%% 结果分析函数
function analyzeResults(single_schedule, dual_schedule, single_makespan, dual_makespan)
    fprintf('\n=== 详细时间分析 ===\n');
    
    % 单体船分析
    s_loading = sum((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
    s_travel = sum((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time));
    s_install = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time));
    
    fprintf('单体船时间构成：\n');
    fprintf('  装载时间：%.2f 小时\n', s_loading);
    fprintf('  航行时间：%.2f 小时\n', s_travel);
    fprintf('  安装时间：%.2f 小时\n', s_install);
    
    % 双体船分析
    d_welding = sum((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_loading = sum((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_assembly = sum((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_travel = sum((dual_schedule.process_id == -1 | dual_schedule.process_id == -2) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_install = sum((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));
    
    fprintf('\n双体船时间构成：\n');
    fprintf('  拼接时间：%.2f 小时\n', d_welding);
    fprintf('  装载时间：%.2f 小时\n', d_loading);
    fprintf('  船上组装：%.2f 小时\n', d_assembly);
    fprintf('  航行时间：%.2f 小时\n', d_travel);
    fprintf('  最终安装：%.2f 小时\n', d_install);
    
    % 效率分析
    fprintf('\n=== 效率对比分析 ===\n');
    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;
    
    if efficiency > 0
        fprintf('双体船效率提升：%.2f%%\n', efficiency);
        fprintf('节省时间：%.2f 小时\n', single_makespan - dual_makespan);
    else
        fprintf('单体船效率更高：%.2f%%\n', -efficiency);
        fprintf('双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
    end
    
    % 安装效率对比
    install_efficiency = (s_install - d_install) / s_install * 100;
    fprintf('\n关键优势分析：\n');
    fprintf('双体船安装时间节省：%.2f%% (%.2f vs %.2f 小时)\n', ...
        install_efficiency, s_install, d_install);
    
    % 拼接成本分析
    fprintf('拼接时间成本：%.2f 小时 (占总工期的%.2f%%)\n', ...
        d_welding, d_welding/dual_makespan*100);
end

%% 优化的甘特图绘制函数
function plotOptimizedComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                                single_makespan, dual_makespan)

    figure('Position', [50, 50, 1400, 900], 'Color', 'white');

    % 颜色定义
    colors = [
        0.9290, 0.6940, 0.3250;  % 装载
        0.4660, 0.7740, 0.8880;  % 前往风场
        0.6350, 0.5040, 0.7410;  % 基础安装
        0.4660, 0.7410, 0.3880;  % 塔筒安装
        0.8500, 0.3250, 0.0980;  % 机舱安装
        0.9290, 0.6940, 0.1250;  % 叶片安装
        0.3010, 0.7450, 0.9330;  % 船上组装
        0.6350, 0.0780, 0.1840;  % 最终安装
        0.5000, 0.5000, 0.5000;  % 返回港口
        0.7500, 0.7500, 0.7500;  % 拼接焊接
    ];

    %% 单体船甘特图
    subplot(2, 1, 1);
    hold on;

    % 只显示前10条船避免过于拥挤
    display_vessels = min(10, length(single_vessels));

    for i = 1:length(single_schedule.turbine_id)
        vessel_id = single_schedule.vessel_id(i);
        if vessel_id <= display_vessels
            start_time = single_schedule.start_time(i);
            end_time = single_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = single_schedule.process_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 0.5);

            if single_schedule.turbine_id(i) > 0 && duration > 8
                text(start_time + duration/2, vessel_id, sprintf('T%d', single_schedule.turbine_id(i)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 7, 'FontWeight', 'bold');
            end
        end
    end

    title(sprintf('单体船调度方案 - 总工期: %.2f小时 (显示前%d条船)', single_makespan, display_vessels), ...
        'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, display_vessels + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 双体船甘特图
    subplot(2, 1, 2);
    hold on;

    display_dual_vessels = min(10, length(dual_vessels));

    for i = 1:length(dual_schedule.turbine_id)
        vessel_id = dual_schedule.vessel_id(i);
        if vessel_id <= display_dual_vessels
            start_time = dual_schedule.start_time(i);
            end_time = dual_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = dual_schedule.process_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 0.5);

            if dual_schedule.turbine_id(i) > 0 && duration > 8
                text(start_time + duration/2, vessel_id, sprintf('T%d', dual_schedule.turbine_id(i)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 7, 'FontWeight', 'bold');
            elseif process_id == -3 && duration > 2
                text(start_time + duration/2, vessel_id, '拼接', ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 7, 'FontWeight', 'bold');
            end
        end
    end

    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;
    if efficiency > 0
        title_text = sprintf('双体船调度方案 - 总工期: %.2f小时 (效率提升%.2f%%, 显示前%d条船)', ...
            dual_makespan, efficiency, display_dual_vessels);
    else
        title_text = sprintf('双体船调度方案 - 总工期: %.2f小时 (效率降低%.2f%%, 显示前%d条船)', ...
            dual_makespan, -efficiency, display_dual_vessels);
    end
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, display_dual_vessels + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 图例
    legend_handles = [];
    legend_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', ...
                   '叶片安装', '船上组装', '最终安装', '返回港口', '拼接焊接'};

    for i = 1:length(legend_names)
        legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 8, 'Color', colors(i,:));
    end

    legend(legend_handles, legend_names, 'Location', 'southoutside', ...
        'Orientation', 'horizontal', 'FontSize', 8, 'NumColumns', 5);

    sgtitle('单体船 vs 双体船效率对比分析（优化版）', 'FontSize', 16, 'FontWeight', 'bold');

    hold off;
end

%% 颜色索引辅助函数
function color_idx = getColorIndex(process_id)
    if process_id == 0
        color_idx = 1; % 装载
    elseif process_id == -1
        color_idx = 2; % 前往风场
    elseif process_id >= 1 && process_id <= 4
        color_idx = process_id + 2; % 安装工序
    elseif process_id == 5
        color_idx = 7; % 船上组装
    elseif process_id == 6
        color_idx = 8; % 最终安装
    elseif process_id == -2
        color_idx = 9; % 返回港口
    elseif process_id == -3
        color_idx = 10; % 拼接焊接
    else
        color_idx = 1; % 默认
    end
end
