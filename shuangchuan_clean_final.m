%% 单体船 vs 双体船效率对比分析（最终清洁版）
% 10条单体船 vs 10条拼接双体船（由10条单体船拼接而成）运送40台风机
% 重点分析双体船甲板组装优势和拼接时间成本
% 删除故障、右移调度和完全重调度功能

clear all;
close all;
clc;
rng(42);

%% 问题参数设置
TURBINE_COUNT = 40;        % 风机总数
VESSEL_COUNT = 10;         % 船舶总数
DUAL_VESSEL_COUNT = 10;    % 双体船数量（10条拼接船）
BERTH_COUNT = 2;           % 泊位数量

% 风机参数（参考wanquan1的设置）
TURBINE_POWER = 8.0;       % MW
TURBINE_PROCESS_TIMES = [20, 12, 10, 36]; % [基础, 塔筒, 机舱, 叶片] 安装时间(小时)

% 船舶和组装参数
SHIP_ASSEMBLY_TIME = 35;   % 船上组装时间(小时)
FINAL_INSTALLATION_TIME = 12; % 完全组装后的最终安装时间(小时)
WELDING_TIME = 6;          % 双体船拼接焊接时间(小时)

% 距离和速度参数
PORT_TO_FARM_DISTANCE = 80; % 港口到风场距离(km)
VESSEL_SPEED = 15;          % 船舶航行速度(km/h)
LOADING_TIME = 8;           % 装载时间(小时)

%% 数据结构初始化
% 初始化风机数据
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'power', num2cell(ones(1, TURBINE_COUNT) * TURBINE_POWER), ...
                 'processes', cell(1, TURBINE_COUNT));

% 为每台风机设置工序时间（添加±10%随机变化）
for i = 1:TURBINE_COUNT
    variation = 0.9 + 0.2*rand(1, length(TURBINE_PROCESS_TIMES));
    turbines(i).processes = TURBINE_PROCESS_TIMES .* variation;
end

% 初始化单体船数据
single_vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                       'speed', num2cell(ones(1, VESSEL_COUNT) * VESSEL_SPEED), ...
                       'loading_time', num2cell(ones(1, VESSEL_COUNT) * LOADING_TIME));

% 初始化双体船数据（10条拼接船）
dual_vessels = struct('id', num2cell(1:DUAL_VESSEL_COUNT), ...
                     'speed', num2cell(ones(1, DUAL_VESSEL_COUNT) * VESSEL_SPEED), ...
                     'loading_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * LOADING_TIME), ...
                     'welding_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * WELDING_TIME), ...
                     'assembly_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * SHIP_ASSEMBLY_TIME), ...
                     'final_install_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * FINAL_INSTALLATION_TIME));

% 初始化泊位
berths = struct('id', num2cell(1:BERTH_COUNT));

%% 场景1：10条单体船运送40台风机
fprintf('=== 场景1：10条单体船运送40台风机 ===\n');
fprintf('船舶配置：%d条单体船\n', VESSEL_COUNT);
fprintf('风机配置：%d台大型风机(%.1fMW)\n', TURBINE_COUNT, TURBINE_POWER);

% 生成单体船调度
single_schedule = generateSingleVesselSchedule(turbines, single_vessels, berths);
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时\n', single_makespan);

% 绘制单体船甘特图
plotSingleVesselGantt(single_schedule, single_vessels, berths, single_makespan, turbines);

%% 场景2：10条双体船（拼接而成）运送40台风机
fprintf('\n=== 场景2：10条双体船运送40台风机 ===\n');
fprintf('船舶配置：%d条双体船（由%d条单体船拼接而成）\n', DUAL_VESSEL_COUNT, VESSEL_COUNT);
fprintf('风机配置：%d台大型风机(%.1fMW)\n', TURBINE_COUNT, TURBINE_POWER);

% 生成双体船调度（包含拼接时间）
dual_schedule = generateDualVesselSchedule(turbines, dual_vessels, berths);
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时（包含拼接时间）\n', dual_makespan);

% 绘制双体船甘特图
plotDualVesselGantt(dual_schedule, dual_vessels, berths, dual_makespan, turbines);

%% 效率对比分析
fprintf('\n=== 效率对比分析 ===\n');
efficiency_improvement = (single_makespan - dual_makespan) / single_makespan * 100;

if efficiency_improvement > 0
    fprintf('✓ 双体船效率提升：%.2f%%\n', efficiency_improvement);
    fprintf('✓ 节省时间：%.2f 小时\n', single_makespan - dual_makespan);
    fprintf('✓ 双体船方案更优！\n');
else
    fprintf('✗ 单体船效率更高：%.2f%%\n', -efficiency_improvement);
    fprintf('✗ 双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
    fprintf('✗ 单体船方案更优！\n');
end

% 详细时间分析
analyzeTimeBreakdown(single_schedule, dual_schedule, single_makespan, dual_makespan);

%% 单体船调度生成函数（参考wanquan1的预调度处理）
function schedule = generateSingleVesselSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 计算港口到风场的航行时间
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = 80 / vessel_speed;
    end
    
    % 使用优化的风机-船舶分配策略
    [turbine_order, vessel_assignment] = optimizeTurbineVesselAssignment(turbines, vessels);
    
    % 处理每台风机（按优化后的顺序）
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % 获取此风机的工序时间
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % 泊位分配 - 找到最早可用的泊位
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 计算装载开始时间
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % 更新泊位可用性
        berth_avail_time(berth_idx) = loading_end;
        
        % 添加装载任务到调度
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 1);
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + port_to_farm_time(vessel_idx);
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 1);
        
        % 处理安装任务（按工序顺序）
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            process_end = process_start + process_times(p);
            
            % 添加工序到调度
            schedule = addTask(schedule, turbine_idx, vessel_idx, process_start, process_end, p, 0, 1);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 1);
        
        % 更新船舶可用时间
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 优化的风机-船舶分配策略
function [turbine_order, vessel_assignment] = optimizeTurbineVesselAssignment(turbines, vessels)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % 按风机工序复杂度排序
    turbine_complexity = zeros(1, turbine_count);
    for i = 1:turbine_count
        turbine_complexity(i) = sum(turbines(i).processes);
    end
    
    % 复杂风机优先
    [~, turbine_order] = sort(turbine_complexity, 'descend');
    
    % 船舶分配 - 轮询分配以平衡负载
    vessel_assignment = zeros(1, turbine_count);
    vessel_loads = zeros(1, vessel_count);
    
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        
        % 选择当前负载最小的船舶
        [~, best_vessel] = min(vessel_loads);
        vessel_assignment(i) = best_vessel;
        
        % 更新船舶负载
        total_process_time = sum(turbines(turbine_idx).processes);
        travel_time = 2 * (80 / vessels(best_vessel).speed);
        loading_time = vessels(best_vessel).loading_time;
        vessel_loads(best_vessel) = vessel_loads(best_vessel) + total_process_time + travel_time + loading_time;
    end
end
