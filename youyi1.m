%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with disruption handling and rescheduling.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours)
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 100;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.18;  % PSO mutation rate
INERTIA_WEIGHT = 0.75;     % Inertia weight
C1 = 1.6;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 100;    % Time of disruption (hours) - changed to occur earlier
REPAIR_TIME = 52;         % Repair time (hours)
AFFECTED_VESSEL = 6;      % Affected vessel ID

%% 天气参数设置
% 创建指定天数的天气数据
SIMULATION_DAYS = 30;      % 模拟天数
HOURS_PER_DAY = 24;        % 每天小时数
TOTAL_HOURS = SIMULATION_DAYS * HOURS_PER_DAY; % 总小时数

% 创建全局天气数据变量
global WEATHER_DATA;

% 生成天气数据 (1 = 良好天气, 0 = 恶劣天气)
% 默认全部初始化为良好天气
WEATHER_DATA = ones(1, TOTAL_HOURS);

% 定义多个恶劣天气周期（例如，持续12-48小时的风暴）
bad_weather_starts = [2*24, 8*24, 15*24, 22*24, 27*24]; % 第2, 8, 15, 22, 27天
bad_weather_durations = [36, 24, 48, 18, 30]; % 小时

% 标记恶劣天气时段
for i = 1:length(bad_weather_starts)
    start_hour = bad_weather_starts(i);
    end_hour = min(start_hour + bad_weather_durations(i) - 1, TOTAL_HOURS);
    WEATHER_DATA(start_hour+1:end_hour+1) = 0; % 0表示恶劣天气
end

% 计算良好天气比例
good_weather_percentage = sum(WEATHER_DATA) / length(WEATHER_DATA) * 100;
fprintf('已生成天气数据: %.1f%% 良好天气, %.1f%% 恶劣天气\n', good_weather_percentage, 100-good_weather_percentage);

%% Data Structures
% Define turbine installation tasks
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;
end

% Define vessels
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES));

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

%% Right-Shift Rescheduling
fprintf('Applying right-shift rescheduling...\n');
right_shift_schedule = rightShiftRescheduling(initial_schedule, disruption);
makespan_right_shift = calculateMakespan(right_shift_schedule);
fprintf('Right-shift schedule makespan: %.2f hours\n', makespan_right_shift);

%% Plot Results
% Plot two Gantt charts
plotGanttChart(initial_schedule, 'Initial Schedule', turbines, vessels, []);
plotGanttChart(right_shift_schedule, 'Right-Shift Rescheduling', turbines, vessels, disruption);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);
    
    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);
    
    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);
    
    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Check diversity
        diversity = calculateDiversity(fitness);
        
        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end
    
    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);
    
    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE
    global PORT_TO_FARM_DISTANCE;
    
    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        [loading_start, loading_end] = findNextFeasibleWindow(loading_start, vessels(vessel_idx).loading_time);
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm
        travel_start = loading_end;
        [travel_start, travel_end] = findNextFeasibleWindow(travel_start, port_to_farm_time(vessel_idx));
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            [process_start, process_end] = findNextFeasibleWindow(process_start, process_times(p));
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port
        travel_back_start = current_time;
        [travel_back_start, travel_back_end] = findNextFeasibleWindow(travel_back_start, port_to_farm_time(vessel_idx));
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);
    
    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));
    
    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);
    
    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));
    
    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));
    
    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);
    
    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;
    
    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function right_shift_schedule = rightShiftRescheduling(schedule, disruption)
    % 右移重调度函数 - 修正版
    % 输入：原调度计划，中断信息
    % 输出：调整后的调度
    
    % 复制原调度
    right_shift_schedule = schedule;
    
    % 提取中断信息
    affected_vessel = disruption.affected_vessel;
    t_d = disruption.time; % 中断发生时间
    t_r = disruption.repair_time; % 维修所需时间
    
    % 1. 识别受影响的任务
    % 找出故障船舶在中断时正在执行的任务
    ongoing_idx = find(schedule.vessel_id == affected_vessel & ...
                      schedule.start_time < t_d & ...
                      schedule.end_time > t_d);
    
    % 找出故障船舶后续的所有任务
    future_idx = find(schedule.vessel_id == affected_vessel & ...
                     schedule.start_time >= t_d);
    
    % 合并受影响的任务索引
    affected_tasks = [ongoing_idx, future_idx];
    
    % 2. 处理正在进行的任务：将其结束时间设为t_d，并计算剩余时间
    for i = 1:length(ongoing_idx)
        task_idx = ongoing_idx(i);
        % 记录剩余处理时间
        remaining_time = right_shift_schedule.end_time(task_idx) - t_d;
        % 更新当前任务的结束时间为中断时间
        right_shift_schedule.end_time(task_idx) = t_d;
        % 在任务后插入剩余处理（需在维修完成后）
        right_shift_schedule.turbine_id(end+1) = right_shift_schedule.turbine_id(task_idx);
        right_shift_schedule.vessel_id(end+1) = affected_vessel;
        right_shift_schedule.start_time(end+1) = t_d + t_r; % 维修结束后开始
        right_shift_schedule.end_time(end+1) = t_d + t_r + remaining_time;
        right_shift_schedule.process_id(end+1) = right_shift_schedule.process_id(task_idx);
        right_shift_schedule.berth_id(end+1) = right_shift_schedule.berth_id(task_idx);
    end
    
    % 3. 插入维修任务
    repair_task_idx = length(right_shift_schedule.turbine_id) + 1;
    right_shift_schedule.turbine_id(repair_task_idx) = 0; % 0表示维修任务
    right_shift_schedule.vessel_id(repair_task_idx) = affected_vessel;
    right_shift_schedule.start_time(repair_task_idx) = t_d;
    right_shift_schedule.end_time(repair_task_idx) = t_d + t_r;
    right_shift_schedule.process_id(repair_task_idx) = -3; % 特殊标识
    right_shift_schedule.berth_id(repair_task_idx) = 0;
    
    % 4. 调整后续任务的开始和结束时间
    % 计算总延迟时间（维修时间 + 剩余处理时间）
    total_delay = t_r;
    if ~isempty(ongoing_idx)
        total_delay = total_delay + remaining_time;
    end
    
    % 对故障船舶的所有后续任务进行右移
    for i = 1:length(future_idx)
        task_idx = future_idx(i);
        right_shift_schedule.start_time(task_idx) = right_shift_schedule.start_time(task_idx) + total_delay;
        right_shift_schedule.end_time(task_idx) = right_shift_schedule.end_time(task_idx) + total_delay;
    end
    
    % 5. 处理泊位冲突：调整使用同一泊位的其他船舶任务
    % 获取故障船舶使用的泊位
    affected_berths = unique(right_shift_schedule.berth_id(affected_tasks));
    affected_berths = affected_berths(affected_berths > 0); % 排除0
    
    for b = 1:length(affected_berths)
        berth = affected_berths(b);
        % 找到在维修期间使用该泊位的其他船舶任务
        conflict_tasks = find(right_shift_schedule.berth_id == berth & ...
                             right_shift_schedule.vessel_id ~= affected_vessel & ...
                             right_shift_schedule.start_time >= t_d);
        
        % 按原开始时间排序
        [~, order] = sort(right_shift_schedule.start_time(conflict_tasks));
        conflict_tasks = conflict_tasks(order);
        
        % 逐个右移，考虑前一个任务的结束时间
        prev_end = t_d + t_r; % 泊位在维修后可用
        for i = 1:length(conflict_tasks)
            task_idx = conflict_tasks(i);
            new_start = max(prev_end, right_shift_schedule.start_time(task_idx));
            duration = right_shift_schedule.end_time(task_idx) - right_shift_schedule.start_time(task_idx);
            right_shift_schedule.start_time(task_idx) = new_start;
            right_shift_schedule.end_time(task_idx) = new_start + duration;
            prev_end = new_start + duration;
        end
    end
    
    % 6. 重新计算所有任务的开始和结束时间，确保一致性
    % 按时间排序所有任务
    [~, sorted_idx] = sort(right_shift_schedule.start_time);
    fields = fieldnames(right_shift_schedule);
    for f = 1:numel(fields)
        right_shift_schedule.(fields{f}) = right_shift_schedule.(fields{f})(sorted_idx);
    end
end

function validateSchedule(schedule, disruption)
    % 验证调度结果是否满足约束条件
    t_d = disruption.time;
    t_r = disruption.repair_time;
    affected_vessel = disruption.affected_vessel;
    
    % 检查维修任务
    repair_tasks = find(schedule.process_id == -3 & schedule.vessel_id == affected_vessel);
    if isempty(repair_tasks)
        warning('未找到维修任务');
    else
        repair_start = schedule.start_time(repair_tasks(1));
        repair_end = schedule.end_time(repair_tasks(1));
        if abs(repair_start - t_d) > 1e-6 || abs(repair_end - (t_d + t_r)) > 1e-6
            warning('维修时间不匹配: 应为 [%g, %g], 实际为 [%g, %g]', ...
                t_d, t_d + t_r, repair_start, repair_end);
        end
    end
    
    % 检查受影响任务
    vessel_tasks = find(schedule.vessel_id == affected_vessel & schedule.process_id ~= -3);
    for i = 1:length(vessel_tasks)
        idx = vessel_tasks(i);
        if schedule.start_time(idx) >= t_d && schedule.start_time(idx) < t_d + t_r
            warning('任务 %d 未正确右移: 开始于 %g, 应该至少从 %g 开始', ...
                idx, schedule.start_time(idx), t_d + t_r);
        end
    end
    
    % 检查资源约束是否满足
    vessels = unique(schedule.vessel_id);
    for v = 1:length(vessels)
        v_id = vessels(v);
        v_tasks = find(schedule.vessel_id == v_id);
        if length(v_tasks) <= 1
            continue;
        end
        
        % 按开始时间排序
        [~, sorted_idx] = sort(schedule.start_time(v_tasks));
        v_tasks_sorted = v_tasks(sorted_idx);
        
        % 检查是否有重叠
        for i = 1:length(v_tasks_sorted)-1
            task1 = v_tasks_sorted(i);
            task2 = v_tasks_sorted(i+1);
            if schedule.end_time(task1) > schedule.start_time(task2)
                warning('船舶 %d 的任务 %d 和 %d 时间重叠', ...
                    v_id, task1, task2);
            end
        end
    end
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % 创建专业外观的甘特图 - 修正版
    % 包含正确的维修任务显示
    
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    
    % 使用更柔和的颜色方案，并明确包含维修任务颜色
    process_colors = [
        0.9290, 0.6940, 0.3250;  % 装载 (process 0) - 柔和橙色
        0.4660, 0.7740, 0.8880;  % 前往风场 (process -1) - 柔和蓝色
        0.6350, 0.5040, 0.7410;  % 工序 1 - 柔和紫色
        0.4660, 0.7410, 0.3880;  % 工序 2 - 柔和绿色
        0.8500, 0.3250, 0.3980;  % 工序 3 - 柔和红色
        0.9290, 0.6940, 0.1250;  % 工序 4 - 柔和黄色
        0.4660, 0.7740, 0.8880;  % 返回港口 (process -2) - 柔和蓝色
        1.0000, 0.3000, 0.3000;  % 维修 (process -3) - 醒目但不太刺眼的红色
    ];
    
    % 图例的过程名称，明确包含维修
    process_names = {'装载', '前往风场', '工序 1', '工序 2', '工序 3', '工序 4', '返回港口', '维修'};
    
    % 创建图例的虚拟对象
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end
    
    % 绘制船舶时间线
    hold on;
    
    % 如果有故障，添加故障标记线
    if ~isempty(disruption)
        % 绘制故障发生的垂直线
        line([disruption.time, disruption.time], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);
        
        % 添加故障标记文本
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
            
        % 添加结束修复的垂直线
        repair_end = disruption.time + disruption.repair_time;
        line([repair_end, repair_end], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', ':', 'LineWidth', 1.0);
        
        % 添加修复结束标记文本
        text(repair_end, length(vessels)+2.5, '修复结束', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end
    
    % 按船舶组织任务
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        y_pos = v;
        
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;
            
            % 确定过程类型
            process_id = schedule.process_id(task_idx);
            
            % 将process_id映射到颜色索引 - 确保包含维修任务
            if process_id == 0  % 装载
                color_idx = 1;
                process_label = 'L';
            elseif process_id == -1  % 前往风场
                color_idx = 2;
                process_label = 'TF';
            elseif process_id == -2  % 返回港口
                color_idx = 7;
                process_label = 'TB';
            elseif process_id == -3  % 维修 - 确保包含此情况
                color_idx = 8;
                process_label = 'R';
            else  % 常规过程
                color_idx = min(process_id + 2, 7);  % 确保不超出范围
                process_label = ['P', num2str(process_id)];
            end
            
            % 绘制任务条
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);
            
            % 根据持续时间调整字体大小
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end
            
            % 创建标签
            if process_id == -3  % 维修 - 特殊标签
                label_text = '维修';
            else
                if schedule.turbine_id(task_idx) == 0
                    label_text = process_label;
                else
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                end
            end
            
            % 对于非常短的持续时间，使用文本旋转或外部标签
            if duration < 5
                % 使用带线的外部标签
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % 绘制连接到块的小线
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % 常规内部标签
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end
        
        % 在y轴添加船舶标签
        % 如果这是受影响的船舶，添加标记
        if ~isempty(disruption) && v == disruption.affected_vessel
            text(-20, y_pos, sprintf('船舶 %d (故障)', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r');
        else
            text(-20, y_pos, sprintf('船舶 %d', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % 添加泊位时间线
    berth_count = max(schedule.berth_id);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            y_pos = y_start + b;
            
            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;
                
                % 绘制任务条（装载始终是process_id 0）
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
                
                % 添加适当比例的文本标签
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end
                
                % 创建标签
                label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                
                if duration < 5
                    % 小持续时间的外部标签
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % 常规内部标签
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end
            
            % 在y轴添加泊位标签
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % 使用虚拟句柄添加图例
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');
    
    % 设置绘图属性
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    
    % 计算工期并显示
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');

    % 设置轴限制 - 确保显示关键时间范围
    % 找到关键时间点
    if ~isempty(disruption)
        % 显示故障前后的任务
        start_view = max(0, disruption.time - 70); % 显示故障前70小时
        repair_end = disruption.time + disruption.repair_time;
        end_view = max(makespan * 1.05, repair_end + 100); % 确保显示修复后的时段
        xlim([start_view, end_view]);
        
        % 强调显示故障期间 - 添加额外的错误检查
        try
            % 打印调试信息
            fprintf('调试信息：\n');
            fprintf('disruption.time = %.4f\n', disruption.time);
            fprintf('disruption.repair_time = %.4f\n', disruption.repair_time);
            
            highlight_start = disruption.time;
            highlight_end = disruption.time + disruption.repair_time;
            rectangle_width = highlight_end - highlight_start;
            rectangle_height = length(vessels) + berth_count + 3;
            
            fprintf('highlight_start = %.4f\n', highlight_start);
            fprintf('highlight_end = %.4f\n', highlight_end);
            fprintf('rectangle_width = %.4f\n', rectangle_width);
            fprintf('rectangle_height = %.4f\n', rectangle_height);
            
            % 检查所有值是否为有效的数值
            if ~isfinite(highlight_start) || ~isreal(highlight_start)
                error('无效的高亮开始时间：%f', highlight_start);
            end
            if ~isfinite(highlight_end) || ~isreal(highlight_end)
                error('无效的高亮结束时间：%f', highlight_end);
            end
            if ~isfinite(rectangle_width) || rectangle_width <= 0
                error('无效的矩形宽度：%f', rectangle_width);
            end
            if ~isfinite(rectangle_height) || rectangle_height <= 0
                error('无效的矩形高度：%f', rectangle_height);
            end
            
            % 如果所有检查都通过，尝试绘制矩形
            rectangle('Position', [highlight_start, 0, rectangle_width, rectangle_height], ...
                     'FaceColor', [1.0, 0.95, 0.95], 'EdgeColor', 'none', 'LineWidth', 0);
                     
            % 将高亮区域置于底层
            uistack(gca, 'bottom');
            
        catch err
              warning('绘制故障高亮区域时出错：%s', err.message);
            % 继续执行，不中断整个图表的绘制
        end
    else
        % 对于初始调度，保持原始视图
        xlim([0, makespan * 1.05]);
    end
    
    % ... [remaining code] ...
end

function [start_time, end_time] = findNextFeasibleWindow(initial_start, duration)
    % 查找考虑天气条件的下一个可行时间窗口
    global WEATHER_DATA;
    
    % 初始化开始时间
    start_time = initial_start;
    
    % 如果超出天气数据时间范围，则假设为良好天气
    if start_time >= length(WEATHER_DATA)
        end_time = start_time + duration;
        return;
    end
    
    % 寻找能够容纳任务的连续良好天气窗口
    while true
        % 将start_time向上舍入到下一个小时边界（如果不是整点）
        hour_start = ceil(start_time);
        
        % 检查是否已超出天气数据
        if hour_start >= length(WEATHER_DATA)
            end_time = start_time + duration;
            return;
        end
        
        % 检查当前小时是否为良好天气
        if WEATHER_DATA(hour_start + 1) == 1  % +1因为MATLAB索引从1开始
            % 初始化连续良好天气计数器
            continuous_good_hours = 0;
            
            % 检查是否有连续的良好天气窗口
            for h = hour_start:min(hour_start + ceil(duration) - 1, length(WEATHER_DATA) - 1)
                if h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 1
                    continuous_good_hours = continuous_good_hours + 1;
                else
                    break;
                end
            end
            
            % 如果找到足够连续的良好天气小时数
            if continuous_good_hours >= duration
                end_time = start_time + duration;
                return;
            else
                % 跳过不良天气
                h = hour_start + continuous_good_hours;
                while h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 0
                    h = h + 1;
                end
                start_time = h;
            end
        else
            % 当前小时是不良天气，寻找下一个良好天气小时
            h = hour_start;
            while h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 0
                h = h + 1;
            end
            start_time = h;
        end
        
        % 如果超出天气数据，假设为良好天气
        if start_time >= length(WEATHER_DATA)
            end_time = start_time + duration;
            return;
        end
    end
end

function plotWeatherData()
    global WEATHER_DATA;
    
    % 创建天气数据图表
    figure('Position', [100, 100, 800, 300], 'Color', 'white');
    
    % 绘制天气数据
    subplot(2,1,1);
    
    % 创建条形图显示天气状况
    for i = 1:length(WEATHER_DATA)
        if WEATHER_DATA(i) == 1  % 良好天气
            bar(i-1, 1, 'FaceColor', [0.3, 0.6, 0.9], 'EdgeColor', 'none');
            hold on;
        else  % 恶劣天气
            bar(i-1, 1, 'FaceColor', [0.9, 0.3, 0.3], 'EdgeColor', 'none');
            hold on;
        end
    end
    
    % 设置属性
    title('天气状况变化', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('天气状况');
    xlabel('时间(小时)');
    ylim([0, 1.2]);
    xlim([0, length(WEATHER_DATA)]);
    
    % 添加自定义y刻度
    set(gca, 'YTick', [0, 1], 'YTickLabel', {'恶劣', '良好'});
    grid on;
    
    % 添加天数标记
    hold on;
    for day = 1:floor(length(WEATHER_DATA)/24)
        line([day*24, day*24], [0, 1.2], 'Color', [0.5, 0.5, 0.5], 'LineStyle', ':', 'LineWidth', 1);
        text(day*24, 1.1, sprintf('第%d天', day), 'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    
    % 添加天气分布子图
    subplot(2,1,2);
    weather_stats = [sum(WEATHER_DATA == 1), sum(WEATHER_DATA == 0)];
    bar_h = bar(weather_stats, 0.4, 'FaceColor', [0.4, 0.6, 0.8]);
    set(gca, 'XTick', [1, 2], 'XTickLabel', {'良好天气', '恶劣天气'});
    ylabel('小时数');
    title('天气分布统计', 'FontSize', 10);
    grid on;
    
    % 添加百分比标签
    good_pct = weather_stats(1) / sum(weather_stats) * 100;
    bad_pct = weather_stats(2) / sum(weather_stats) * 100;
    
    text(1, weather_stats(1)/2, sprintf('%.1f%%', good_pct), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'w');
    text(2, weather_stats(2)/2, sprintf('%.1f%%', bad_pct), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'w');
    
    % 添加总小时数文本
    text(1, weather_stats(1)+5, sprintf('%d小时', weather_stats(1)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
    text(2, weather_stats(2)+5, sprintf('%d小时', weather_stats(2)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
    
    % 添加说明注解
    annotation('textbox', [0.15, 0.01, 0.7, 0.05], 'String', ...
        '注：安装、航行等离岸作业在恶劣天气期间无法执行', ...
        'EdgeColor', 'none', 'HorizontalAlignment', 'center', 'FontSize', 8);
end