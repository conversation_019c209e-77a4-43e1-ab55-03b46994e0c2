%% Offshore Wind Farm Installation Scheduler with Multi-vessel Optimization and Failure Handling
% This enhanced version supports:
% 1. Multi-vessel parallel operations with task decomposition
% 2. Vessel failure simulation and dynamic rescheduling
% 3. Genetic algorithm-based optimization for task scheduling
% 4. Advanced visualization capabilities

%% Input Data Definition

% Vessel data
vessel_values = [
    4, 12, 10;    % Transit Velocity
    1, 6, 4;      % Vessel Capacity Foundations
    1, 6, 4;      % Vessel Capacity Turbines
    15, 15, 15;   % Hammer speed
    NaN, NaN, NaN;% Empty row
    0, 0, 20;     % Airgap
    0, 0, 5;      % Penetration Depth leg
    0, 0, 30;     % Jacking speed
    NaN, NaN, NaN;% Empty row
    NaN, NaN, NaN;% Empty row
    150, 400, 200;% Charter Cost
    NaN, NaN, NaN;% Empty row
    0, 0, 1;      % Jack Up
    1, 0, 0       % Supply vessel
];

% Operation data
operation_values = [
    0, 2, 2;      % Loading time Foundation
    0, 3, 3;      % Loading time Turbine
    2, 8, 4;      % Positioning time Vessel
    2, 2, 2;      % Lifting operation time Foundation
    1, 1, 1;      % Lifting operation time Transition piece
    2, 2, 2;      % Grouting time Transition Piece
    1, 1, 1;      % Lifting operation time Tower
    2, 2, 2;      % Lifting operation time Nacelle
    1, 1, 1;      % Lifting operation time Blade
    2, 2, 2       % Installation time Blade
];

% Farm data
farm_values = [
    30;           % Number of foundations to be installed
    30;           % Number of turbines to be installed
    178.3;        % Turbine rotor diameter
    0.5;          % Distance between installation locations
    25;           % Water depth
    29;           % Penetration depth monopile
    1;            % Number of tower pieces per turbine
    3;            % Number of blades per turbine
    54            % Distance farm to feeding harbour
];

% Configuration combinations - Restored all 9 configurations for better visualization
config_values = [
    1, 1, 1;
    1, 2, 1;
    1, 3, 1;
    2, 1, 2;
    2, 2, 2;
    2, 3, 2;
    3, 1, 3;
    3, 2, 3;
    3, 3, 3
];

%% Task Definition - Creating data structures for operations

% Define the number of operations for each task type
NUM_FDN_OPERATIONS = 4;  % Foundation operations: Positioning, Jacking, Installation, Transition Piece
NUM_WTB_OPERATIONS = 4;  % Turbine operations: Tower, Nacelle, Blades, Final checks

% Define operation types for identification
OP_TYPE = struct(...
    'LOADING', 1, ...
    'POSITIONING', 2, ...
    'JACKING_UP', 3, ...
    'INSTALLATION', 4, ...
    'JACKING_DOWN', 5, ...
    'TRANSIT', 6, ...
    'REPOSITIONING', 7);

% Parameters for the genetic algorithm
GA_PARAMS = struct(...
    'POPULATION_SIZE', 50, ...
    'MAX_GENERATIONS', 100, ...
    'CROSSOVER_RATE', 0.8, ...
    'MUTATION_RATE', 0.2, ...
    'ELITE_COUNT', 5);

% Failure simulation parameters
FAILURE_PARAMS = struct(...
    'ENABLE_FAILURES', true, ... % Set to true to enable random vessel failures
    'FAILURE_PROBABILITY', 0.1, ... % Probability of failure per vessel per day
    'MIN_REPAIR_TIME', 12, ... % Minimum repair time in hours
    'MAX_REPAIR_TIME', 72); ... % Maximum repair time in hours

% Rescheduling parameters
RESCHEDULE_PARAMS = struct(...
    'MAKESPAN_WEIGHT', 0.6, ... % Weight for makespan difference
    'START_TIME_WEIGHT', 0.3, ... % Weight for start time changes
    'VESSEL_CHANGE_WEIGHT', 0.1); % Weight for vessel assignment changes

% Results storage
results = struct('option', [], 'makespan', [], 'total_cost', [], 'vessel_utilization', [], ...
                'num_failures', [], 'reschedule_stats', [], 'schedule', []);

%% Helper Functions

function vessels = createVesselObjects(vessel_values, operation_values, vessel_types)
    % Create vessel objects with all relevant parameters
    
    % Initialize vessel array
    num_vessels = length(vessel_types);
    vessels = struct('id', {}, 'type', {}, 'transit_velocity', {}, 'capacity_fdn', {}, ...
                    'capacity_wtb', {}, 'hammer_speed', {}, 'airgap', {}, ...
                    'pen_leg', {}, 'jacking_speed', {}, 'charter_cost', {}, ...
                    'is_jacking', {}, 'is_supply', {}, 'loading_time_fdn', {}, ...
                    'loading_time_wtb', {}, 'positioning_time', {}, 'lifting_time_fdn', {}, ...
                    'lifting_time_tp', {}, 'grouting_time_tp', {}, 'lifting_time_tower', {}, ...
                    'lifting_time_nacelle', {}, 'lifting_time_blade', {}, 'installation_time_blade', {}, ...
                    'current_tasks', {}, 'schedule', {}, 'failure_history', {});
    
    % Create vessels based on specified types
    for i = 1:num_vessels
        vessel_type = vessel_types(i);
        vessels(i).id = i;
        vessels(i).type = vessel_type;
        
        % Extract vessel parameters
        vessels(i).transit_velocity = vessel_values(1, vessel_type);
        vessels(i).capacity_fdn = vessel_values(2, vessel_type);
        vessels(i).capacity_wtb = vessel_values(3, vessel_type);
        vessels(i).hammer_speed = vessel_values(4, vessel_type);
        vessels(i).airgap = vessel_values(6, vessel_type);
        vessels(i).pen_leg = vessel_values(7, vessel_type);
        vessels(i).jacking_speed = vessel_values(8, vessel_type);
        vessels(i).charter_cost = vessel_values(11, vessel_type);
        vessels(i).is_jacking = vessel_values(13, vessel_type);
        vessels(i).is_supply = vessel_values(14, vessel_type);
        
        % Extract operation times
        vessels(i).loading_time_fdn = operation_values(1, vessel_type);
        vessels(i).loading_time_wtb = operation_values(2, vessel_type);
        vessels(i).positioning_time = operation_values(3, vessel_type);
        vessels(i).lifting_time_fdn = operation_values(4, vessel_type);
        vessels(i).lifting_time_tp = operation_values(5, vessel_type);
        vessels(i).grouting_time_tp = operation_values(6, vessel_type);
        vessels(i).lifting_time_tower = operation_values(7, vessel_type);
        vessels(i).lifting_time_nacelle = operation_values(8, vessel_type);
        vessels(i).lifting_time_blade = operation_values(9, vessel_type);
        vessels(i).installation_time_blade = operation_values(10, vessel_type);
        
        % Initialize task tracking
        vessels(i).current_tasks = [];
        vessels(i).schedule = [];
        vessels(i).failure_history = [];
    end
end

function [fdn_tasks, wtb_tasks] = createTaskObjects(N_FDN, N_WTB, vessels, farm_values, operation_values)
    % Create task objects for foundations and turbines
    
    % Extract parameters
    WD = farm_values(5);          % Water depth
    PEN_FDN = farm_values(6);     % Monopile penetration depth
    N_TOW = farm_values(7);       % Number of tower pieces per turbine
    N_BLADE = farm_values(8);     % Number of blades per turbine
    
    % Initialize foundation tasks
    for i = 1:N_FDN
        fdn_tasks(i).id = i;
        fdn_tasks(i).type = 'Foundation';
        fdn_tasks(i).location = i;
        fdn_tasks(i).status = 'Pending';
        fdn_tasks(i).assigned_vessel = 0;
        
        % Define operations fields
        fdn_tasks(i).operations = struct(...
            'positioning', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0), ...
            'jacking', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0), ...
            'installation', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0), ...
            'transition_piece', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0));
        
        % Pre-initialize vessel_durations field
        fdn_tasks(i).vessel_durations = repmat(struct('positioning', 0, 'jacking', 0, 'installation', 0, 'transition_piece', 0), 1, length(vessels));
    end
    
    % Initialize turbine tasks
    for i = 1:N_WTB
        wtb_tasks(i).id = i;
        wtb_tasks(i).type = 'Turbine';
        wtb_tasks(i).location = i;
        wtb_tasks(i).status = 'Pending';
        wtb_tasks(i).assigned_vessel = 0;
        
        % Define operations fields
        wtb_tasks(i).operations = struct(...
            'tower', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0), ...
            'nacelle', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0), ...
            'blades', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0), ...
            'commissioning', struct('duration', 0, 'status', 'Pending', 'start_time', 0, 'end_time', 0));
        
        % Pre-initialize vessel_durations field
        wtb_tasks(i).vessel_durations = repmat(struct('tower', 0, 'nacelle', 0, 'blades', 0, 'commissioning', 0), 1, length(vessels));
    end
    
    % Pre-calculate operation durations for each vessel
    for v = 1:length(vessels)
        vessel = vessels(v);
        
        % Foundation operation durations
        for i = 1:N_FDN
            % Calculate durations based on vessel characteristics
            pos_duration = vessel.positioning_time;
            jack_duration = 0;
            if vessel.is_jacking == 1
                jack_duration = (vessel.pen_leg + vessel.airgap + WD) / vessel.jacking_speed;
            end
            install_duration = vessel.lifting_time_fdn + (PEN_FDN / vessel.hammer_speed);
            tp_duration = vessel.lifting_time_tp + vessel.grouting_time_tp;
            
            fdn_tasks(i).vessel_durations(v).positioning = pos_duration;
            fdn_tasks(i).vessel_durations(v).jacking = jack_duration;
            fdn_tasks(i).vessel_durations(v).installation = install_duration;
            fdn_tasks(i).vessel_durations(v).transition_piece = tp_duration;
        end
        
        % Turbine operation durations
        for i = 1:N_WTB
            % Calculate durations based on vessel characteristics
            tower_duration = N_TOW * vessel.lifting_time_tower;
            nacelle_duration = vessel.lifting_time_nacelle;
            blade_duration = N_BLADE * (vessel.lifting_time_blade + vessel.installation_time_blade);
            commissioning_duration = 8; % Fixed time for commissioning (8 hours)
            
            wtb_tasks(i).vessel_durations(v).tower = tower_duration;
            wtb_tasks(i).vessel_durations(v).nacelle = nacelle_duration;
            wtb_tasks(i).vessel_durations(v).blades = blade_duration;
            wtb_tasks(i).vessel_durations(v).commissioning = commissioning_duration;
        end
    end
end

function [schedule, best_fitness, convergence_data] = scheduleTasksGA(fdn_tasks, wtb_tasks, vessels, farm_values, ga_params)
    % Schedule tasks using genetic algorithm
    
    % Extract parameters
    N_FDN = length(fdn_tasks);
    N_WTB = length(wtb_tasks);
    num_vessels = length(vessels);
    
    % Define the GA parameters
    population_size = ga_params.POPULATION_SIZE;
    max_generations = ga_params.MAX_GENERATIONS;
    crossover_rate = ga_params.CROSSOVER_RATE;
    mutation_rate = ga_params.MUTATION_RATE;
    elite_count = ga_params.ELITE_COUNT;
    
    % Initialize the population
    population = initializePopulation(population_size, N_FDN, N_WTB, num_vessels);
    
    % Evaluate initial population
    fitness_values = zeros(population_size, 1);
    for i = 1:population_size
        fitness_values(i) = evaluateFitness(population(i, :), fdn_tasks, wtb_tasks, vessels, farm_values);
    end
    
    % Initialize convergence tracking data
    convergence_data = struct('best_fitness', zeros(max_generations, 1), ...
                             'avg_fitness', zeros(max_generations, 1), ...
                             'generation', 1:max_generations, ...
                             'max_generations', max_generations);
    
    % Main GA loop
    for gen = 1:max_generations
        % Select parents for crossover
        parents = selectParents(population, fitness_values, population_size);
        
        % Create offspring through crossover and mutation
        offspring = createOffspring(parents, N_FDN, N_WTB, num_vessels, crossover_rate, mutation_rate);
        
        % Evaluate offspring
        offspring_fitness = zeros(size(offspring, 1), 1);
        for i = 1:size(offspring, 1)
            offspring_fitness(i) = evaluateFitness(offspring(i, :), fdn_tasks, wtb_tasks, vessels, farm_values);
        end
        
        % Elitism: Keep best solutions from current population
        [sorted_fitness, indices] = sort(fitness_values);
        elite_indices = indices(1:elite_count);
        elite_solutions = population(elite_indices, :);
        
        % Create new population
        [combined_pop, combined_fitness] = combinePopulations(...
            population, fitness_values, offspring, offspring_fitness);
        
        % Select best solutions for next generation while ensuring elites are included
        [population, fitness_values] = selectNextGeneration(...
            combined_pop, combined_fitness, population_size, elite_solutions);
        
        % Record convergence data
        [best_fitness, best_idx] = min(fitness_values);
        convergence_data.best_fitness(gen) = best_fitness;
        convergence_data.avg_fitness(gen) = mean(fitness_values);
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best fitness = %.2f, Avg fitness = %.2f\n', ...
                gen, best_fitness, convergence_data.avg_fitness(gen));
        end
    end
    
    % Get best solution
    [best_fitness, best_idx] = min(fitness_values);
    best_solution = population(best_idx, :);
    
    % Decode the best solution to get the schedule
    schedule = decodeSolution(best_solution, fdn_tasks, wtb_tasks, vessels, farm_values);
end

function population = initializePopulation(population_size, N_FDN, N_WTB, num_vessels)
    % Initialize a population of chromosomes
    % Each chromosome consists of:
    % 1. Task assignments: which vessel is assigned to which task
    % 2. Task sequence: the order of tasks for each vessel
    
    total_tasks = N_FDN + N_WTB;
    population = zeros(population_size, total_tasks * 2);
    
    for i = 1:population_size
        % Assign tasks to vessels
        task_assignments = randi(num_vessels, 1, total_tasks);
        
        % Create a random sequence for all tasks
        task_sequence = randperm(total_tasks);
        
        % Combine assignment and sequence
        chromosome = [task_assignments, task_sequence];
        population(i, :) = chromosome;
    end
end

function fitness = evaluateFitness(chromosome, fdn_tasks, wtb_tasks, vessels, farm_values)
    % Evaluate the fitness (lower is better) of a chromosome
    % Fitness is based on makespan and total cost
    
    % Decode the solution to get a schedule
    schedule = decodeSolution(chromosome, fdn_tasks, wtb_tasks, vessels, farm_values);
    
    % Calculate performance metrics
    [makespan, total_cost, ~] = calculatePerformanceMetrics(schedule, vessels);
    
    % Fitness is a weighted combination of makespan and cost
    makespan_weight = 0.6;
    cost_weight = 0.4;
    
    % Normalize by approximate maximum values to get comparable scales
    norm_makespan = makespan / (30 * 24); % Normalized by 30 days
    norm_cost = total_cost / 10000;       % Normalized by 10,000 units
    
    fitness = makespan_weight * norm_makespan + cost_weight * norm_cost;
end

function schedule = decodeSolution(chromosome, fdn_tasks, wtb_tasks, vessels, farm_values)
    % Decode a genetic algorithm chromosome into a schedule
    
    N_FDN = length(fdn_tasks);
    N_WTB = length(wtb_tasks);
    total_tasks = N_FDN + N_WTB;
    PARK_DIS = farm_values(9);    % Distance from harbor to farm
    INT_D = farm_values(4);       % Distance between installation locations
    WD = farm_values(5);          % Water depth
    
    % Extract task assignments and sequence from chromosome
    task_assignments = chromosome(1:total_tasks);
    task_sequence = chromosome(total_tasks+1:end);
    
    % Initialize schedule data structure
    schedule = struct();
    schedule.tasks = struct('id', {}, 'type', {}, 'vessel_id', {}, 'location', {}, 'start_time', {}, 'end_time', {}, 'operations', {});
    
    % Create combined task list
    all_tasks = [fdn_tasks, wtb_tasks];
    
    % Create vessel schedules
    vessel_schedules = cell(length(vessels), 1);
    vessel_locations = zeros(length(vessels), 1); % Start at harbor (location 0)
    vessel_times = zeros(length(vessels), 1);     % Start at time 0
    
    % Process tasks according to sequence
    for i = 1:length(task_sequence)
        task_idx = task_sequence(i);
        vessel_idx = task_assignments(task_idx);
        task = all_tasks(task_idx);
        
        % Determine if it's foundation or turbine task
        is_foundation = task_idx <= N_FDN;
        
        % Get current vessel details
        vessel = vessels(vessel_idx);
        current_time = vessel_times(vessel_idx);
        current_location = vessel_locations(vessel_idx);
        
        % Calculate transit time to task location
        if current_location == 0 % Coming from harbor
            transit_time = PARK_DIS / vessel.transit_velocity;
        else % Coming from another installation location
            distance = INT_D * abs(current_location - task.location);
            transit_time = distance / vessel.transit_velocity;
        end
        
        % Add transit time
        arrival_time = current_time + transit_time;
        
        % Initialize task in schedule
        task_schedule = struct();
        task_schedule.id = task.id;
        task_schedule.type = task.type;
        task_schedule.vessel_id = vessel_idx;
        task_schedule.location = task.location;
        
        % Schedule operations based on task type
        if is_foundation
            % Foundation operations
            operations = struct();
            
            % Positioning
            pos_start = arrival_time;
            pos_duration = vessel.positioning_time;
            pos_end = pos_start + pos_duration;
            operations.positioning = struct('start_time', pos_start, 'end_time', pos_end, 'duration', pos_duration);
            current_time = pos_end;
            
            % Jacking (if applicable)
            jack_duration = 0;
            if vessel.is_jacking == 1
                jack_duration = (vessel.pen_leg + vessel.airgap + WD) / vessel.jacking_speed;
                jack_start = current_time;
                jack_end = jack_start + jack_duration;
                operations.jacking_up = struct('start_time', jack_start, 'end_time', jack_end, 'duration', jack_duration);
                current_time = jack_end;
            end
            
            % Installation
            install_start = current_time;
            install_duration = vessel.lifting_time_fdn + (farm_values(6) / vessel.hammer_speed);
            install_end = install_start + install_duration;
            operations.installation = struct('start_time', install_start, 'end_time', install_end, 'duration', install_duration);
            current_time = install_end;
            
            % Transition piece
            tp_start = current_time;
            tp_duration = vessel.lifting_time_tp + vessel.grouting_time_tp;
            tp_end = tp_start + tp_duration;
            operations.transition_piece = struct('start_time', tp_start, 'end_time', tp_end, 'duration', tp_duration);
            current_time = tp_end;
            
            % Jacking down (if applicable)
            if vessel.is_jacking == 1 && jack_duration > 0
                jack_down_start = current_time;
                jack_down_end = jack_down_start + jack_duration;
                operations.jacking_down = struct('start_time', jack_down_start, 'end_time', jack_down_end, 'duration', jack_duration);
                current_time = jack_down_end;
            end
            
            task_schedule.operations = operations;
            task_schedule.start_time = pos_start;
            task_schedule.end_time = current_time;
            
        else
            % Turbine operations
            operations = struct();
            
            % Positioning
            pos_start = arrival_time;
            pos_duration = vessel.positioning_time;
            pos_end = pos_start + pos_duration;
            operations.positioning = struct('start_time', pos_start, 'end_time', pos_end, 'duration', pos_duration);
            current_time = pos_end;
            
            % Jacking (if applicable)
            jack_duration = 0;
            if vessel.is_jacking == 1
                jack_duration = (vessel.pen_leg + vessel.airgap + WD) / vessel.jacking_speed;
                jack_start = current_time;
                jack_end = jack_start + jack_duration;
                operations.jacking_up = struct('start_time', jack_start, 'end_time', jack_end, 'duration', jack_duration);
                current_time = jack_end;
            end
            
            % Tower
            tower_start = current_time;
            tower_duration = farm_values(7) * vessel.lifting_time_tower;
            tower_end = tower_start + tower_duration;
            operations.tower = struct('start_time', tower_start, 'end_time', tower_end, 'duration', tower_duration);
            current_time = tower_end;
            
            % Nacelle
            nacelle_start = current_time;
            nacelle_duration = vessel.lifting_time_nacelle;
            nacelle_end = nacelle_start + nacelle_duration;
            operations.nacelle = struct('start_time', nacelle_start, 'end_time', nacelle_end, 'duration', nacelle_duration);
            current_time = nacelle_end;
            
            % Blades
            blades_start = current_time;
            blades_duration = farm_values(8) * (vessel.lifting_time_blade + vessel.installation_time_blade);
            blades_end = blades_start + blades_duration;
            operations.blades = struct('start_time', blades_start, 'end_time', blades_end, 'duration', blades_duration);
            current_time = blades_end;
            
            % Jacking down (if applicable)
            if vessel.is_jacking == 1 && jack_duration > 0
                jack_down_start = current_time;
                jack_down_end = jack_down_start + jack_duration;
                operations.jacking_down = struct('start_time', jack_down_start, 'end_time', jack_down_end, 'duration', jack_duration);
                current_time = jack_down_end;
            end
            
            task_schedule.operations = operations;
            task_schedule.start_time = pos_start;
            task_schedule.end_time = current_time;
        end
        
        % Update vessel state
        vessel_times(vessel_idx) = current_time;
        vessel_locations(vessel_idx) = task.location;
        
        % Add task to vessel's schedule
        vessel_schedules{vessel_idx} = [vessel_schedules{vessel_idx}, task_schedule];
        
        % Add task to overall schedule
        schedule.tasks = [schedule.tasks, task_schedule];
    end
    
    % Store vessel schedules in the overall schedule
    schedule.vessel_schedules = vessel_schedules;
end

function parents = selectParents(population, fitness_values, num_parents)
    % Select parents using tournament selection
    parents = zeros(size(population));
    
    for i = 1:num_parents
        % Select 3 random individuals for tournament
        candidates = randperm(length(fitness_values), 3);
        [~, idx] = min(fitness_values(candidates));
        winner = candidates(idx);
        
        % Add winner to parents
        parents(i, :) = population(winner, :);
    end
end

function offspring = createOffspring(parents, N_FDN, N_WTB, num_vessels, crossover_rate, mutation_rate)
    % Create offspring through crossover and mutation
    
    num_parents = size(parents, 1);
    offspring = [];
    total_tasks = N_FDN + N_WTB;
    
    % Perform crossover
    for i = 1:2:num_parents
        if i+1 <= num_parents
            parent1 = parents(i, :);
            parent2 = parents(i+1, :);
            
            if rand() < crossover_rate && total_tasks > 1
                % Split chromosome into task assignments and sequence
                p1_assignments = parent1(1:total_tasks);
                p1_sequence = parent1(total_tasks+1:end);
                p2_assignments = parent2(1:total_tasks);
                p2_sequence = parent2(total_tasks+1:end);
                
                % Crossover for task assignments (single point)
                crossover_point = randi(total_tasks-1);  % Only valid when total_tasks>1
                c1_assignments = [p1_assignments(1:crossover_point), p2_assignments(crossover_point+1:end)];
                c2_assignments = [p2_assignments(1:crossover_point), p1_assignments(crossover_point+1:end)];
                
                % Ordered crossover for task sequence
                c1_sequence = orderedCrossover(p1_sequence, p2_sequence);
                c2_sequence = orderedCrossover(p2_sequence, p1_sequence);
                
                % Combine to form complete chromosomes
                child1 = [c1_assignments, c1_sequence];
                child2 = [c2_assignments, c2_sequence];
            else
                % If total tasks is 1 or crossover probability not met, inherit directly
                child1 = parent1;
                child2 = parent2;
            end
            
            % Perform mutation
            child1 = mutateChromosome(child1, total_tasks, num_vessels, mutation_rate);
            child2 = mutateChromosome(child2, total_tasks, num_vessels, mutation_rate);
            
            % Add children to offspring
            offspring = [offspring; child1; child2];
        end
    end
end

function sequence = orderedCrossover(seq1, seq2)
    n = length(seq1);
    if n < 2
        sequence = seq1;
        return;
    end

    % Select random subsequence from parent1
    cut_points = sort(randperm(n, 2));
    start_idx = cut_points(1);
    end_idx = cut_points(2);
    
    % Copy subsequence from parent1
    sequence = zeros(1, n);
    sequence(start_idx:end_idx) = seq1(start_idx:end_idx);
    
    % Fill the remaining positions with elements from parent2 in the original order
    remaining_elements = setdiff(seq2, seq1(start_idx:end_idx), 'stable');
    
    % Fill before the start_idx
    if start_idx > 1
        sequence(1:start_idx-1) = remaining_elements(1:start_idx-1);
        remaining_elements = remaining_elements(start_idx:end);
    end
    
    % Fill after the end_idx
    if end_idx < n
        sequence(end_idx+1:end) = remaining_elements;
    end
end

function chromosome = mutateChromosome(chromosome, total_tasks, num_vessels, mutation_rate)
    % Mutate a chromosome
    
    % Split chromosome into task assignments and sequence
    assignments = chromosome(1:total_tasks);
    sequence = chromosome(total_tasks+1:end);
    
    % Mutate task assignments
    for i = 1:total_tasks
        if rand() < mutation_rate
            assignments(i) = randi(num_vessels);
        end
    end
    
    % Mutate task sequence (swap mutation)
    if rand() < mutation_rate
        idx1 = randi(total_tasks);
        idx2 = randi(total_tasks);
        temp = sequence(idx1);
        sequence(idx1) = sequence(idx2);
        sequence(idx2) = temp;
    end
    
    % Combine back into one chromosome
    chromosome = [assignments, sequence];
end

function [combined_pop, combined_fitness] = combinePopulations(population, fitness, offspring, offspring_fitness)
    % Combine parent and offspring populations
    combined_pop = [population; offspring];
    combined_fitness = [fitness; offspring_fitness];
end

function [new_population, new_fitness] = selectNextGeneration(population, fitness, pop_size, elite_solutions)
    % Select the next generation using elitism and fitness-based selection
    
    % Sort the population by fitness
    [sorted_fitness, indices] = sort(fitness);
    sorted_pop = population(indices, :);
    
    % Ensure elites are included
    num_elites = size(elite_solutions, 1);
    new_population = zeros(pop_size, size(population, 2));
    new_fitness = zeros(pop_size, 1);
    
    % Add elites to new population
    new_population(1:num_elites, :) = elite_solutions;
    
    % Add the best individuals from sorted population to fill remaining spots
    remaining_spots = pop_size - num_elites;
    new_population(num_elites+1:end, :) = sorted_pop(1:remaining_spots, :);
    new_fitness(1:num_elites) = fitness(indices(1:num_elites));
    new_fitness(num_elites+1:end) = sorted_fitness(1:remaining_spots);
end

function [final_schedule, failure_stats] = simulateWithFailures(initial_schedule, vessels, fdn_tasks, wtb_tasks, farm_values, failure_params, reschedule_params, ga_params)
    % Simulate the execution of the schedule with potential vessel failures and rescheduling
    
    % Extract parameters
    enable_failures = failure_params.ENABLE_FAILURES;
    failure_probability = failure_params.FAILURE_PROBABILITY;
    min_repair_time = failure_params.MIN_REPAIR_TIME;
    max_repair_time = failure_params.MAX_REPAIR_TIME;
    
    % Initialize simulation variables
    current_time = 0;
    final_schedule = initial_schedule;
    completed_tasks = [];
    remaining_tasks = {fdn_tasks, wtb_tasks};
    num_failures = 0;
    reschedule_stats = [];
    vessel_status = ones(length(vessels), 1); % 1 = operational, 0 = failed
    vessel_repair_times = zeros(length(vessels), 1);
    
    % Define a time step for the simulation (e.g., 1 hour)
    time_step = 1;
    
    % Get the expected completion time of the schedule
    [makespan, ~, ~] = calculatePerformanceMetrics(initial_schedule, vessels);
    
    % Run simulation until all tasks are completed
    while current_time < makespan * 1.5 % Allow for some buffer due to failures
        % Check for vessel failures
        if enable_failures
            for v = 1:length(vessels)
                % Only operational vessels can fail
                if vessel_status(v) == 1
                    if rand() < (failure_probability / 24) % Convert daily to hourly probability
                        % Vessel failure occurs
                        vessel_status(v) = 0;
                        repair_time = min_repair_time + rand() * (max_repair_time - min_repair_time);
                        vessel_repair_times(v) = current_time + repair_time;
                        num_failures = num_failures + 1;
                        
                        fprintf('Simulation time %.1f: Vessel %d failed, estimated repair time: %.1f hours\n', ...
                            current_time, v, repair_time);
                        
                        % Trigger rescheduling
                       [right_shift_schedule, complete_reschedule] = rescheduleAfterFailure(final_schedule, vessels, remaining_tasks, current_time, v, repair_time, reschedule_params, ga_params, farm_values);

                        
                        % Choose between right-shift or complete rescheduling
                        [selected_schedule, reschedule_type] = selectRescheduleStrategy(right_shift_schedule, complete_reschedule, final_schedule, vessels, reschedule_params);

                        
                        % Update the schedule
                        final_schedule = selected_schedule;
                        
                        % Record statistics
                        reschedule_stats = [reschedule_stats; struct(...
                            'time', current_time, ...
                            'vessel', v, ...
                            'repair_time', repair_time, ...
                            'strategy', reschedule_type)];
                    end
                else
                    % Check if vessel is repaired
                    if current_time >= vessel_repair_times(v)
                        vessel_status(v) = 1;
                        fprintf('Simulation time %.1f: Vessel %d repaired and back in operation\n', current_time, v);
                    end
                end
            end
        end
        
        % Update tasks status at current time
        [completed, remaining] = updateTasksStatus(final_schedule, current_time);
        completed_tasks = [completed_tasks, completed];
        remaining_tasks = remaining;
        
        % Advance time
        current_time = current_time + time_step;
    end
    
    % Return statistics
    failure_stats = struct('num_failures', num_failures, 'reschedule_stats', reschedule_stats);
end

function [right_shift_schedule, complete_reschedule] = rescheduleAfterFailure(current_schedule, vessels, remaining_tasks, current_time, failed_vessel, repair_time, reschedule_params, ga_params, farm_values)
    % 1. Right-shift rescheduling
    right_shift_schedule = rightShiftReschedule(current_schedule, failed_vessel, current_time, repair_time);
    
    % 2. Complete rescheduling
    complete_reschedule = completeReschedule(current_schedule, vessels, remaining_tasks, current_time, failed_vessel, repair_time, ga_params, farm_values);
end

function new_schedule = rightShiftReschedule(current_schedule, failed_vessel, current_time, repair_time)
    % Implement right-shift rescheduling by delaying affected tasks
    new_schedule = current_schedule;
    
    % Find all tasks of the failed vessel that start after current_time
    for i = 1:length(new_schedule.tasks)
        task = new_schedule.tasks(i);
        
        % Check if task is assigned to failed vessel and affected by failure
        if task.vessel_id == failed_vessel && task.start_time >= current_time
            % Shift the task by repair time
            shift_amount = repair_time;
            
            % Update task times
            new_schedule.tasks(i).start_time = task.start_time + shift_amount;
            new_schedule.tasks(i).end_time = task.end_time + shift_amount;
            
            % Update operation times
            op_fields = fieldnames(task.operations);
            for j = 1:length(op_fields)
                op_name = op_fields{j};
                if isfield(task.operations, op_name)
                    op = task.operations.(op_name);
                    new_schedule.tasks(i).operations.(op_name).start_time = op.start_time + shift_amount;
                    new_schedule.tasks(i).operations.(op_name).end_time = op.end_time + shift_amount;
                end
            end
        end
    end
end

function new_schedule = completeReschedule(current_schedule, vessels, remaining_tasks, current_time, failed_vessel, repair_time, ga_params, farm_values)
    % Get remaining foundation and turbine tasks
    fdn_tasks = remaining_tasks{1};
    wtb_tasks = remaining_tasks{2};

    % If no remaining tasks, return current schedule
    if isempty(fdn_tasks) && isempty(wtb_tasks)
        new_schedule = current_schedule;
        return;
    end

    % Implement complete rescheduling: use genetic algorithm to schedule remaining tasks
    % Create a modified vessels list to ensure failed vessel can't take tasks during repair
    modified_vessels = vessels;
    modified_vessels(failed_vessel).failure_history = [modified_vessels(failed_vessel).failure_history; ...
        struct('failure_time', current_time, 'repair_time', current_time + repair_time)];

    % Run genetic algorithm to get new schedule
    [new_schedule, ~] = scheduleTasksGA(fdn_tasks, wtb_tasks, modified_vessels, farm_values, ga_params);

    % Keep currently completed tasks
    completed_tasks = [];
    for i = 1:length(current_schedule.tasks)
        if current_schedule.tasks(i).end_time <= current_time
            completed_tasks = [completed_tasks, current_schedule.tasks(i)];
        end
    end

    % Merge completed tasks with newly scheduled tasks
    new_schedule.tasks = [completed_tasks, new_schedule.tasks];
end

function [selected_schedule, strategy] = selectRescheduleStrategy(right_shift_schedule, complete_reschedule, original_schedule, vessels, reschedule_params)

    % Choose between right-shift and complete rescheduling based on performance metrics
    
    % Calculate metrics for both strategies
    [makespan_rs, cost_rs, ~] = calculatePerformanceMetrics(right_shift_schedule, vessels);
    [makespan_cr, cost_cr, ~] = calculatePerformanceMetrics(complete_reschedule, vessels);
    [makespan_orig, cost_orig, ~] = calculatePerformanceMetrics(original_schedule, vessels);
    
    % Calculate schedule changes (start time deviations)
    start_time_changes_rs = calculateStartTimeChanges(original_schedule, right_shift_schedule);
    start_time_changes_cr = calculateStartTimeChanges(original_schedule, complete_reschedule);
    
    % Calculate vessel assignment changes
    vessel_changes_rs = 0; % Right-shift doesn't change vessel assignments
    vessel_changes_cr = calculateVesselChanges(original_schedule, complete_reschedule);
    
    % Calculate weighted scores
    w1 = reschedule_params.MAKESPAN_WEIGHT;
    w2 = reschedule_params.START_TIME_WEIGHT;
    w3 = reschedule_params.VESSEL_CHANGE_WEIGHT;
    
    % Normalize metrics
    norm_makespan_diff_rs = (makespan_rs - makespan_orig) / makespan_orig;
    norm_makespan_diff_cr = (makespan_cr - makespan_orig) / makespan_orig;
    
    score_rs = w1 * norm_makespan_diff_rs + w2 * start_time_changes_rs + w3 * vessel_changes_rs;
    score_cr = w1 * norm_makespan_diff_cr + w2 * start_time_changes_cr + w3 * vessel_changes_cr;
    
    % Select strategy with lower score
    if score_rs <= score_cr
        selected_schedule = right_shift_schedule;
        strategy = 'right-shift';
    else
        selected_schedule = complete_reschedule;
        strategy = 'complete-reschedule';
    end
end

function changes = calculateStartTimeChanges(original_schedule, new_schedule)
    % Calculate the average change in task start times as a measure of schedule stability
    total_changes = 0;
    num_tasks = 0;
    
    % Map original tasks by id and type for quick lookup
    orig_task_map = containers.Map();
    for i = 1:length(original_schedule.tasks)
        task = original_schedule.tasks(i);
        key = sprintf('%s-%d', task.type, task.id);
        orig_task_map(key) = task;
    end
    
    % Calculate changes for tasks in new schedule
    for i = 1:length(new_schedule.tasks)
        new_task = new_schedule.tasks(i);
        key = sprintf('%s-%d', new_task.type, new_task.id);
        
        if isKey(orig_task_map, key)
            orig_task = orig_task_map(key);
            % Calculate absolute change in start time
            change = abs(new_task.start_time - orig_task.start_time);
            total_changes = total_changes + change;
            num_tasks = num_tasks + 1;
        end
    end
    
    % Return average change (normalized by 24 hours)
    if num_tasks > 0
        changes = (total_changes / num_tasks) / 24;
    else
        changes = 0;
    end
end

function changes = calculateVesselChanges(original_schedule, new_schedule)
    % Calculate the proportion of tasks that have been reassigned to different vessels
    total_changes = 0;
    num_tasks = 0;
    
    % Map original tasks by id and type for quick lookup
    orig_task_map = containers.Map();
    for i = 1:length(original_schedule.tasks)
        task = original_schedule.tasks(i);
        key = sprintf('%s-%d', task.type, task.id);
        orig_task_map(key) = task;
    end
    
    % Count vessel assignment changes
    for i = 1:length(new_schedule.tasks)
        new_task = new_schedule.tasks(i);
        key = sprintf('%s-%d', new_task.type, new_task.id);
        
        if isKey(orig_task_map, key)
            orig_task = orig_task_map(key);
            % Check if vessel assignment changed
            if new_task.vessel_id ~= orig_task.vessel_id
                total_changes = total_changes + 1;
            end
            num_tasks = num_tasks + 1;
        end
    end
    
    % Return proportion of changed assignments
    if num_tasks > 0
        changes = total_changes / num_tasks;
    else
        changes = 0;
    end
end

function [completed, remaining] = updateTasksStatus(schedule, current_time)
    % Update which tasks are completed and which are remaining at the current time
    
    completed_fdn_tasks = [];
    completed_wtb_tasks = [];
    remaining_fdn_tasks = [];
    remaining_wtb_tasks = [];
    
    for i = 1:length(schedule.tasks)
        task = schedule.tasks(i);
        
        if task.end_time <= current_time
            % Task is completed
            if strcmp(task.type, 'Foundation')
                completed_fdn_tasks = [completed_fdn_tasks, task];
            else % Turbine
                completed_wtb_tasks = [completed_wtb_tasks, task];
            end
        else
            % Task is remaining
            if strcmp(task.type, 'Foundation')
                remaining_fdn_tasks = [remaining_fdn_tasks, task];
            else % Turbine
                remaining_wtb_tasks = [remaining_wtb_tasks, task];
            end
        end
    end
    
    completed = {completed_fdn_tasks, completed_wtb_tasks};
    remaining = {remaining_fdn_tasks, remaining_wtb_tasks};
end

function [makespan, total_cost, vessel_utilization] = calculatePerformanceMetrics(schedule, vessels)
    % Calculate performance metrics: makespan, total cost, and vessel utilization
    
    % Calculate makespan (latest end time of any task)
    end_times = zeros(1, length(schedule.tasks));
    for i = 1:length(schedule.tasks)
        end_times(i) = schedule.tasks(i).end_time;
    end
    makespan = max(end_times);
    
    % Calculate vessel utilization and costs
    vessel_utilization = zeros(1, length(vessels));
    vessel_finish_times = zeros(1, length(vessels));
    
    % Create a map of tasks per vessel
    vessel_tasks = cell(length(vessels), 1);
    for i = 1:length(schedule.tasks)
        task = schedule.tasks(i);
        vessel_id = task.vessel_id;
        if vessel_id > 0 && vessel_id <= length(vessel_tasks)
            vessel_tasks{vessel_id} = [vessel_tasks{vessel_id}, task];
            
            % Update vessel finish time
            vessel_finish_times(vessel_id) = max(vessel_finish_times(vessel_id), task.end_time);
        end
    end
    
    % Calculate charter costs and utilization
    total_cost = 0;
    for v = 1:length(vessels)
        vessel = vessels(v);
        
        % If vessel is not used, skip it
        if isempty(vessel_tasks{v})
            vessel_utilization(v) = 0;
            continue;
        end
        
        % Calculate total active time for this vessel
        active_time = 0;
        v_tasks = vessel_tasks{v};
        for i = 1:length(v_tasks)
            task_duration = v_tasks(i).end_time - v_tasks(i).start_time;
            active_time = active_time + task_duration;
        end
        
        % Calculate utilization (active time / total time)
        vessel_utilization(v) = active_time / vessel_finish_times(v);
        
        % Calculate charter cost (charter rate * total time)
        vessel_cost = (vessel_finish_times(v) / 24) * vessel.charter_cost;
        total_cost = total_cost + vessel_cost;
    end
end

%% Visualization Functions

function plotGAConvergence(convergence_data, fig_handle)
    % Check if a figure handle was provided
    if nargin < 2
        % No figure handle provided, use current figure
        fig_handle = gcf;
    else
        % Use the provided figure handle
        figure(fig_handle);
    end
    
    % Check for valid data
    if isempty(convergence_data) || ~isfield(convergence_data, 'generation') || isempty(convergence_data.generation)
        disp('No valid convergence data to plot.');
        return;
    end

    % Clear the figure first to ensure we're working with a clean slate
    clf(fig_handle);
    
    % Plot best fitness and average fitness
    plot(convergence_data.generation, convergence_data.best_fitness, 'r-', 'LineWidth', 2);
    hold on;
    plot(convergence_data.generation, convergence_data.avg_fitness, 'Color', [0.7 0.7 0.7], 'LineWidth', 1.5);
    
    % Get max generations from convergence data
    if isfield(convergence_data, 'max_generations')
        max_generations = convergence_data.max_generations;
    else
        max_generations = convergence_data.generation(end);
    end
    
    conv_gen = max_generations;
    convergence_threshold = 0.01; % 1% improvement threshold
    
    % Calculate convergence generation
    for i = 2:length(convergence_data.best_fitness)
        if convergence_data.best_fitness(i-1) ~= 0
            rel_improvement = abs(convergence_data.best_fitness(i) - convergence_data.best_fitness(i-1)) / convergence_data.best_fitness(i-1);
        else
            rel_improvement = 0;
        end
        
        if i > 10 && rel_improvement < convergence_threshold
            if i+5 <= length(convergence_data.best_fitness)
                all_below = true;
                for j = 1:5
                    if convergence_data.best_fitness(i+j-1) ~= 0
                        next_improvement = abs(convergence_data.best_fitness(i+j) - convergence_data.best_fitness(i+j-1)) / convergence_data.best_fitness(i+j-1);
                    else
                        next_improvement = 0;
                    end
                    if next_improvement >= convergence_threshold
                        all_below = false;
                        break;
                    end
                end
                if all_below
                    conv_gen = i;
                    break;
                end
            end
        end
    end
    
    % Mark convergence point
    if conv_gen < max_generations
        plot(conv_gen, convergence_data.best_fitness(conv_gen), 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r');
        text(conv_gen, convergence_data.best_fitness(conv_gen), sprintf('  Convergence (Gen %d)', conv_gen), ...
            'VerticalAlignment', 'bottom', 'FontSize', 10);
    end
    
    % Add axis labels and title
    xlabel('Generation', 'FontSize', 12);
    ylabel('Fitness Value (Lower is Better)', 'FontSize', 12);
    title('Genetic Algorithm Convergence', 'FontSize', 14, 'FontWeight', 'bold');
    
    % Add legend
    legend('Best Fitness', 'Average Fitness', 'Location', 'northeast', 'FontSize', 10);
    grid on;
    
    % Set y-axis limits
    minVal = min(convergence_data.best_fitness);
    maxVal = max(convergence_data.avg_fitness);
    if minVal == maxVal
        ylim([minVal - 0.1, maxVal + 0.1]);
    else
        ylim([0.9*minVal, 1.1*maxVal]);
    end
    
    hold off;
end
function visualizeSchedule(schedule, vessels, reschedule_stats)
    % Enhanced visualization of the schedule as a Gantt chart
    if nargin < 3
        reschedule_stats = []; % Empty if not provided
    end
    
    % Get unique vessel IDs
    num_vessels = length(vessels);
    
    % Create figure
    figure('Position', [100, 100, 1200, 600], 'Name', 'Installation Schedule');
    
    % Colors for different task types
    colors = struct('Foundation', [0.3, 0.6, 0.9], 'Turbine', [0.9, 0.6, 0.3], 'Rescheduled', [0.2, 0.8, 0.3]);
    
    % Plot tasks as horizontal bars
    hold on;
    
    % Create a y-position for each vessel and task type
    vessel_positions = zeros(num_vessels, 2); % [Foundation position, Turbine position]
    for v = 1:num_vessels
        vessel_positions(v, 1) = v * 2 - 0.25; % Foundation position
        vessel_positions(v, 2) = v * 2 + 0.25; % Turbine position
    end
    
    % Mark rescheduled tasks if provided
    rescheduled_tasks = [];
    if ~isempty(reschedule_stats)
        % Find tasks affected by rescheduling
        for i = 1:length(reschedule_stats)
            event = reschedule_stats(i);
            event_time = event.time;
            
            % Consider tasks affected if they start after failure time on the failed vessel
            % or if they were reassigned in a complete rescheduling
            for j = 1:length(schedule.tasks)
                task = schedule.tasks(j);
                if (strcmp(event.strategy, 'right-shift') && task.vessel_id == event.vessel && task.start_time >= event_time) || ...
                   (strcmp(event.strategy, 'complete-reschedule') && task.start_time >= event_time)
                    rescheduled_tasks = [rescheduled_tasks j];
                end
            end
        end
        
        % Remove duplicates
        rescheduled_tasks = unique(rescheduled_tasks);
    end
    
    % Process all tasks
    for i = 1:length(schedule.tasks)
        task = schedule.tasks(i);
        v_id = task.vessel_id;
        
        % Skip tasks with invalid vessel ID
        if v_id <= 0 || v_id > num_vessels
            continue;
        end
        
        % Determine position based on task type
        if strcmp(task.type, 'Foundation')
            y_pos = vessel_positions(v_id, 1);
            color = colors.Foundation;
        else % Turbine
            y_pos = vessel_positions(v_id, 2);
            color = colors.Turbine;
        end
        
        % Check if task was rescheduled
        if ismember(i, rescheduled_tasks)
            % Add border to indicate rescheduling
            edgeColor = colors.Rescheduled;
            lineWidth = 2;
        else
            edgeColor = 'k';
            lineWidth = 1;
        end
        
        % Plot task bar
        bar_start = task.start_time;
        bar_width = task.end_time - task.start_time;
        rectangle('Position', [bar_start, y_pos-0.2, bar_width, 0.4], ...
            'FaceColor', color, 'EdgeColor', edgeColor, 'LineWidth', lineWidth);
        
        % Add task label with task ID
        task_label = '';
        if strcmp(task.type, 'Foundation')
            task_label = sprintf('F%d', task.id);
        else
            task_label = sprintf('T%d', task.id);
        end
        
        text(bar_start + bar_width/2, y_pos, task_label, ...
            'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
        
        % Plot operations as internal segments if detailed information available
        if isfield(task, 'operations') && ~isempty(fieldnames(task.operations))
            op_fields = fieldnames(task.operations);
            for j = 1:length(op_fields)
                op_name = op_fields{j};
                if isfield(task.operations, op_name) && isfield(task.operations.(op_name), 'start_time')
                    op = task.operations.(op_name);
                    if isfield(op, 'start_time') && isfield(op, 'end_time')
                        % Draw a thin line to separate operations
                        op_start = op.start_time;
                        if op_start > bar_start
                            line([op_start, op_start], [y_pos-0.2, y_pos+0.2], 'Color', 'k', 'LineStyle', ':');
                        end
                    end
                end
            end
        end
    end
    
    % Mark vessel failure periods if available
    if ~isempty(reschedule_stats)
        for i = 1:length(reschedule_stats)
            event = reschedule_stats(i);
            v_id = event.vessel;
            
            % Skip events with invalid vessel ID
            if v_id <= 0 || v_id > num_vessels
                continue;
            end
            
            failure_time = event.time;
            repair_time = event.repair_time;
            repair_end = failure_time + repair_time;
            
            % Create failure marker - red block for the vessel during repair time
            y_pos = vessel_positions(v_id, 1) - 0.5; % Position between vessel rows
            rectangle('Position', [failure_time, y_pos, repair_time, 1.5], ...
                'FaceColor', [1, 0.8, 0.8], 'EdgeColor', 'r', 'LineWidth', 1, 'FaceAlpha', 0.3);
            
            % Add failure label
            text(failure_time + repair_time/2, y_pos + 0.75, sprintf('Failure (%0.1f h)', repair_time), ...
                'HorizontalAlignment', 'center', 'Color', 'r', 'FontWeight', 'bold');
        end
    end
    
    % Set y-axis labels and ticks
    y_ticks = [];
    y_labels = {};
    for v = 1:num_vessels
        y_ticks = [y_ticks, v*2];
        y_labels{end+1} = sprintf('Vessel %d', v);
    end
    
    % Set axis properties
    set(gca, 'YTick', y_ticks, 'YTickLabel', y_labels);
    ylim([0, max(y_ticks) + 1]);
    
    % Convert hours to days in x-axis (with both scales)
    ax1 = gca;
    ax1_pos = ax1.Position;
    ax2 = axes('Position', ax1_pos, 'XAxisLocation', 'top', 'Color', 'none', 'YTick', []);
    linkaxes([ax1, ax2], 'x');
    
    % Set the second x-axis to show days
    if ~isempty(schedule.tasks)
        max_time = max([schedule.tasks.end_time]);
        ax2.XLim = ax1.XLim;
        ax2.XTick = 0:24:(ceil(max_time/24)*24);
        ax2.XTickLabel = 0:ceil(max_time/24);
        xlabel(ax2, 'Time (days)', 'FontSize', 12);
        xlabel(ax1, 'Time (hours)', 'FontSize', 12);
    end
    
    % Add grid and title
    grid on;
    title('Offshore Wind Farm Installation Schedule', 'FontSize', 14, 'FontWeight', 'bold');
    
    % Create legend markers - correct approach with dummy objects
    h1 = plot(NaN, NaN, 's', 'MarkerFaceColor', colors.Foundation, 'MarkerEdgeColor', 'k', 'MarkerSize', 10);
    h2 = plot(NaN, NaN, 's', 'MarkerFaceColor', colors.Turbine, 'MarkerEdgeColor', 'k', 'MarkerSize', 10);
    h3 = plot(NaN, NaN, 's', 'MarkerFaceColor', [1, 1, 1], 'MarkerEdgeColor', colors.Rescheduled, 'LineWidth', 2, 'MarkerSize', 10);
    h4 = plot(NaN, NaN, 's', 'MarkerFaceColor', [1, 0.8, 0.8], 'MarkerEdgeColor', 'r', 'MarkerSize', 10);
    
    % Add the legend
    legend ();
    
    hold off;
end

function plotConfigurationComparison(results)
    % Simple and robust version of configuration comparison
    if isempty(results)
        disp('No valid configuration results to plot.');
        text(0.5, 0.5, 'No valid configuration data', 'HorizontalAlignment', 'center', 'FontSize', 14);
        return;
    end
    
    % Extract data
    num_configs = length(results);
    makespans = zeros(1, num_configs);
    costs = zeros(1, num_configs);
    config_labels = cell(1, num_configs);
    
    try
        for i = 1:num_configs
            makespans(i) = results(i).makespan / 24; % Convert hours to days
            costs(i) = results(i).total_cost / 1000; % Scale down costs for display
            config_labels{i} = sprintf('C%d', i);
        end
        
        % Create grouped bar plot
        x = 1:num_configs;
        bar_width = 0.35;
        
        % Create left y-axis for makespan
        yyaxis left;
        makespan_bars = bar(x - bar_width/2, makespans, bar_width, 'FaceColor', [0.3, 0.6, 0.9]);
        
        % Add data labels to the bars
        for i = 1:length(makespans)
            text(x(i) - bar_width/2, makespans(i), sprintf('%.1f', makespans(i)), ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 9);
        end
        
        ylabel('Makespan (days)', 'FontSize', 12);
        set(gca, 'YColor', [0.3, 0.6, 0.9]);
        
        % Create right y-axis for cost
        yyaxis right;
        cost_bars = bar(x + bar_width/2, costs, bar_width, 'FaceColor', [0.9, 0.6, 0.3]);
        
        % Add data labels to the bars
        for i = 1:length(costs)
            text(x(i) + bar_width/2, costs(i), sprintf('%.1f', costs(i)), ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 9);
        end
        
        ylabel('Total Cost (thousands)', 'FontSize', 12);
        set(gca, 'YColor', [0.9, 0.6, 0.3]);
        
        % Set common properties
        xlabel('Configuration', 'FontSize', 12);
        set(gca, 'XTick', x, 'XTickLabel', config_labels);
        grid on;
        title('Comparison of Different Vessel Configurations', 'FontSize', 14, 'FontWeight', 'bold');
        
        % Add legend - with safer implementation
        h_legend = legend([makespan_bars, cost_bars], {'Makespan (days)', 'Total Cost'});
        set(h_legend, 'Location', 'northwest', 'FontSize', 10);
        
    catch e
        % In case of any error, show error message and return empty plot
        disp(['Error in plotConfigurationComparison: ', e.message]);
        cla; % Clear current axes
        text(0.5, 0.5, 'Error plotting configuration comparison', ...
            'HorizontalAlignment', 'center', 'FontSize', 14);
    end
end 
function plotRescheduleTimeline(reschedule_stats, makespan)
    % Plot a timeline of vessel failures and rescheduling events
    if isempty(reschedule_stats)
        disp('No rescheduling events to display.');
        return;
    end
    
    % Create figure
    figure('Position', [100, 100, 1200, 400], 'Name', 'Rescheduling Event Timeline');
    
    % Define colors
    right_shift_color = [1, 0.8, 0.2]; % Yellow for right-shift
    complete_reschedule_color = [1, 0.4, 0.4]; % Red for complete reschedule
    
    % Create the base timeline
    hold on;
    
    % Draw horizontal timeline
    timeline_y = 1;
    line([0, makespan], [timeline_y, timeline_y], 'Color', 'k', 'LineWidth', 2);
    
    % Add time markers
    time_interval = 24; % 24 hours = 1 day
    for t = 0:time_interval:makespan
        line([t, t], [timeline_y-0.1, timeline_y+0.1], 'Color', 'k', 'LineWidth', 1);
        text(t, timeline_y-0.2, sprintf('%d', t), 'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    
    % Add day markers (secondary scale)
    for d = 0:(makespan/24)
        t = d * 24;
        if t <= makespan
            line([t, t], [timeline_y-0.15, timeline_y+0.15], 'Color', 'k', 'LineWidth', 1.5);
            text(t, timeline_y-0.3, sprintf('Day %d', d), 'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold');
        end
    end
    
    % Plot each failure and recovery event
    vessel_y_positions = containers.Map('KeyType', 'double', 'ValueType', 'double');
    next_vessel_pos = timeline_y + 0.5;
    
    for i = 1:length(reschedule_stats)
        event = reschedule_stats(i);
        vessel_id = event.vessel;
        
        % Assign a y-position for this vessel if not already assigned
        if ~vessel_y_positions.isKey(vessel_id)
            vessel_y_positions(vessel_id) = next_vessel_pos;
            next_vessel_pos = next_vessel_pos + 1;
        end
        
        vessel_y = vessel_y_positions(vessel_id);
        
        % Plot failure point
        failure_time = event.time;
        repair_time = event.repair_time;
        repair_end = failure_time + repair_time;
        
        % Draw vertical line for failure
        line([failure_time, failure_time], [timeline_y, vessel_y], 'Color', 'r', 'LineWidth', 1.5);
        plot(failure_time, timeline_y, 'rv', 'MarkerFaceColor', 'r', 'MarkerSize', 8);
        text(failure_time, timeline_y+0.2, sprintf('Vessel %d (%0.1fh)', vessel_id, repair_time), ...
            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'Color', 'r', 'FontWeight', 'bold');
        
        % Draw repair period
        plot(repair_end, timeline_y, 'gv', 'MarkerFaceColor', 'g', 'MarkerSize', 8);
        line([repair_end, repair_end], [timeline_y, vessel_y], 'Color', 'g', 'LineWidth', 1.5, 'LineStyle', '--');
        text(repair_end, timeline_y+0.2, sprintf('Repair V%d', vessel_id), ...
            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'Color', 'g', 'FontWeight', 'bold');
        
        % Draw repair duration line
        line([failure_time, repair_end], [vessel_y, vessel_y], 'Color', 'r', 'LineWidth', 2);
        
        % Add repair time annotation
        mid_repair = (failure_time + repair_end) / 2;
        text(mid_repair, vessel_y+0.1, sprintf('%.1fh', repair_time), ...
            'HorizontalAlignment', 'center', 'Color', 'r', 'FontWeight', 'bold');
        
        % Draw impact region based on rescheduling strategy
       strategy = event.strategy;
    if strcmp(strategy, 'right-shift')
        % Right-shift affects from failure to makespan, but primarily vessel's tasks
        impact_height = 0.3;
        
        % Add this check to ensure width is always positive
        width = makespan - failure_time;
        if width <= 0
            width = 0.1;  % Set a small positive width instead
        end
        
        rectangle('Position', [failure_time, timeline_y-impact_height/2, width, impact_height], ...
            'FaceColor', right_shift_color, 'EdgeColor', 'none', 'FaceAlpha', 0.3);
        
            % Label
            text(failure_time + 5, timeline_y - impact_height, 'Right-Shift Rescheduling', ...
                'FontSize', 8, 'FontWeight', 'bold', 'Color', [0.7, 0.5, 0]);
        else
            % Complete reschedule has broader impact
            impact_height = 0.5;
             % Add similar check here too
        width = makespan - failure_time;
        if width <= 0
            width = 0.1;  % Set a small positive width
        end
        
        rectangle('Position', [failure_time, timeline_y-impact_height/2, width, impact_height], ...
            'FaceColor', complete_reschedule_color, 'EdgeColor', 'none', 'FaceAlpha', 0.3);
        
            % Label
            text(failure_time + 5, timeline_y - impact_height, 'Complete Rescheduling', ...
                'FontSize', 8, 'FontWeight', 'bold', 'Color', [0.7, 0, 0]);
        end
    end
    
    % Add vessel labels
    vessel_ids = cell2mat(vessel_y_positions.keys);
    for i = 1:length(vessel_ids)
        vessel_id = vessel_ids(i);
        vessel_y = vessel_y_positions(vessel_id);
        text(-makespan*0.05, vessel_y, sprintf('Vessel %d', vessel_id), ...
            'HorizontalAlignment', 'right', 'VerticalAlignment', 'middle', 'FontWeight', 'bold');
    end
    
    % Set axis properties
    xlim([-makespan*0.1, makespan*1.05]);
    ylim([timeline_y-1, next_vessel_pos]);
    set(gca, 'YTick', []);
    xlabel('Time (hours)', 'FontSize', 12);
    title('Vessel Failure and Rescheduling Timeline', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % Create legend
    h1 = plot(NaN, NaN, 'rv', 'MarkerFaceColor', 'r');
    h2 = plot(NaN, NaN, 'gv', 'MarkerFaceColor', 'g');
    h3 = rectangle('Position', [0, 0, 1, 1], 'FaceColor', right_shift_color, 'EdgeColor', 'none');
    h4 = rectangle('Position', [0, 0, 1, 1], 'FaceColor', complete_reschedule_color, 'EdgeColor', 'none');
    legend();
    
    hold off;
end

function plotVesselUtilization(schedule, vessels, reschedule_stats, makespan)
    % Create pie charts showing vessel utilization
    
    % Colors
    active_color = [0.3, 0.6, 0.9]; % Blue
    idle_color = [0.9, 0.9, 0.9];   % Light gray
    failed_color = [0.9, 0.3, 0.3]; % Red
    
    % Get number of vessels
    num_vessels = length(vessels);
    
    % Create a figure
    figure('Position', [100, 100, 800, 600], 'Name', 'Vessel Utilization');
    
    % Calculate rows and columns for subplots
    cols = min(3, num_vessels);
    rows = ceil(num_vessels / cols);
    
    % Process each vessel
    for v = 1:num_vessels
        % Create subplot
        subplot(rows, cols, v);
        
        % Extract tasks for this vessel
        vessel_tasks = [];
        for i = 1:length(schedule.tasks)
            if schedule.tasks(i).vessel_id == v
                vessel_tasks = [vessel_tasks, schedule.tasks(i)];
            end
        end
        
        % Calculate time utilization
        active_time = 0;
        for i = 1:length(vessel_tasks)
            task_duration = vessel_tasks(i).end_time - vessel_tasks(i).start_time;
            active_time = active_time + task_duration;
        end
        
        % Calculate failure time if available
        failed_time = 0;
        if ~isempty(reschedule_stats)
            for i = 1:length(reschedule_stats)
                if reschedule_stats(i).vessel == v
                    failed_time = failed_time + reschedule_stats(i).repair_time;
                end
            end
        end
        
        % Ensure vessel_finish_time is at least makespan
        vessel_finish_time = makespan;
        if ~isempty(vessel_tasks)
            task_end_times = [vessel_tasks.end_time];
            vessel_finish_time = max(makespan, max(task_end_times));
        end
        
        % Calculate idle time (finish time - active time - failed time)
        idle_time = vessel_finish_time - active_time - failed_time;
        if idle_time < 0
            % Adjust in case of overlapping time calculations
            idle_time = 0;
        end
        
        % Prepare data for pie chart
        labels = {};
        values = [];
        colors = [];
        
        % Add segments only if they have non-zero time
        if active_time > 0
            labels{end+1} = sprintf('Active: %.1fh (%.1f%%)', active_time, 100 * active_time / vessel_finish_time);
            values(end+1) = active_time;
            colors(end+1, :) = active_color;
        end
        
        if idle_time > 0
            labels{end+1} = sprintf('Idle: %.1fh (%.1f%%)', idle_time, 100 * idle_time / vessel_finish_time);
            values(end+1) = idle_time;
            colors(end+1, :) = idle_color;
        end
        
        if failed_time > 0
            labels{end+1} = sprintf('Failed: %.1fh (%.1f%%)', failed_time, 100 * failed_time / vessel_finish_time);
            values(end+1) = failed_time;
            colors(end+1, :) = failed_color;
        end
        
        % Create pie chart
        if ~isempty(values)
            pie_chart = pie(values);
            
            % Apply custom colors
            for i = 1:length(values)
                pie_segment = pie_chart((i-1)*2 + 1);
                set(pie_segment, 'FaceColor', colors(i,:));
            end
            
            % Add utilization percentage in center
            utilization_pct = 100 * active_time / vessel_finish_time;
            text(0, 0, sprintf('%.1f%%', utilization_pct), ...
                'HorizontalAlignment', 'center', 'FontSize', 14, 'FontWeight', 'bold');
        else
            text(0.5, 0.5, 'No data', 'HorizontalAlignment', 'center');
        end
        
        % Add title
        title(sprintf('Vessel %d Utilization', v), 'FontSize', 12, 'FontWeight', 'bold');
        
        % Add legend outside pie to avoid overlapping
        legend(labels, 'Location', 'southoutside', 'FontSize', 8);
    end
    
    % Add overall title
    sgtitle('Vessel Time Utilization Analysis', 'FontSize', 14, 'FontWeight', 'bold');
end

function plotCostBreakdown(results, vessel_values)
    % Create a cost breakdown visualization with error handling
    try
        % Check for valid inputs
        if isempty(results) || ~isfield(results, 'option') || ~isfield(results, 'total_cost')
            disp('Invalid results data for cost breakdown.');
            text(0.5, 0.5, 'Invalid results data', 'HorizontalAlignment', 'center', 'FontSize', 14);
            return;
        end
        
        % Extract data
        num_configs = length(results);
        config_labels = cell(1, num_configs);
        
        % Define colors for vessel types (3 types maximum)
        vessel_colors = [
            0.3, 0.6, 0.9;  % Vessel type 1 - Blue
            0.9, 0.6, 0.3;  % Vessel type 2 - Orange
            0.3, 0.9, 0.3   % Vessel type 3 - Green
        ];
        
        % Create cost data structure - simplified approach
        vessel_costs = zeros(3, num_configs); % 3 vessel types max, num_configs configurations
        
        % Fill in data
        for i = 1:num_configs
            config = results(i).option;
            config_labels{i} = sprintf('C%d', i);
            
            % Simplified cost calculation based on vessel types in config
            if length(config) >= 3
                fdn_vessel_type = config(2);
                wtb_vessel_type = config(3);
                total_cost = results(i).total_cost;
                
                % Ensure vessel types are valid
                if fdn_vessel_type > 3
                    fdn_vessel_type = 1;
                end
                if wtb_vessel_type > 3
                    wtb_vessel_type = 1;
                end
                
                % Simple approximation: split cost based on vessel types
                if fdn_vessel_type == wtb_vessel_type
                    % Same vessel type for both - assign all cost to that type
                    vessel_costs(fdn_vessel_type, i) = total_cost;
                else
                    % Different vessel types - split 50/50
                    vessel_costs(fdn_vessel_type, i) = total_cost * 0.5;
                    vessel_costs(wtb_vessel_type, i) = total_cost * 0.5;
                end
            end
        end
        
        % Scale costs for display
        vessel_costs = vessel_costs / 1000; % Scale to thousands
        
        % Create stacked bar chart
        bar_h = bar(1:num_configs, vessel_costs', 'stacked');
        
        % Set bar colors
        for v = 1:3
            set(bar_h(v), 'FaceColor', vessel_colors(v,:));
        end
        
        % Add data labels to bars (total only for simplicity)
        for i = 1:num_configs
            total = sum(vessel_costs(:, i));
            text(i, total + 0.1, sprintf('%.1f', total), ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
                'FontSize', 9, 'FontWeight', 'bold');
        end
        
        % Set labels and title
        xlabel('Configuration', 'FontSize', 12);
        ylabel('Cost (thousands)', 'FontSize', 12);
        title('Cost Breakdown by Vessel Type', 'FontSize', 14, 'FontWeight', 'bold');
        
        % Set x-axis ticks
        set(gca, 'XTick', 1:num_configs, 'XTickLabel', config_labels);
        
        % Add grid and legend
        grid on;
        legend({'Vessel Type 1', 'Vessel Type 2', 'Vessel Type 3'}, 'Location', 'northeast');
        
        % Add configuration details below x-axis
        for i = 1:num_configs
            if length(results(i).option) >= 3
                fdn_vessel = sprintf('F:V%d', results(i).option(2));
                turbine_vessel = sprintf('T:V%d', results(i).option(3));
                
                % Add text below x-axis
                text(i, -max(sum(vessel_costs))*0.05, [fdn_vessel, ', ', turbine_vessel], ...
                    'HorizontalAlignment', 'center', 'FontSize', 8, 'Rotation', 45);
            end
        end
        
        % Highlight the best (lowest cost) configuration
        [~, best_idx] = min(sum(vessel_costs));
        text(best_idx, sum(vessel_costs(:, best_idx)) + 2, '★ Best', ...
            'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
            'FontWeight', 'bold', 'Color', 'r', 'FontSize', 12);
            
    catch e
        % Display error message
        disp(['Error in plotCostBreakdown: ', e.message]);
        cla; % Clear current axes
        text(0.5, 0.5, 'Error plotting cost breakdown', ...
            'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', 'FontSize', 14);
    end
end
function runSensitivityAnalysis(fdn_tasks, wtb_tasks, vessels, farm_values, ga_params, best_config)
    % Run sensitivity analysis on failure rate and repair time
    
    % Define parameter ranges
    failure_rates = [0.05, 0.10, 0.15, 0.20, 0.25];
    repair_times = [12, 24, 36, 48, 60, 72];
    
    % Create result matrix
    results_matrix = zeros(length(repair_times), length(failure_rates));
    
    % Get baseline makespan without failures
    baseline_params = struct('ENABLE_FAILURES', false);
    [baseline_schedule, ~] = scheduleTasksGA(fdn_tasks, wtb_tasks, vessels, farm_values, ga_params);
    [baseline_makespan, ~, ~] = calculatePerformanceMetrics(baseline_schedule, vessels);
    
    % Run analysis for each parameter combination
    for i = 1:length(repair_times)
        for j = 1:length(failure_rates)
            % Set parameters for this run
            failure_params = struct(...
                'ENABLE_FAILURES', true, ...
                'FAILURE_PROBABILITY', failure_rates(j), ...
                'MIN_REPAIR_TIME', repair_times(i), ...
                'MAX_REPAIR_TIME', repair_times(i));
            
            reschedule_params = struct(...
                'MAKESPAN_WEIGHT', 0.6, ...
                'START_TIME_WEIGHT', 0.3, ...
                'VESSEL_CHANGE_WEIGHT', 0.1);
            
            % Run simulation
            fprintf('Running sensitivity analysis for failure rate %.2f, repair time %.1f hours...\n', ...
                failure_rates(j), repair_times(i));
            
            % Run multiple iterations to get average performance
            num_iterations = 3;
            makespans = zeros(1, num_iterations);
            
            for iter = 1:num_iterations
                [final_schedule, ~] = simulateWithFailures(baseline_schedule, vessels, ...
                    fdn_tasks, wtb_tasks, farm_values, failure_params, reschedule_params, ga_params);
                [makespan, ~, ~] = calculatePerformanceMetrics(final_schedule, vessels);
                makespans(iter) = makespan;
            end
            
            % Calculate average makespan and percentage increase
            avg_makespan = mean(makespans);
            pct_increase = 100 * (avg_makespan - baseline_makespan) / baseline_makespan;
            
            % Store result
            results_matrix(i, j) = pct_increase;
        end
    end
    
    % Visualize results as heatmap
    plotSensitivityHeatmap(results_matrix, failure_rates, repair_times);
end

function plotSensitivityHeatmap(results_matrix, failure_rates, repair_times)
    % Plot the sensitivity analysis results as a heatmap
    
    figure('Position', [100, 100, 800, 600], 'Name', 'Sensitivity Analysis');
    
    % Create heatmap
    imagesc(results_matrix);
    colormap(parula); % Use a colormap that's color-blind friendly
    colorbar;
    
    % Add labels
    xlabel('Failure Probability', 'FontSize', 12);
    ylabel('Repair Time (hours)', 'FontSize', 12);
    title('Makespan Increase (%) due to Vessel Failures', 'FontSize', 14, 'FontWeight', 'bold');
    
    % Set tick labels
    xticks(1:length(failure_rates));
    yticks(1:length(repair_times));
    xticklabels(arrayfun(@(x) sprintf('%.2f', x), failure_rates, 'UniformOutput', false));
    yticklabels(arrayfun(@(x) sprintf('%.0f', x), repair_times, 'UniformOutput', false));
    
    % Add data values in cells
    for i = 1:size(results_matrix, 1)
        for j = 1:size(results_matrix, 2)
            text(j, i, sprintf('%.1f%%', results_matrix(i, j)), ...
                'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold', ...
                'Color', 'white');
        end
    end
    
    % Add contour lines for better visibility of value regions
    hold on;
    [C, h] = contour(1:size(results_matrix, 2), 1:size(results_matrix, 1), results_matrix, 'k-');
    clabel(C, h, 'FontSize', 8, 'FontWeight', 'bold', 'Color', 'k');
    hold off;
    
    % Add grid lines
    grid on;
end

%% Main Loop - Process each configuration option (UPDATED WITH VISUALIZATIONS)
% Track convergence data for all configurations
all_convergence_data = cell(size(config_values, 1), 1);

for option_idx = 1:size(config_values, 1)
    option = config_values(option_idx, :);
    VT_FDN = option(2);  % Vessel type for foundations
    VT_WTB = option(3);  % Vessel type for turbines
    
    fprintf('Processing configuration option %d: Foundation vessel type %d, Turbine vessel type %d\n', ...
        option_idx, VT_FDN, VT_WTB);
    
    % Extract farm parameters
    N_FDN = farm_values(1);       % Number of foundations to install
    N_WTB = farm_values(2);       % Number of turbines to install
    WTB_D = farm_values(3);       % Turbine rotor diameter
    INT_D = farm_values(4);       % Distance between installation locations
    WD = farm_values(5);          % Water depth
    PEN_FDN = farm_values(6);     % Monopile penetration depth
    N_TOW = farm_values(7);       % Number of tower pieces per turbine
    N_BLADE = farm_values(8);     % Number of blades per turbine
    PARK_DIS = farm_values(9);    % Distance from harbor to farm
    % Create vessel objects with all relevant parameters
    vessels = createVesselObjects(vessel_values, operation_values, [VT_FDN, VT_WTB]);
    
    % Create task objects (foundations and turbines)
    [fdn_tasks, wtb_tasks] = createTaskObjects(N_FDN, N_WTB, vessels, farm_values, operation_values);
    
    % Initial scheduling using genetic algorithm
    fprintf('Running initial scheduling optimization...\n');
    [schedule, ~, convergence_data] = scheduleTasksGA(fdn_tasks, wtb_tasks, vessels, farm_values, GA_PARAMS);
    
    % Store convergence data for later plotting
    all_convergence_data{option_idx} = convergence_data;
    
    % Run simulation with potential failures and rescheduling
    if FAILURE_PARAMS.ENABLE_FAILURES
        fprintf('Running simulation with potential vessel failures...\n');
        [final_schedule, failure_stats] = simulateWithFailures(schedule, vessels, ...
            fdn_tasks, wtb_tasks, farm_values, FAILURE_PARAMS, RESCHEDULE_PARAMS, GA_PARAMS);
    else
        final_schedule = schedule;
        failure_stats = struct('num_failures', 0, 'reschedule_stats', []);
    end
    
    % Calculate performance metrics
    [makespan, total_cost, vessel_utilization] = calculatePerformanceMetrics(final_schedule, vessels);
    
    % Store results for this configuration
    results(option_idx).option = option;
    results(option_idx).makespan = makespan;
    results(option_idx).total_cost = total_cost;
    results(option_idx).vessel_utilization = vessel_utilization;
    results(option_idx).num_failures = failure_stats.num_failures;
    results(option_idx).reschedule_stats = failure_stats.reschedule_stats;
    results(option_idx).schedule = final_schedule;
    
    fprintf('Configuration %d completed: Makespan = %.2f days, Total cost = %.2f\n\n', ...
        option_idx, makespan/24, total_cost);
end
function safeRescheduleTimeline(reschedule_stats, makespan)
    % Plot a timeline of vessel failures and rescheduling events with safety checks
    try
        if isempty(reschedule_stats)
            disp('No rescheduling events to display.');
            text(0.5, 0.5, 'No rescheduling events to display', 'HorizontalAlignment', 'center', 'FontSize', 14);
            return;
        end
        
        % Create the base timeline
        hold on;
        
        % Define colors
        right_shift_color = [1, 0.8, 0.2]; % Yellow for right-shift
        complete_reschedule_color = [1, 0.4, 0.4]; % Red for complete reschedule
        
        % Draw horizontal timeline
        timeline_y = 1;
        line([0, makespan], [timeline_y, timeline_y], 'Color', 'k', 'LineWidth', 2);
        
        % Add time markers
        time_interval = 24; % 24 hours = 1 day
        for t = 0:time_interval:makespan
            line([t, t], [timeline_y-0.1, timeline_y+0.1], 'Color', 'k', 'LineWidth', 1);
            text(t, timeline_y-0.2, sprintf('%d', t), 'HorizontalAlignment', 'center', 'FontSize', 8);
        end
        
        % Add day markers (secondary scale)
        for d = 0:floor(makespan/24)
            t = d * 24;
            if t <= makespan
                line([t, t], [timeline_y-0.15, timeline_y+0.15], 'Color', 'k', 'LineWidth', 1.5);
                text(t, timeline_y-0.3, sprintf('Day %d', d), 'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold');
            end
        end
        
        % Plot each failure and recovery event
        vessel_y_positions = containers.Map('KeyType', 'double', 'ValueType', 'double');
        next_vessel_pos = timeline_y + 0.5;
        
        for i = 1:length(reschedule_stats)
            event = reschedule_stats(i);
            vessel_id = event.vessel;
            
            % Assign a y-position for this vessel if not already assigned
            if ~vessel_y_positions.isKey(vessel_id)
                vessel_y_positions(vessel_id) = next_vessel_pos;
                next_vessel_pos = next_vessel_pos + 1;
            end
            
            vessel_y = vessel_y_positions(vessel_id);
            
            % Plot failure point
            failure_time = event.time;
            repair_time = event.repair_time;
            repair_end = failure_time + repair_time;
            
            % Draw vertical line for failure
            line([failure_time, failure_time], [timeline_y, vessel_y], 'Color', 'r', 'LineWidth', 1.5);
            plot(failure_time, timeline_y, 'rv', 'MarkerFaceColor', 'r', 'MarkerSize', 8);
            
            % Draw repair period
            plot(repair_end, timeline_y, 'gv', 'MarkerFaceColor', 'g', 'MarkerSize', 8);
            line([repair_end, repair_end], [timeline_y, vessel_y], 'Color', 'g', 'LineWidth', 1.5, 'LineStyle', '--');
            
            % Draw repair duration line
            line([failure_time, repair_end], [vessel_y, vessel_y], 'Color', 'r', 'LineWidth', 2);
            
            % Draw impact region based on rescheduling strategy - WITH SAFETY CHECKS
            strategy = event.strategy;
            if strcmp(strategy, 'right-shift')
                % Right-shift affects from failure to makespan, but primarily vessel's tasks
                impact_height = 0.3;
                width = makespan - failure_time;
                if width <= 0
                    width = 1; % Set a positive width
                end
                
                rectangle('Position', [failure_time, timeline_y-impact_height/2, width, impact_height], ...
                    'FaceColor', right_shift_color, 'EdgeColor', 'none', 'FaceAlpha', 0.3);
            else
                % Complete reschedule has broader impact
                impact_height = 0.5;
                width = makespan - failure_time;
                if width <= 0
                    width = 1; % Set a positive width
                end
                
                rectangle('Position', [failure_time, timeline_y-impact_height/2, width, impact_height], ...
                    'FaceColor', complete_reschedule_color, 'EdgeColor', 'none', 'FaceAlpha', 0.3);
            end
        end
        
        % Set axis properties
        xlim([0, makespan*1.05]);
        ylim([timeline_y-1, next_vessel_pos]);
        xlabel('Time (hours)', 'FontSize', 12);
        title('Vessel Failure and Rescheduling Timeline', 'FontSize', 14, 'FontWeight', 'bold');
        grid on;
        
        % Create legend with dummy plots
        h1 = plot(NaN, NaN, 'rv', 'MarkerFaceColor', 'r');
        h2 = plot(NaN, NaN, 'gv', 'MarkerFaceColor', 'g');
        h3 = patch([NaN NaN NaN NaN], [NaN NaN NaN NaN], right_shift_color);
        h4 = patch([NaN NaN NaN NaN], [NaN NaN NaN NaN], complete_reschedule_color);
        
        % Create the legend - safer implementation
        h_legend = legend([h1, h2, h3, h4], {'Vessel Failure', 'Repair Complete', ...
            'Right-Shift Rescheduling', 'Complete Rescheduling'});
        set(h_legend, 'Location', 'northeastoutside');
        
        hold off;
    catch e
        % Display error message
        disp(['Error in safeRescheduleTimeline: ', e.message]);
        cla; % Clear current axes
        text(0.5, 0.5, 'Error plotting rescheduling timeline', ...
            'HorizontalAlignment', 'center', 'FontSize', 14);
    end
end
function createSimpleGanttChart(schedule, vessels)
    % A simplified Gantt chart implementation that's more robust
    
    % Check if schedule has tasks
    if ~isfield(schedule, 'tasks') || isempty(schedule.tasks)
        text(0.5, 0.5, 'No tasks in schedule!', 'FontSize', 14, 'HorizontalAlignment', 'center');
        return;
    end
    
    % Get unique vessel IDs
    tasks = schedule.tasks;
    vessel_ids = unique([tasks.vessel_id]);
    num_vessels = length(vessel_ids);
    
    % Colors for different task types
    colors = struct('Foundation', [0.3, 0.6, 0.9], 'Turbine', [0.9, 0.6, 0.3]);
    
    % Plot tasks as horizontal bars
    hold on;
    
    % Create a y-position for each vessel and task type
    y_pos = 1;
    vessel_positions = containers.Map('KeyType', 'double', 'ValueType', 'any');
    
    for v = 1:num_vessels
        vessel_id = vessel_ids(v);
        vessel_positions(vessel_id) = struct('Foundation', y_pos, 'Turbine', y_pos + 0.5);
        y_pos = y_pos + 1.5;
    end
    
    % Calculate the max time for x-axis scaling
    end_times = [tasks.end_time];
    max_time = max(end_times);
    if isempty(max_time) || max_time <= 0
        max_time = 24; % Default 1 day if no valid time
    end
    
    % Process all tasks
    for i = 1:length(tasks)
        task = tasks(i);
        v_id = task.vessel_id;
        
        % Skip tasks with invalid vessel ID
        if ~vessel_positions.isKey(v_id)
            continue;
        end
        
        % Get task details
        task_type = task.type;
        if ~isfield(vessel_positions(v_id), task_type)
            continue; % Skip if position not defined for this task type
        end
        
        y_pos = vessel_positions(v_id).(task_type);
        color = colors.(task_type);
        
        % Plot task bar with safety checks
        bar_start = max(0, task.start_time); % Ensure not negative
        bar_width = task.end_time - task.start_time;
        if bar_width <= 0
            bar_width = 0.1; % Set minimum width to avoid errors
        end
        
        rectangle('Position', [bar_start, y_pos-0.2, bar_width, 0.4], ...
            'FaceColor', color, 'EdgeColor', 'k');
        
        % Add task label with task ID
        task_label = '';
        if strcmp(task.type, 'Foundation')
            task_label = sprintf('F%d', task.id);
        else
            task_label = sprintf('T%d', task.id);
        end
        
        text(bar_start + bar_width/2, y_pos, task_label, ...
            'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
    end
    
    % Set y-axis labels and ticks
    y_ticks = [];
    y_labels = {};
    vessel_ids_array = cell2mat(vessel_positions.keys);
    for i = 1:length(vessel_ids_array)
        v = vessel_ids_array(i);
        v_pos = mean([vessel_positions(v).Foundation, vessel_positions(v).Turbine]);
        y_ticks = [y_ticks, v_pos];
        y_labels{end+1} = sprintf('Vessel %d', v);
    end
    
    % Set axis properties
    set(gca, 'YTick', y_ticks, 'YTickLabel', y_labels);
    ylim([0, max(y_ticks) + 1]);
    xlim([0, max_time * 1.05]);
    
    % Convert hours to days in x-axis
    ax1 = gca;
    ax1_pos = ax1.Position;
    ax2 = axes('Position', ax1_pos, 'XAxisLocation', 'top', 'Color', 'none', 'YTick', []);
    
    % Set the second x-axis to show days
    ax2.XLim = [0, max_time * 1.05];
    ax2.XTick = 0:24:(ceil(max_time/24)*24);
    ax2.XTickLabel = 0:ceil(max_time/24);
    xlabel(ax2, 'Time (days)', 'FontSize', 12);
    xlabel(ax1, 'Time (hours)', 'FontSize', 12);
    
    % Add grid and title
    grid on;
    title('Offshore Wind Farm Installation Schedule', 'FontSize', 14, 'FontWeight', 'bold');
    
    % Create legend
    h1 = rectangle('Position', [0, 0, 1, 1], 'FaceColor', colors.Foundation);
    h2 = rectangle('Position', [0, 0, 1, 1], 'FaceColor', colors.Turbine);
    legend();
    
    hold off;
end
function plotSimpleVesselUtilization(schedule, vessels, reschedule_stats, makespan)
    % A simplified implementation of vessel utilization pie charts
    try
        % Colors
        active_color = [0.3, 0.6, 0.9]; % Blue
        idle_color = [0.9, 0.9, 0.9];   % Light gray
        failed_color = [0.9, 0.3, 0.3]; % Red
        
        % Get number of vessels
        num_vessels = length(vessels);
        
        % Calculate rows and columns for subplots
        cols = min(3, num_vessels);
        rows = ceil(num_vessels / cols);
        
        % Process each vessel
        for v = 1:num_vessels
            % Create subplot
            subplot(rows, cols, v);
            
            % Extract tasks for this vessel
            vessel_tasks = [];
            for i = 1:length(schedule.tasks)
                if schedule.tasks(i).vessel_id == v
                    vessel_tasks = [vessel_tasks, schedule.tasks(i)];
                end
            end
            
            % Calculate time utilization
            active_time = 0;
            for i = 1:length(vessel_tasks)
                task_duration = vessel_tasks(i).end_time - vessel_tasks(i).start_time;
                if task_duration > 0
                    active_time = active_time + task_duration;
                end
            end
            
            % Calculate failure time if available
            failed_time = 0;
            if ~isempty(reschedule_stats)
                for i = 1:length(reschedule_stats)
                    if reschedule_stats(i).vessel == v
                        failed_time = failed_time + reschedule_stats(i).repair_time;
                    end
                end
            end
            
            % Ensure vessel_finish_time is at least makespan
            vessel_finish_time = makespan;
            if ~isempty(vessel_tasks)
                task_end_times = [vessel_tasks.end_time];
                if ~isempty(task_end_times) && max(task_end_times) > 0
                    vessel_finish_time = max(makespan, max(task_end_times));
                end
            end
            
            % Calculate idle time (finish time - active time - failed time)
            idle_time = vessel_finish_time - active_time - failed_time;
            if idle_time < 0
                idle_time = 0;
            end
            
            % Prepare data for pie chart
            labels = {};
            values = [];
            colors = [];
            
            % Add segments only if they have non-zero time
            if active_time > 0
                labels{end+1} = sprintf('Active: %.1fh (%.1f%%)', active_time, 100 * active_time / vessel_finish_time);
                values(end+1) = active_time;
                colors(end+1, :) = active_color;
            end
            
            if idle_time > 0
                labels{end+1} = sprintf('Idle: %.1fh (%.1f%%)', idle_time, 100 * idle_time / vessel_finish_time);
                values(end+1) = idle_time;
                colors(end+1, :) = idle_color;
            end
            
            if failed_time > 0
                labels{end+1} = sprintf('Failed: %.1fh (%.1f%%)', failed_time, 100 * failed_time / vessel_finish_time);
                values(end+1) = failed_time;
                colors(end+1, :) = failed_color;
            end
            
            % Create pie chart
            if ~isempty(values)
                % Use simple implementation for safety
                pie_chart = pie(values);
                
                % Apply colors safely
                for i = 1:length(values)
                    if 2*i-1 <= length(pie_chart)
                        set(pie_chart(2*i-1), 'FaceColor', colors(i,:));
                    end
                end
                
                % Add utilization percentage in center
                utilization_pct = 100 * active_time / vessel_finish_time;
                text(0, 0, sprintf('%.1f%%', utilization_pct), ...
                    'HorizontalAlignment', 'center', 'FontSize', 14, 'FontWeight', 'bold');
            else
                text(0.5, 0.5, 'No data', 'HorizontalAlignment', 'center');
            end
            
            % Add title
            title(sprintf('Vessel %d Utilization', v), 'FontSize', 12, 'FontWeight', 'bold');
        end
        
        % Add overall title
        sgtitle('Vessel Time Utilization Analysis', 'FontSize', 14, 'FontWeight', 'bold');
    catch e
        % Display error message
        disp(['Error in plotSimpleVesselUtilization: ', e.message]);
        cla; % Clear current axes
        text(0.5, 0.5, 'Error plotting vessel utilization', ...
            'HorizontalAlignment', 'center', 'FontSize', 14);
    end
end
% Find the best configuration based on total cost
[~, best_idx] = min([results.total_cost]);
best_config = results(best_idx).option;
best_makespan = results(best_idx).makespan;
best_cost = results(best_idx).total_cost;

fprintf('\nBest configuration: Foundation vessel type %d, Turbine vessel type %d\n', ...
    best_config(2), best_config(3));
fprintf('Makespan: %.2f days\n', best_makespan/24);
fprintf('Total cost: %.2f\n', best_cost);

%% Generate all visualizations
%% Generate all visualizations

%% Generate all visualizations

% Close any existing figures first
close all;

% Create exactly the figures we need with specific handles
fig1 = figure('Position', [100, 100, 800, 500], 'Name', 'GA Convergence');
fig2 = figure('Position', [100, 100, 1200, 600], 'Name', 'Installation Schedule');
fig3 = figure('Position', [100, 100, 1000, 600], 'Name', 'Configuration Comparison');
fig5 = figure('Position', [100, 100, 800, 600], 'Name', 'Vessel Utilization');
fig7 = figure('Position', [100, 100, 900, 500], 'Name', 'Cost Breakdown');

% 1. Plot GA Convergence for the best configuration
figure(fig1);
try
    % Pass the figure handle to prevent creating a new figure
    plotGAConvergence(all_convergence_data{best_idx}, fig1);
    title(sprintf('GA Convergence for Best Configuration (Option %d)', best_idx));
catch e
    disp(['Error plotting GA convergence: ', e.message]);
    text(0.5, 0.5, 'Error plotting GA convergence', 'HorizontalAlignment', 'center', 'FontSize', 14);
end
% 2. Visualize the best schedule with enhanced Gantt chart
figure(fig2);
% Debug the schedule data
disp('Schedule details before visualization:');
disp(['Number of tasks: ', num2str(length(results(best_idx).schedule.tasks))]);
disp(['Vessel count: ', num2str(length(vessels))]);

% Use our simple Gantt chart implementation
try
    createSimpleGanttChart(results(best_idx).schedule, vessels);
catch e
    disp(['Error creating Gantt chart: ', e.message]);
    text(0.5, 0.5, 'Error creating Gantt chart', 'HorizontalAlignment', 'center', 'FontSize', 14);
end

% 3. Plot configuration comparison bar chart
figure(fig3);
try
    % Use our safer version if needed, otherwise just wrap the existing one in try-catch
    plotConfigurationComparison(results);
catch e
    disp(['Error in configuration comparison: ', e.message]);
    text(0.5, 0.5, 'Error creating configuration comparison', 'HorizontalAlignment', 'center', 'FontSize', 14);
end

% 4. Plot rescheduling event timeline for the best configuration (conditional)
if ~isempty(results(best_idx).reschedule_stats)
    fig4 = figure('Position', [100, 100, 1200, 400], 'Name', 'Rescheduling Timeline');
    try
        safeRescheduleTimeline(results(best_idx).reschedule_stats, best_makespan);
    catch e
        disp(['Error in reschedule timeline: ', e.message]);
        text(0.5, 0.5, 'Error plotting reschedule timeline', 'HorizontalAlignment', 'center', 'FontSize', 14);
    end
end

% 5. Plot vessel utilization pie charts for the best configuration
figure(fig5);
try
    % Use our simplified version
    plotSimpleVesselUtilization(results(best_idx).schedule, vessels, results(best_idx).reschedule_stats, best_makespan);
catch e
    disp(['Error in vessel utilization: ', e.message]);
    text(0.5, 0.5, 'Error plotting vessel utilization', 'HorizontalAlignment', 'center', 'FontSize', 14);
end

% 7. Plot cost breakdown chart
figure(fig7); 
try
    % Use our simplified version
    plotCostBreakdown(results, vessel_values);
catch e
    disp(['Error in cost breakdown: ', e.message]);
    clf(fig7); % Clear the figure
    axes('Parent', fig7); % Create axes for displaying text
    text(0.5, 0.5, 'Error plotting cost breakdown', ...
        'HorizontalAlignment', 'center', 'FontSize', 14, 'Parent', gca);
end