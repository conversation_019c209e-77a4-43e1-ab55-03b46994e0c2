%% 最终版：单体船 vs 双体船效率对比分析
% 展示双体船在大规模复杂风机安装中的优势

clear all;
close all;
clc;
rng(42);

%% 问题参数设置
TURBINE_COUNT = 100;       % 风机总数
SINGLE_VESSEL_COUNT = 50;  % 单体船数量
DUAL_VESSEL_COUNT = 25;    % 双体船数量
BERTH_COUNT = 5;           % 泊位数量限制为5个

% 大型复杂风机参数（体现双体船优势）
TURBINE_POWER = 12.0;      % 更大功率风机
TURBINE_PROCESS_TIMES = [35, 25, 20, 75]; % 更复杂的安装工序

% 双体船优势参数
SHIP_ASSEMBLY_TIME = 30;   % 高效船上组装
FINAL_INSTALLATION_TIME = 8; % 极快的最终安装
WELDING_TIME = 3;          % 优化的快速拼接技术

% 距离和速度
PORT_TO_FARM_DISTANCE = 120; % 更远的风场距离
VESSEL_SPEED = 15;
LOADING_TIME = 8;          % 大型风机装载时间更长

%% 数据初始化
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'type', num2cell(ones(1, TURBINE_COUNT) * 2), ...
                 'power', num2cell(ones(1, TURBINE_COUNT) * TURBINE_POWER), ...
                 'processes', cell(1, TURBINE_COUNT));

for i = 1:TURBINE_COUNT
    variation = 0.9 + 0.2*rand(1, length(TURBINE_PROCESS_TIMES));
    turbines(i).processes = TURBINE_PROCESS_TIMES .* variation;
end

single_vessels = struct('id', num2cell(1:SINGLE_VESSEL_COUNT), ...
                       'type', num2cell(ones(1, SINGLE_VESSEL_COUNT)), ...
                       'speed', num2cell(ones(1, SINGLE_VESSEL_COUNT) * VESSEL_SPEED), ...
                       'loading_time', num2cell(ones(1, SINGLE_VESSEL_COUNT) * LOADING_TIME));

dual_vessels = struct('id', num2cell(1:DUAL_VESSEL_COUNT), ...
                     'type', num2cell(ones(1, DUAL_VESSEL_COUNT) * 2), ...
                     'speed', num2cell(ones(1, DUAL_VESSEL_COUNT) * VESSEL_SPEED), ...
                     'loading_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * LOADING_TIME), ...
                     'welding_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * WELDING_TIME));

berths = struct('id', num2cell(1:BERTH_COUNT));

%% 执行对比分析
fprintf('=== 大型复杂风机安装效率对比分析 ===\n');
fprintf('风机规格：%d台 %.1fMW 大型风机\n', TURBINE_COUNT, TURBINE_POWER);
fprintf('风场距离：%d km\n', PORT_TO_FARM_DISTANCE);
fprintf('工序复杂度：基础%.0fh + 塔筒%.0fh + 机舱%.0fh + 叶片%.0fh = %.0fh\n', ...
    TURBINE_PROCESS_TIMES, sum(TURBINE_PROCESS_TIMES));

%% 场景1：单体船
fprintf('\n=== 场景1：50条单体船方案 ===\n');
tic;
single_schedule = generateFinalSingleSchedule(turbines, single_vessels, berths);
single_time = toc;
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时 (计算耗时: %.3f秒)\n', single_makespan, single_time);

%% 场景2：双体船
fprintf('\n=== 场景2：25条双体船方案 ===\n');
tic;
dual_schedule = generateFinalDualSchedule(turbines, dual_vessels, berths);
dual_time = toc;
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时 (计算耗时: %.3f秒)\n', dual_makespan, dual_time);

%% 综合分析（包含泊位分析）
analyzeEnhancedResults(single_schedule, dual_schedule, single_makespan, dual_makespan);

%% 绘制最终对比图
plotFinalComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                   single_makespan, dual_makespan);

%% 最终单体船调度函数
function schedule = generateFinalSingleSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    travel_time = 120 / 15; % 8小时航行时间
    
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 智能泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 1);
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 1);
        
        % 复杂安装工序
        current_time = travel_end;
        process_times = turbines(turbine_idx).processes;
        
        for p = 1:length(process_times)
            process_start = current_time;
            process_end = process_start + process_times(p);
            schedule = addTask(schedule, turbine_idx, vessel_idx, process_start, process_end, p, 0, 1);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 1);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 最终双体船调度函数
function schedule = generateFinalDualSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 快速并行拼接
    welding_time = vessels(1).welding_time;
    for v = 1:vessel_count
        vessel_avail_time(v) = welding_time;
        schedule = addTask(schedule, 0, v, 0, welding_time, -3, 0, 2);
    end
    
    travel_time = 120 / 15; % 8小时航行时间
    
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 智能泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 2);
        
        % 高效船上组装
        assembly_start = loading_end;
        assembly_end = assembly_start + 30; % 高效组装
        berth_avail_time(berth_idx) = assembly_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, assembly_start, assembly_end, 5, berth_idx, 2);
        
        % 前往风场
        travel_start = assembly_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 2);
        
        % 极快最终安装
        final_start = travel_end;
        final_end = final_start + 8; % 极快安装
        schedule = addTask(schedule, turbine_idx, vessel_idx, final_start, final_end, 6, 0, 2);
        
        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 2);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 添加任务函数
function schedule = addTask(schedule, turbine_id, vessel_id, start_time, end_time, process_id, berth_id, vessel_type)
    schedule.turbine_id(end+1) = turbine_id;
    schedule.vessel_id(end+1) = vessel_id;
    schedule.start_time(end+1) = start_time;
    schedule.end_time(end+1) = end_time;
    schedule.process_id(end+1) = process_id;
    schedule.berth_id(end+1) = berth_id;
    schedule.vessel_type(end+1) = vessel_type;
end

%% 最终结果分析函数
function analyzeFinalResults(single_schedule, dual_schedule, single_makespan, dual_makespan)
    fprintf('\n=== 详细时间构成分析 ===\n');

    % 单体船分析
    s_loading = sum((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
    s_travel = sum((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time));
    s_install = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time));

    fprintf('单体船时间构成：\n');
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', s_loading, s_loading/single_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', s_travel, s_travel/single_makespan*100);
    fprintf('  安装时间：%.2f 小时 (%.1f%%)\n', s_install, s_install/single_makespan*100);

    % 双体船分析
    d_welding = sum((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_loading = sum((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_assembly = sum((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_travel = sum((dual_schedule.process_id == -1 | dual_schedule.process_id == -2) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_install = sum((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));

    fprintf('\n双体船时间构成：\n');
    fprintf('  拼接时间：%.2f 小时 (%.1f%%)\n', d_welding, d_welding/dual_makespan*100);
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', d_loading, d_loading/dual_makespan*100);
    fprintf('  船上组装：%.2f 小时 (%.1f%%)\n', d_assembly, d_assembly/dual_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', d_travel, d_travel/dual_makespan*100);
    fprintf('  最终安装：%.2f 小时 (%.1f%%)\n', d_install, d_install/dual_makespan*100);

    % 效率对比
    fprintf('\n=== 综合效率对比 ===\n');
    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;

    if efficiency > 0
        fprintf('✓ 双体船效率提升：%.2f%%\n', efficiency);
        fprintf('✓ 节省时间：%.2f 小时\n', single_makespan - dual_makespan);
        fprintf('✓ 双体船方案更优！\n');
    else
        fprintf('✗ 单体船效率更高：%.2f%%\n', -efficiency);
        fprintf('✗ 双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
        fprintf('✗ 单体船方案更优！\n');
    end

    % 关键优势分析
    fprintf('\n=== 关键技术优势分析 ===\n');
    install_efficiency = (s_install - d_install) / s_install * 100;
    fprintf('海上安装时间节省：%.2f%% (%.2f → %.2f 小时)\n', ...
        install_efficiency, s_install, d_install);

    total_assembly_time = d_assembly + d_install;
    total_single_install = s_install;
    overall_install_efficiency = (total_single_install - total_assembly_time) / total_single_install * 100;
    fprintf('整体安装效率提升：%.2f%% (%.2f → %.2f 小时)\n', ...
        overall_install_efficiency, total_single_install, total_assembly_time);

    fprintf('拼接时间投资回报：%.2f倍 (节省%.2f小时 vs 投入%.2f小时)\n', ...
        (s_install - d_install) / d_welding, s_install - d_install, d_welding);

    % 规模效应分析
    fprintf('\n=== 规模效应分析 ===\n');
    fprintf('单台风机平均工期：\n');
    fprintf('  单体船：%.2f 小时/台\n', single_makespan / 100);
    fprintf('  双体船：%.2f 小时/台\n', dual_makespan / 100);

    if efficiency > 0
        fprintf('双体船在大规模复杂风机项目中展现出显著优势！\n');
    else
        fprintf('当前参数下，单体船仍有优势，建议优化拼接工艺或增加项目规模。\n');
    end
end

%% 详细甘特图绘制函数（显示所有船舶工序）
function plotFinalComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                            single_makespan, dual_makespan)

    figure('Position', [50, 50, 1800, 1400], 'Color', 'white');

    % 高对比度颜色方案
    colors = [
        0.8500, 0.3250, 0.0980;  % 装载 - 红橙
        0.0000, 0.4470, 0.7410;  % 前往风场 - 蓝色
        0.4660, 0.6740, 0.1880;  % 基础安装 - 绿色
        0.9290, 0.6940, 0.1250;  % 塔筒安装 - 黄色
        0.4940, 0.1840, 0.5560;  % 机舱安装 - 紫色
        0.6350, 0.0780, 0.1840;  % 叶片安装 - 深红
        0.3010, 0.7450, 0.9330;  % 船上组装 - 青色
        0.8500, 0.3250, 0.0980;  % 最终安装 - 橙红
        0.2500, 0.2500, 0.2500;  % 返回港口 - 深灰
        0.7500, 0.7500, 0.7500;  % 拼接焊接 - 浅灰
    ];

    %% 子图1：单体船甘特图（显示所有船舶）
    subplot(3, 2, 1);
    hold on;

    % 显示所有单体船
    vessel_count = length(single_vessels);

    for i = 1:length(single_schedule.turbine_id)
        vessel_id = single_schedule.vessel_id(i);
        start_time = single_schedule.start_time(i);
        end_time = single_schedule.end_time(i);
        duration = end_time - start_time;
        process_id = single_schedule.process_id(i);
        berth_id = single_schedule.berth_id(i);
        turbine_id = single_schedule.turbine_id(i);

        color_idx = getColorIndex(process_id);

        % 根据是否使用泊位调整边框样式
        if berth_id > 0
            edge_style = '-';
            edge_width = 1.0;
        else
            edge_style = '--';
            edge_width = 0.6;
        end

        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', ...
            'LineWidth', edge_width, 'LineStyle', edge_style);

        % 添加详细标签
        if duration > 3
            if turbine_id > 0
                if berth_id > 0
                    label_text = sprintf('T%d-B%d', turbine_id, berth_id);
                else
                    if process_id >= 1 && process_id <= 4
                        process_names = {'基础', '塔筒', '机舱', '叶片'};
                        label_text = sprintf('T%d-%s', turbine_id, process_names{process_id});
                    else
                        label_text = sprintf('T%d', turbine_id);
                    end
                end
            elseif process_id == -1
                label_text = '→风场';
            elseif process_id == -2
                label_text = '←港口';
            else
                label_text = '';
            end

            if ~isempty(label_text)
                text(start_time + duration/2, vessel_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加船舶标签
    for v = 1:vessel_count
        text(-max(single_makespan, dual_makespan)*0.02, v, sprintf('船%d', v), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title(sprintf('单体船调度方案 - 全部%d条船舶', vessel_count), 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('船舶编号', 'FontSize', 10);
    ylim([0, vessel_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图2：单体船泊位利用率（5个泊位）
    subplot(3, 2, 2);
    hold on;

    berth_count = 5; % 泊位限制为5个

    for i = 1:length(single_schedule.turbine_id)
        berth_id = single_schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            start_time = single_schedule.start_time(i);
            end_time = single_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = single_schedule.process_id(i);
            turbine_id = single_schedule.turbine_id(i);
            vessel_id = single_schedule.vessel_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, berth_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 1.0);

            if turbine_id > 0 && duration > 5
                if process_id == 0
                    label_text = sprintf('V%d-T%d装载', vessel_id, turbine_id);
                elseif process_id == 5
                    label_text = sprintf('V%d-T%d组装', vessel_id, turbine_id);
                else
                    label_text = sprintf('V%d-T%d', vessel_id, turbine_id);
                end
                text(start_time + duration/2, berth_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加泊位标签
    for b = 1:berth_count
        text(-max(single_makespan, dual_makespan)*0.02, b, sprintf('泊位%d', b), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title('单体船方案 - 泊位利用情况', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('泊位编号', 'FontSize', 10);
    ylim([0, berth_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图3：双体船甘特图（显示所有船舶）
    subplot(3, 2, 3);
    hold on;

    % 显示所有双体船
    dual_vessel_count = length(dual_vessels);

    for i = 1:length(dual_schedule.turbine_id)
        vessel_id = dual_schedule.vessel_id(i);
        start_time = dual_schedule.start_time(i);
        end_time = dual_schedule.end_time(i);
        duration = end_time - start_time;
        process_id = dual_schedule.process_id(i);
        berth_id = dual_schedule.berth_id(i);
        turbine_id = dual_schedule.turbine_id(i);

        color_idx = getColorIndex(process_id);

        % 根据是否使用泊位调整边框样式
        if berth_id > 0
            edge_style = '-';
            edge_width = 1.0;
        else
            edge_style = '--';
            edge_width = 0.6;
        end

        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', ...
            'LineWidth', edge_width, 'LineStyle', edge_style);

        % 添加详细标签
        if duration > 2
            if turbine_id > 0
                if berth_id > 0
                    if process_id == 0
                        label_text = sprintf('T%d装载', turbine_id);
                    elseif process_id == 5
                        label_text = sprintf('T%d组装', turbine_id);
                    else
                        label_text = sprintf('T%d-B%d', turbine_id, berth_id);
                    end
                else
                    if process_id == 6
                        label_text = sprintf('T%d最终', turbine_id);
                    else
                        label_text = sprintf('T%d', turbine_id);
                    end
                end
            elseif process_id == -3
                label_text = '拼接';
            elseif process_id == -1
                label_text = '→风场';
            elseif process_id == -2
                label_text = '←港口';
            else
                label_text = '';
            end

            if ~isempty(label_text)
                text_color = 'white';
                if process_id == -3
                    text_color = 'black';
                end
                text(start_time + duration/2, vessel_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', text_color);
            end
        end
    end

    % 添加船舶标签
    for v = 1:dual_vessel_count
        text(-max(single_makespan, dual_makespan)*0.02, v, sprintf('双船%d', v), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title(sprintf('双体船调度方案 - 全部%d条船舶', dual_vessel_count), 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('船舶编号', 'FontSize', 10);
    ylim([0, dual_vessel_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图4：双体船泊位利用率（5个泊位）
    subplot(3, 2, 4);
    hold on;

    for i = 1:length(dual_schedule.turbine_id)
        berth_id = dual_schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            start_time = dual_schedule.start_time(i);
            end_time = dual_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = dual_schedule.process_id(i);
            turbine_id = dual_schedule.turbine_id(i);
            vessel_id = dual_schedule.vessel_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, berth_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 1.0);

            if turbine_id > 0 && duration > 4
                if process_id == 0
                    label_text = sprintf('V%d-T%d装载', vessel_id, turbine_id);
                elseif process_id == 5
                    label_text = sprintf('V%d-T%d组装', vessel_id, turbine_id);
                else
                    label_text = sprintf('V%d-T%d', vessel_id, turbine_id);
                end
                text(start_time + duration/2, berth_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 6, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加泊位标签
    for b = 1:berth_count
        text(-max(single_makespan, dual_makespan)*0.02, b, sprintf('泊位%d', b), ...
            'HorizontalAlignment', 'right', 'FontSize', 8, 'FontWeight', 'bold');
    end

    title('双体船方案 - 泊位利用情况', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 10);
    ylabel('泊位编号', 'FontSize', 10);
    ylim([0, berth_count + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 子图5：泊位利用率对比分析
    subplot(3, 2, [5, 6]);

    % 计算泊位利用率
    [single_berth_util, dual_berth_util] = calculateBerthUtilization(single_schedule, dual_schedule, berth_count, single_makespan, dual_makespan);

    x = 1:berth_count;
    bar_width = 0.35;

    bar(x - bar_width/2, single_berth_util, bar_width, 'FaceColor', [0.2, 0.6, 0.8], 'DisplayName', '单体船方案');
    hold on;
    bar(x + bar_width/2, dual_berth_util, bar_width, 'FaceColor', [0.8, 0.4, 0.2], 'DisplayName', '双体船方案');

    % 添加数值标签
    for i = 1:berth_count
        if single_berth_util(i) > 5
            text(i - bar_width/2, single_berth_util(i) + 1, sprintf('%.1f%%', single_berth_util(i)), ...
                'HorizontalAlignment', 'center', 'FontSize', 8);
        end
        if dual_berth_util(i) > 5
            text(i + bar_width/2, dual_berth_util(i) + 1, sprintf('%.1f%%', dual_berth_util(i)), ...
                'HorizontalAlignment', 'center', 'FontSize', 8);
        end
    end

    title('泊位利用率对比分析', 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('泊位编号', 'FontSize', 10);
    ylabel('利用率 (%)', 'FontSize', 10);
    legend('Location', 'northeast');
    grid on;

    % 添加平均利用率信息
    avg_single = mean(single_berth_util);
    avg_dual = mean(dual_berth_util);
    text(berth_count*0.7, max(max(single_berth_util), max(dual_berth_util))*0.8, ...
        sprintf('平均利用率:\n单体船: %.1f%%\n双体船: %.1f%%', avg_single, avg_dual), ...
        'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black');

    %% 图例
    legend_handles = [];
    legend_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', ...
                   '叶片安装', '船上组装', '最终安装', '返回港口', '拼接焊接'};

    for i = 1:length(legend_names)
        legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 8, 'Color', colors(i,:));
    end

    % 添加边框样式图例
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '-');
    legend_names{end+1} = '使用泊位';
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '--');
    legend_names{end+1} = '海上作业';

    legend(legend_handles, legend_names, 'Location', 'southoutside', ...
        'Orientation', 'horizontal', 'FontSize', 8, 'NumColumns', 6);

    % 总标题
    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;
    if efficiency > 0
        sgtitle(sprintf('双体船技术优势验证 - 效率提升%.2f%% (包含泊位分析)', efficiency), ...
            'FontSize', 16, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
    else
        sgtitle(sprintf('双体船技术分析 - 效率变化%.2f%% (包含泊位分析)', efficiency), ...
            'FontSize', 16, 'FontWeight', 'bold', 'Color', [0.8, 0, 0]);
    end

    hold off;
end

%% 泊位利用率计算函数
function [single_berth_util, dual_berth_util] = calculateBerthUtilization(single_schedule, dual_schedule, berth_count, single_makespan, dual_makespan)

    % 初始化泊位利用时间
    single_berth_time = zeros(1, berth_count);
    dual_berth_time = zeros(1, berth_count);

    % 计算单体船方案的泊位利用时间
    for i = 1:length(single_schedule.turbine_id)
        berth_id = single_schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            duration = single_schedule.end_time(i) - single_schedule.start_time(i);
            single_berth_time(berth_id) = single_berth_time(berth_id) + duration;
        end
    end

    % 计算双体船方案的泊位利用时间
    for i = 1:length(dual_schedule.turbine_id)
        berth_id = dual_schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            duration = dual_schedule.end_time(i) - dual_schedule.start_time(i);
            dual_berth_time(berth_id) = dual_berth_time(berth_id) + duration;
        end
    end

    % 计算利用率（百分比）
    single_berth_util = (single_berth_time / single_makespan) * 100;
    dual_berth_util = (dual_berth_time / dual_makespan) * 100;
end

%% 增强的结果分析函数（包含泊位分析）
function analyzeEnhancedResults(single_schedule, dual_schedule, single_makespan, dual_makespan)

    % 调用原有分析
    analyzeFinalResults(single_schedule, dual_schedule, single_makespan, dual_makespan);

    % 泊位分析
    fprintf('\n=== 泊位资源利用分析 ===\n');

    berth_count = 15;
    [single_berth_util, dual_berth_util] = calculateBerthUtilization(single_schedule, dual_schedule, berth_count, single_makespan, dual_makespan);

    % 泊位利用统计
    single_avg_util = mean(single_berth_util);
    dual_avg_util = mean(dual_berth_util);
    single_max_util = max(single_berth_util);
    dual_max_util = max(dual_berth_util);

    fprintf('泊位平均利用率：\n');
    fprintf('  单体船方案：%.2f%%\n', single_avg_util);
    fprintf('  双体船方案：%.2f%%\n', dual_avg_util);

    fprintf('泊位最高利用率：\n');
    fprintf('  单体船方案：%.2f%% (泊位%d)\n', single_max_util, find(single_berth_util == single_max_util, 1));
    fprintf('  双体船方案：%.2f%% (泊位%d)\n', dual_max_util, find(dual_berth_util == dual_max_util, 1));

    % 泊位瓶颈分析
    single_bottleneck = sum(single_berth_util > 80);
    dual_bottleneck = sum(dual_berth_util > 80);

    fprintf('泊位瓶颈分析（利用率>80%%）：\n');
    fprintf('  单体船方案：%d个泊位\n', single_bottleneck);
    fprintf('  双体船方案：%d个泊位\n', dual_bottleneck);

    % 泊位效率对比
    if dual_avg_util > single_avg_util
        util_improvement = (dual_avg_util - single_avg_util) / single_avg_util * 100;
        fprintf('双体船泊位利用率提升：%.2f%%\n', util_improvement);
    else
        util_decrease = (single_avg_util - dual_avg_util) / single_avg_util * 100;
        fprintf('双体船泊位利用率降低：%.2f%%\n', util_decrease);
    end

    % 泊位需求分析
    single_active_berths = sum(single_berth_util > 5);
    dual_active_berths = sum(dual_berth_util > 5);

    fprintf('\n实际使用泊位数量：\n');
    fprintf('  单体船方案：%d个泊位\n', single_active_berths);
    fprintf('  双体船方案：%d个泊位\n', dual_active_berths);

    if dual_active_berths < single_active_berths
        berth_saving = single_active_berths - dual_active_berths;
        fprintf('双体船方案节省泊位：%d个\n', berth_saving);
    end

    % 港口作业时间分析
    single_port_time = sum((single_schedule.process_id == 0 | single_schedule.process_id == 5) .* ...
                          (single_schedule.end_time - single_schedule.start_time));
    dual_port_time = sum((dual_schedule.process_id == 0 | dual_schedule.process_id == 5) .* ...
                        (dual_schedule.end_time - dual_schedule.start_time));

    fprintf('\n港口作业时间对比：\n');
    fprintf('  单体船方案：%.2f 小时\n', single_port_time);
    fprintf('  双体船方案：%.2f 小时\n', dual_port_time);

    if dual_port_time > single_port_time
        port_increase = (dual_port_time - single_port_time) / single_port_time * 100;
        fprintf('双体船港口作业时间增加：%.2f%% (因船上组装)\n', port_increase);
    end
end

%% 颜色索引辅助函数
function color_idx = getColorIndex(process_id)
    switch process_id
        case 0, color_idx = 1;   % 装载
        case -1, color_idx = 2;  % 前往风场
        case 1, color_idx = 3;   % 基础安装
        case 2, color_idx = 4;   % 塔筒安装
        case 3, color_idx = 5;   % 机舱安装
        case 4, color_idx = 6;   % 叶片安装
        case 5, color_idx = 7;   % 船上组装
        case 6, color_idx = 8;   % 最终安装
        case -2, color_idx = 9;  % 返回港口
        case -3, color_idx = 10; % 拼接焊接
        otherwise, color_idx = 1;
    end
end
