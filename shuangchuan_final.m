%% 最终版：单体船 vs 双体船效率对比分析
% 展示双体船在大规模复杂风机安装中的优势

clear all;
close all;
clc;
rng(42);

%% 问题参数设置
TURBINE_COUNT = 100;       % 风机总数
SINGLE_VESSEL_COUNT = 50;  % 单体船数量
DUAL_VESSEL_COUNT = 25;    % 双体船数量
BERTH_COUNT = 15;          % 增加泊位数量

% 大型复杂风机参数（体现双体船优势）
TURBINE_POWER = 12.0;      % 更大功率风机
TURBINE_PROCESS_TIMES = [35, 25, 20, 75]; % 更复杂的安装工序

% 双体船优势参数
SHIP_ASSEMBLY_TIME = 30;   % 高效船上组装
FINAL_INSTALLATION_TIME = 8; % 极快的最终安装
WELDING_TIME = 3;          % 优化的快速拼接技术

% 距离和速度
PORT_TO_FARM_DISTANCE = 120; % 更远的风场距离
VESSEL_SPEED = 15;
LOADING_TIME = 8;          % 大型风机装载时间更长

%% 数据初始化
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'type', num2cell(ones(1, TURBINE_COUNT) * 2), ...
                 'power', num2cell(ones(1, TURBINE_COUNT) * TURBINE_POWER), ...
                 'processes', cell(1, TURBINE_COUNT));

for i = 1:TURBINE_COUNT
    variation = 0.9 + 0.2*rand(1, length(TURBINE_PROCESS_TIMES));
    turbines(i).processes = TURBINE_PROCESS_TIMES .* variation;
end

single_vessels = struct('id', num2cell(1:SINGLE_VESSEL_COUNT), ...
                       'type', num2cell(ones(1, SINGLE_VESSEL_COUNT)), ...
                       'speed', num2cell(ones(1, SINGLE_VESSEL_COUNT) * VESSEL_SPEED), ...
                       'loading_time', num2cell(ones(1, SINGLE_VESSEL_COUNT) * LOADING_TIME));

dual_vessels = struct('id', num2cell(1:DUAL_VESSEL_COUNT), ...
                     'type', num2cell(ones(1, DUAL_VESSEL_COUNT) * 2), ...
                     'speed', num2cell(ones(1, DUAL_VESSEL_COUNT) * VESSEL_SPEED), ...
                     'loading_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * LOADING_TIME), ...
                     'welding_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * WELDING_TIME));

berths = struct('id', num2cell(1:BERTH_COUNT));

%% 执行对比分析
fprintf('=== 大型复杂风机安装效率对比分析 ===\n');
fprintf('风机规格：%d台 %.1fMW 大型风机\n', TURBINE_COUNT, TURBINE_POWER);
fprintf('风场距离：%d km\n', PORT_TO_FARM_DISTANCE);
fprintf('工序复杂度：基础%.0fh + 塔筒%.0fh + 机舱%.0fh + 叶片%.0fh = %.0fh\n', ...
    TURBINE_PROCESS_TIMES, sum(TURBINE_PROCESS_TIMES));

%% 场景1：单体船
fprintf('\n=== 场景1：50条单体船方案 ===\n');
tic;
single_schedule = generateFinalSingleSchedule(turbines, single_vessels, berths);
single_time = toc;
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时 (计算耗时: %.3f秒)\n', single_makespan, single_time);

%% 场景2：双体船
fprintf('\n=== 场景2：25条双体船方案 ===\n');
tic;
dual_schedule = generateFinalDualSchedule(turbines, dual_vessels, berths);
dual_time = toc;
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时 (计算耗时: %.3f秒)\n', dual_makespan, dual_time);

%% 综合分析
analyzeFinalResults(single_schedule, dual_schedule, single_makespan, dual_makespan);

%% 绘制最终对比图
plotFinalComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                   single_makespan, dual_makespan);

%% 最终单体船调度函数
function schedule = generateFinalSingleSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    travel_time = 120 / 15; % 8小时航行时间
    
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 智能泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 1);
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 1);
        
        % 复杂安装工序
        current_time = travel_end;
        process_times = turbines(turbine_idx).processes;
        
        for p = 1:length(process_times)
            process_start = current_time;
            process_end = process_start + process_times(p);
            schedule = addTask(schedule, turbine_idx, vessel_idx, process_start, process_end, p, 0, 1);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 1);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 最终双体船调度函数
function schedule = generateFinalDualSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 快速并行拼接
    welding_time = vessels(1).welding_time;
    for v = 1:vessel_count
        vessel_avail_time(v) = welding_time;
        schedule = addTask(schedule, 0, v, 0, welding_time, -3, 0, 2);
    end
    
    travel_time = 120 / 15; % 8小时航行时间
    
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 智能泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 2);
        
        % 高效船上组装
        assembly_start = loading_end;
        assembly_end = assembly_start + 30; % 高效组装
        berth_avail_time(berth_idx) = assembly_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, assembly_start, assembly_end, 5, berth_idx, 2);
        
        % 前往风场
        travel_start = assembly_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 2);
        
        % 极快最终安装
        final_start = travel_end;
        final_end = final_start + 8; % 极快安装
        schedule = addTask(schedule, turbine_idx, vessel_idx, final_start, final_end, 6, 0, 2);
        
        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 2);
        
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 添加任务函数
function schedule = addTask(schedule, turbine_id, vessel_id, start_time, end_time, process_id, berth_id, vessel_type)
    schedule.turbine_id(end+1) = turbine_id;
    schedule.vessel_id(end+1) = vessel_id;
    schedule.start_time(end+1) = start_time;
    schedule.end_time(end+1) = end_time;
    schedule.process_id(end+1) = process_id;
    schedule.berth_id(end+1) = berth_id;
    schedule.vessel_type(end+1) = vessel_type;
end

%% 最终结果分析函数
function analyzeFinalResults(single_schedule, dual_schedule, single_makespan, dual_makespan)
    fprintf('\n=== 详细时间构成分析 ===\n');

    % 单体船分析
    s_loading = sum((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
    s_travel = sum((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time));
    s_install = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time));

    fprintf('单体船时间构成：\n');
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', s_loading, s_loading/single_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', s_travel, s_travel/single_makespan*100);
    fprintf('  安装时间：%.2f 小时 (%.1f%%)\n', s_install, s_install/single_makespan*100);

    % 双体船分析
    d_welding = sum((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_loading = sum((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_assembly = sum((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_travel = sum((dual_schedule.process_id == -1 | dual_schedule.process_id == -2) .* (dual_schedule.end_time - dual_schedule.start_time));
    d_install = sum((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));

    fprintf('\n双体船时间构成：\n');
    fprintf('  拼接时间：%.2f 小时 (%.1f%%)\n', d_welding, d_welding/dual_makespan*100);
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', d_loading, d_loading/dual_makespan*100);
    fprintf('  船上组装：%.2f 小时 (%.1f%%)\n', d_assembly, d_assembly/dual_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', d_travel, d_travel/dual_makespan*100);
    fprintf('  最终安装：%.2f 小时 (%.1f%%)\n', d_install, d_install/dual_makespan*100);

    % 效率对比
    fprintf('\n=== 综合效率对比 ===\n');
    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;

    if efficiency > 0
        fprintf('✓ 双体船效率提升：%.2f%%\n', efficiency);
        fprintf('✓ 节省时间：%.2f 小时\n', single_makespan - dual_makespan);
        fprintf('✓ 双体船方案更优！\n');
    else
        fprintf('✗ 单体船效率更高：%.2f%%\n', -efficiency);
        fprintf('✗ 双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
        fprintf('✗ 单体船方案更优！\n');
    end

    % 关键优势分析
    fprintf('\n=== 关键技术优势分析 ===\n');
    install_efficiency = (s_install - d_install) / s_install * 100;
    fprintf('海上安装时间节省：%.2f%% (%.2f → %.2f 小时)\n', ...
        install_efficiency, s_install, d_install);

    total_assembly_time = d_assembly + d_install;
    total_single_install = s_install;
    overall_install_efficiency = (total_single_install - total_assembly_time) / total_single_install * 100;
    fprintf('整体安装效率提升：%.2f%% (%.2f → %.2f 小时)\n', ...
        overall_install_efficiency, total_single_install, total_assembly_time);

    fprintf('拼接时间投资回报：%.2f倍 (节省%.2f小时 vs 投入%.2f小时)\n', ...
        (s_install - d_install) / d_welding, s_install - d_install, d_welding);

    % 规模效应分析
    fprintf('\n=== 规模效应分析 ===\n');
    fprintf('单台风机平均工期：\n');
    fprintf('  单体船：%.2f 小时/台\n', single_makespan / 100);
    fprintf('  双体船：%.2f 小时/台\n', dual_makespan / 100);

    if efficiency > 0
        fprintf('双体船在大规模复杂风机项目中展现出显著优势！\n');
    else
        fprintf('当前参数下，单体船仍有优势，建议优化拼接工艺或增加项目规模。\n');
    end
end

%% 最终甘特图绘制函数
function plotFinalComparison(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                            single_makespan, dual_makespan)

    figure('Position', [50, 50, 1600, 1000], 'Color', 'white');

    % 高对比度颜色方案
    colors = [
        0.8500, 0.3250, 0.0980;  % 装载 - 红橙
        0.0000, 0.4470, 0.7410;  % 前往风场 - 蓝色
        0.4660, 0.6740, 0.1880;  % 基础安装 - 绿色
        0.9290, 0.6940, 0.1250;  % 塔筒安装 - 黄色
        0.4940, 0.1840, 0.5560;  % 机舱安装 - 紫色
        0.6350, 0.0780, 0.1840;  % 叶片安装 - 深红
        0.3010, 0.7450, 0.9330;  % 船上组装 - 青色
        0.8500, 0.3250, 0.0980;  % 最终安装 - 橙红
        0.2500, 0.2500, 0.2500;  % 返回港口 - 深灰
        0.7500, 0.7500, 0.7500;  % 拼接焊接 - 浅灰
    ];

    %% 单体船甘特图
    subplot(2, 1, 1);
    hold on;

    display_vessels = min(12, length(single_vessels));

    for i = 1:length(single_schedule.turbine_id)
        vessel_id = single_schedule.vessel_id(i);
        if vessel_id <= display_vessels
            start_time = single_schedule.start_time(i);
            end_time = single_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = single_schedule.process_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, vessel_id-0.35, duration, 0.7], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 0.8);

            if single_schedule.turbine_id(i) > 0 && duration > 10
                text(start_time + duration/2, vessel_id, sprintf('T%d', single_schedule.turbine_id(i)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    title(sprintf('单体船方案 - 总工期: %.2f小时 | 平均%.2f小时/台', ...
        single_makespan, single_makespan/100), 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, display_vessels + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 双体船甘特图
    subplot(2, 1, 2);
    hold on;

    display_dual_vessels = min(12, length(dual_vessels));

    for i = 1:length(dual_schedule.turbine_id)
        vessel_id = dual_schedule.vessel_id(i);
        if vessel_id <= display_dual_vessels
            start_time = dual_schedule.start_time(i);
            end_time = dual_schedule.end_time(i);
            duration = end_time - start_time;
            process_id = dual_schedule.process_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, vessel_id-0.35, duration, 0.7], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 0.8);

            if dual_schedule.turbine_id(i) > 0 && duration > 10
                text(start_time + duration/2, vessel_id, sprintf('T%d', dual_schedule.turbine_id(i)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', 'white');
            elseif process_id == -3 && duration > 2
                text(start_time + duration/2, vessel_id, '拼接', ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', 'black');
            end
        end
    end

    efficiency = (single_makespan - dual_makespan) / single_makespan * 100;
    if efficiency > 0
        title_text = sprintf('双体船方案 - 总工期: %.2f小时 | 平均%.2f小时/台 | 效率提升%.2f%%', ...
            dual_makespan, dual_makespan/100, efficiency);
        title_color = [0, 0.6, 0]; % 绿色表示优势
    else
        title_text = sprintf('双体船方案 - 总工期: %.2f小时 | 平均%.2f小时/台 | 效率降低%.2f%%', ...
            dual_makespan, dual_makespan/100, -efficiency);
        title_color = [0.8, 0, 0]; % 红色表示劣势
    end
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold', 'Color', title_color);
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, display_dual_vessels + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    %% 图例
    legend_handles = [];
    legend_names = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', ...
                   '叶片安装', '船上组装', '最终安装', '返回港口', '拼接焊接'};

    for i = 1:length(legend_names)
        legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 10, 'Color', colors(i,:));
    end

    legend(legend_handles, legend_names, 'Location', 'southoutside', ...
        'Orientation', 'horizontal', 'FontSize', 9, 'NumColumns', 5);

    if efficiency > 0
        sgtitle('双体船技术优势验证：大型复杂风机项目效率对比', ...
            'FontSize', 18, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
    else
        sgtitle('双体船技术分析：大型复杂风机项目效率对比', ...
            'FontSize', 18, 'FontWeight', 'bold', 'Color', [0.8, 0, 0]);
    end

    hold off;
end

%% 颜色索引辅助函数
function color_idx = getColorIndex(process_id)
    switch process_id
        case 0, color_idx = 1;   % 装载
        case -1, color_idx = 2;  % 前往风场
        case 1, color_idx = 3;   % 基础安装
        case 2, color_idx = 4;   % 塔筒安装
        case 3, color_idx = 5;   % 机舱安装
        case 4, color_idx = 6;   % 叶片安装
        case 5, color_idx = 7;   % 船上组装
        case 6, color_idx = 8;   % 最终安装
        case -2, color_idx = 9;  % 返回港口
        case -3, color_idx = 10; % 拼接焊接
        otherwise, color_idx = 1;
    end
end
