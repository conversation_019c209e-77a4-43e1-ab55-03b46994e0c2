%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with disruption handling and rescheduling.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;          % Vessel count
BERTH_COUNT = 2;            % Berth count
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours)
MAX_CAPACITY = 4;           % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;       % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;              % Population size
MAX_GEN = 100;              % Maximum generations
PSO_ITERATIONS = 50;        % PSO iterations
CROSSOVER_RATE = 0.8;       % Crossover rate
MUTATION_RATE_GA = 0.01;    % GA mutation rate
MUTATION_RATE_PSO = 0.2;    % PSO mutation rate
INERTIA_WEIGHT = 0.8;       % Inertia weight
C1 = 1.5;                   % Cognitive parameter
C2 = 1.5;                   % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 240;      % Time of disruption (hours)
REPAIR_TIME = 48;           % Repair time (hours)
AFFECTED_VESSEL = 3;        % Affected vessel ID

%% Data Structures
% Define turbine installation tasks
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;
end

% Define vessels
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES));

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

%% Right-Shift Rescheduling
fprintf('Applying right-shift rescheduling...\n');
right_shift_schedule = rightShiftRescheduling(initial_schedule, disruption);
makespan_right_shift = calculateMakespan(right_shift_schedule);
fprintf('Right-shift schedule makespan: %.2f hours\n', makespan_right_shift);

%% Complete Rescheduling
fprintf('Applying complete rescheduling...\n');
complete_schedule = completeRescheduling(initial_schedule, disruption, ...
    turbines, vessels, berths);
makespan_complete = calculateMakespan(complete_schedule);
fprintf('Complete rescheduling makespan: %.2f hours\n', makespan_complete);

%% Plot Results
% Plot three Gantt charts
plotGanttChart(initial_schedule, 'Initial Schedule', turbines, vessels, []);
plotGanttChart(right_shift_schedule, 'Right-Shift Rescheduling', turbines, vessels, disruption);
plotGanttChart(complete_schedule, 'Complete Rescheduling', turbines, vessels, disruption);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);
    
    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);
    
    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);
    
    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Check diversity
        diversity = calculateDiversity(fitness);
        
        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end
    
    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);
    
    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE
    global PORT_TO_FARM_DISTANCE;
    
    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm
        travel_start = loading_end;
        travel_end = travel_start + port_to_farm_time(vessel_idx);
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            process_end = process_start + process_times(p);
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);
    
    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));
    
    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);
    
    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));
    
    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));
    
    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);
    
    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;
    
    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function right_shift_schedule = rightShiftRescheduling(schedule, disruption)
    % Implement right-shift rescheduling strategy
    right_shift_schedule = schedule;
    
    % Find all tasks affected by disruption
    affected_vessel = disruption.affected_vessel;
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    
    % Find tasks of the affected vessel that start after disruption time
    affected_indices = find(schedule.vessel_id == affected_vessel & ...
                           schedule.start_time >= disruption_time);
    
    % Find tasks of the affected vessel that are ongoing during disruption
    ongoing_indices = find(schedule.vessel_id == affected_vessel & ...
                          schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);
    
    % Adjust ongoing tasks
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        
        % Calculate remaining time after disruption
        remaining_time = schedule.end_time(idx) - disruption_time;
        
        % Update end time
        right_shift_schedule.end_time(idx) = disruption_time + repair_time + remaining_time;
    end
    
    % Shift all subsequent tasks
    for i = 1:length(affected_indices)
        idx = affected_indices(i);
        duration = schedule.end_time(idx) - schedule.start_time(idx);
        
        % Apply right-shift
        right_shift_schedule.start_time(idx) = schedule.start_time(idx) + repair_time;
        right_shift_schedule.end_time(idx) = schedule.end_time(idx) + repair_time;
    end
end

function complete_schedule = completeRescheduling(schedule, disruption, turbines, vessels, berths)
    % Implement complete rescheduling strategy
    
    % Identify completed tasks (end time < disruption time)
    completed_indices = find(schedule.end_time <= disruption.time);
    
    % Identify ongoing tasks during disruption
    ongoing_indices = find(schedule.start_time < disruption.time & ...
                           schedule.end_time > disruption.time);
    
    % Prepare input for hybrid GA-PSO
    completed_turbines = unique(schedule.turbine_id(completed_indices));
    remaining_turbines_idx = setdiff(1:length(turbines), completed_turbines);
    remaining_turbines = turbines(remaining_turbines_idx);
    
    % Adjust vessel availability times based on disruption
    vessel_avail_time = zeros(1, length(vessels));
    for v = 1:length(vessels)
        if v == disruption.affected_vessel
            vessel_avail_time(v) = disruption.time + disruption.repair_time;
        else
            % Find last task end time before disruption
            last_task_idx = find(schedule.vessel_id == v & ...
                                schedule.end_time <= disruption.time, 1, 'last');
            if ~isempty(last_task_idx)
                vessel_avail_time(v) = schedule.end_time(last_task_idx);
            else
                vessel_avail_time(v) = 0;
            end
        end
    end
    
    % Generate new schedule for remaining tasks
    new_schedule = generateNewSchedule(remaining_turbines, vessels, berths, vessel_avail_time, disruption.time);
    
    % Combine completed and new schedule
    complete_schedule = mergeSchedules(schedule, new_schedule, completed_indices, disruption.time);
end

function new_schedule = generateNewSchedule(remaining_turbines, vessels, berths, vessel_avail_time, disruption_time)
    % Generate a new schedule for remaining turbines using a simplified version of hybrid GA-PSO
    global PORT_TO_FARM_DISTANCE; % Access global variable
    
    % Create a small-scale scheduling problem
    turbine_count = length(remaining_turbines);
    vessel_count = length(vessels);
    
    % Initialize population
    pop_size = 30;
    max_gen = 100;
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, pop_size);
    fitness = zeros(1, pop_size);
    
    % Generate initial population
    for i = 1:pop_size
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                             'vessel_assignment', vessel_assignment);
    end
    
    % Simple GA for quick rescheduling
    for gen = 1:max_gen
        % Evaluate population
        for i = 1:pop_size
            % Create schedule with adjusted start times
            temp_schedule = decodeChromosomeWithStartTime(population{i}, ...
                remaining_turbines, vessels, berths, vessel_avail_time, disruption_time);
            fitness(i) = calculateMakespan(temp_schedule);
        end
        
        % Select best solution
        [~, best_idx] = min(fitness);
        best_solution = population{best_idx};
        
        % Early termination if max_gen is small
        if gen == max_gen
            break;
        end
        
        % Create new population
        new_pop = cell(1, pop_size);
        new_pop{1} = best_solution;  % Elitism
        
        % Create rest of population using selection, crossover, mutation
        for i = 2:pop_size
            % Tournament selection
            tour_idx = randperm(pop_size, 2);
            if fitness(tour_idx(1)) < fitness(tour_idx(2))
                parent1_idx = tour_idx(1);
            else
                parent1_idx = tour_idx(2);
            end
            
            tour_idx = randperm(pop_size, 2);
            if fitness(tour_idx(1)) < fitness(tour_idx(2))
                parent2_idx = tour_idx(1);
            else
                parent2_idx = tour_idx(2);
            end
            
            % Simple crossover and mutation
            if rand() < 0.8  % Crossover rate
                child = crossover(population{parent1_idx}, population{parent2_idx}, turbine_count);
            else
                child = population{parent1_idx};
            end
            
            if rand() < 0.1  % Mutation rate
                child = mutate(child, vessel_count, turbine_count);
            end
            
            new_pop{i} = child;
        end
        
        population = new_pop;
    end
    
    % Return best schedule
    new_schedule = decodeChromosomeWithStartTime(best_solution, ...
        remaining_turbines, vessels, berths, vessel_avail_time, disruption_time);
end

function schedule = decodeChromosomeWithStartTime(chromosome, turbines, vessels, berths, vessel_avail_time, start_time)
    % Similar to decodeChromosome but with initial vessel availability times
    global PORT_TO_FARM_DISTANCE; % Access global variable
    
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize berth availability times
    berth_avail_time = ones(1, berth_count) * start_time;
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm
        travel_start = loading_end;
        travel_end = travel_start + port_to_farm_time(vessel_idx);
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            process_end = process_start + process_times(p);
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function combined_schedule = mergeSchedules(original_schedule, new_schedule, completed_indices, disruption_time)
    % Combine completed tasks with new schedule
    combined_schedule = struct('turbine_id', [], 'vessel_id', [], ...
                             'start_time', [], 'end_time', [], ...
                             'process_id', [], 'berth_id', []);
    
    % Copy completed tasks
    combined_schedule.turbine_id = original_schedule.turbine_id(completed_indices);
    combined_schedule.vessel_id = original_schedule.vessel_id(completed_indices);
    combined_schedule.start_time = original_schedule.start_time(completed_indices);
    combined_schedule.end_time = original_schedule.end_time(completed_indices);
    combined_schedule.process_id = original_schedule.process_id(completed_indices);
    combined_schedule.berth_id = original_schedule.berth_id(completed_indices);
    
    % Append new schedule
    combined_schedule.turbine_id = [combined_schedule.turbine_id, new_schedule.turbine_id];
    combined_schedule.vessel_id = [combined_schedule.vessel_id, new_schedule.vessel_id];
    combined_schedule.start_time = [combined_schedule.start_time, new_schedule.start_time];
    combined_schedule.end_time = [combined_schedule.end_time, new_schedule.end_time];
    combined_schedule.process_id = [combined_schedule.process_id, new_schedule.process_id];
    combined_schedule.berth_id = [combined_schedule.berth_id, new_schedule.berth_id];
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % Create a professional-looking Gantt chart
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    
    % Define color scheme for different process types
    process_colors = [
        0.9290, 0.6940, 0.1250;  % Loading (process 0)
        0.3010, 0.7450, 0.9330;  % Travel to farm (process -1)
        0.4940, 0.1840, 0.5560;  % Process 1
        0.4660, 0.6740, 0.1880;  % Process 2
        0.6350, 0.0780, 0.1840;  % Process 3
        0.8500, 0.3250, 0.0980;  % Process 4
        0.3010, 0.7450, 0.9330;  % Travel back (process -2)
    ];
    
    % Process names for legend
    process_names = {'Loading', 'Travel to Farm', 'Process 1', 'Process 2', 'Process 3', 'Process 4', 'Travel to Port'};
    
    % Create dummy objects for legend (to avoid the error)
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end
    
    % Plot vessels timeline
    hold on;
    
    % First plot disruption if provided
    if ~isempty(disruption)
        % Draw disruption as a semi-transparent red block
        disruption_area = patch([disruption.time, disruption.time + disruption.repair_time, ...
                               disruption.time + disruption.repair_time, disruption.time], ...
                              [0, 0, length(vessels)+1, length(vessels)+1], ...
                              'r', 'FaceAlpha', 0.2, 'EdgeColor', 'none');
        
        % Add text for disruption
        text(disruption.time + disruption.repair_time/2, length(vessels)+0.5, ...
            'Disruption', 'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold');
    end
    
    % Organize tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        
        y_pos = v;
        
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;
            
            % Determine process type
            process_id = schedule.process_id(task_idx);
            
            % Map process_id to color index
            if process_id == 0  % Loading
                color_idx = 1;
                process_label = 'L';
            elseif process_id == -1  % Travel to farm
                color_idx = 2;
                process_label = 'TF';
            elseif process_id == -2  % Travel back
                color_idx = 7;
                process_label = 'TB';
            else  % Regular processes
                color_idx = process_id + 2;
                process_label = ['P', num2str(process_id)];
            end
            
            % Draw the task bar
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);
            
            % Always add text label with proper scaling for short durations
            % Adjust font size based on duration
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end
            
            % Create label with both turbine ID and process type
            label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
            
            % For very short durations, use text rotation or external labels
            if duration < 5
                % Use external label with a line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw a small line connecting to the block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end
        
        % Add vessel label on y-axis
        text(-20, y_pos, sprintf('Vessel %d', v), ...
            'HorizontalAlignment', 'right', 'FontWeight', 'bold');
    end
    
    % Add berth timeline
    berth_count = max(schedule.berth_id);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            y_pos = y_start + b;
            
            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;
                
                % Draw the task bar (loading is always process_id 0)
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
                
                % Always add text labels with proper scaling
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end
                
                % Create label
                label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                
                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end
            
            % Add berth label on y-axis
            text(-20, y_pos, sprintf('Berth %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Add legend using dummy handles
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');
    
    % Set plot properties
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('Time (hours)', 'FontSize', 12);
    % No y-label needed as we have custom labels
    
    % Calculate makespan and display it
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('Makespan: %.2f hours', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    
    % Set axis limits
    xlim([-30, makespan * 1.05]);
    ylim([0, length(vessels) + berth_count + 3]);
    
    % Add gridlines
    grid minor;
    set(gca, 'YTick', []);  % Remove y-tick labels since we have custom labels
    
    hold off;
end