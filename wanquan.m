%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with disruption handling and rescheduling.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters 
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours)
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 50;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.17;  % PSO mutation rate
INERTIA_WEIGHT = 0.65;     % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 100;    % Time of disruption (hours) - changed to occur earlier
REPAIR_TIME = 48;         % Repair time (hours)
AFFECTED_VESSEL = 3;      % Affected vessel ID

%% 天气参数设置
% 创建指定天数的天气数据
SIMULATION_DAYS = 30;      % 模拟天数
HOURS_PER_DAY = 24;        % 每天小时数
TOTAL_HOURS = SIMULATION_DAYS * HOURS_PER_DAY; % 总小时数

% 创建全局天气数据变量
global WEATHER_DATA;

% 生成天气数据 (1 = 良好天气, 0 = 恶劣天气)
% 默认全部初始化为良好天气
WEATHER_DATA = ones(1, TOTAL_HOURS);

% 定义多个恶劣天气周期（例如，持续12-48小时的风暴）
bad_weather_starts = [2*24, 8*24, 15*24, 22*24, 27*24]; % 第2, 8, 15, 22, 27天
bad_weather_durations = [36, 24, 48, 18, 30]; % 小时

% 标记恶劣天气时段
for i = 1:length(bad_weather_starts)
    start_hour = bad_weather_starts(i);
    end_hour = min(start_hour + bad_weather_durations(i) - 1, TOTAL_HOURS);
    WEATHER_DATA(start_hour+1:end_hour+1) = 0; % 0表示恶劣天气
end

% 计算良好天气比例
good_weather_percentage = sum(WEATHER_DATA) / length(WEATHER_DATA) * 100;
fprintf('已生成天气数据: %.1f%% 良好天气, %.1f%% 恶劣天气\n', good_weather_percentage, 100-good_weather_percentage);

%% Data Structures
% Define turbine installation tasks
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;
end

% Define vessels
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES));

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

%% Complete Rescheduling
fprintf('Applying complete rescheduling...\n');
complete_schedule = completeRescheduling(initial_schedule, disruption, ...
    turbines, vessels, berths);
makespan_complete = calculateMakespan(complete_schedule);
fprintf('Complete rescheduling makespan: %.2f hours\n', makespan_complete);

%% Plot Results
% Plot two Gantt charts
plotGanttChart(initial_schedule, 'Initial Schedule', turbines, vessels, []);
plotGanttChart(complete_schedule, 'Complete Rescheduling', turbines, vessels, disruption);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

% Plot weather data
plotWeatherData();

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);
    
    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);
    
    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);
    
    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Check diversity
        diversity = calculateDiversity(fitness);
        
        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end
    
    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);
    
    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE
    global PORT_TO_FARM_DISTANCE;
    
    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        [loading_start, loading_end] = findNextFeasibleWindow(loading_start, vessels(vessel_idx).loading_time);
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm
        travel_start = loading_end;
        [travel_start, travel_end] = findNextFeasibleWindow(travel_start, port_to_farm_time(vessel_idx));
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            [process_start, process_end] = findNextFeasibleWindow(process_start, process_times(p));
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port
        travel_back_start = current_time;
        [travel_back_start, travel_back_end] = findNextFeasibleWindow(travel_back_start, port_to_farm_time(vessel_idx));
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);
    
    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));
    
    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);
    
    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));
    
    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));
    
    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);
    
    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;
    
    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function complete_schedule = completeRescheduling(schedule, disruption, turbines, vessels, berths)
    % 基于未施工工序的完全重调度
    % 将未开始的工序重新调度为新的优化问题
    
    % 获取故障时间点和修复时间
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    affected_vessel = disruption.affected_vessel;
    
    % 1. 识别已完成的任务（结束时间 < 故障时间）
    completed_indices = find(schedule.end_time <= disruption_time);
    
    % 2. 识别故障时正在进行的任务
    ongoing_indices = find(schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);
    
    % 3. 识别还未开始的任务
    future_indices = find(schedule.start_time >= disruption_time);
    
    % 从原始调度计划复制已完成的任务
    complete_schedule = struct('turbine_id', schedule.turbine_id(completed_indices), ...
                              'vessel_id', schedule.vessel_id(completed_indices), ...
                              'start_time', schedule.start_time(completed_indices), ...
                              'end_time', schedule.end_time(completed_indices), ...
                              'process_id', schedule.process_id(completed_indices), ...
                              'berth_id', schedule.berth_id(completed_indices));
    
    % 添加维修任务到调度计划中
    repair_task_idx = length(complete_schedule.turbine_id) + 1;
    complete_schedule.turbine_id(repair_task_idx) = 0; % 0表示非特定涡轮机
    complete_schedule.vessel_id(repair_task_idx) = affected_vessel;
    complete_schedule.start_time(repair_task_idx) = disruption_time;
    complete_schedule.end_time(repair_task_idx) = disruption_time + repair_time;
    complete_schedule.process_id(repair_task_idx) = -3; % -3 表示维修过程
    complete_schedule.berth_id(repair_task_idx) = 0;  % 维修不占用泊位
    
    % 处理正在进行的任务 - 调整其结束时间
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        remaining_time = schedule.end_time(idx) - disruption_time;
        
        new_end_time = disruption_time;
        if schedule.vessel_id(idx) == affected_vessel
            % 如果是故障船舶的任务，需要加上修复时间
            new_end_time = new_end_time + repair_time;
        end
        new_end_time = new_end_time + remaining_time;
        
        % 添加到新的调度计划
        complete_schedule.turbine_id(end+1) = schedule.turbine_id(idx);
        complete_schedule.vessel_id(end+1) = schedule.vessel_id(idx);
        complete_schedule.start_time(end+1) = schedule.start_time(idx);
        complete_schedule.end_time(end+1) = new_end_time;
        complete_schedule.process_id(end+1) = schedule.process_id(idx);
        complete_schedule.berth_id(end+1) = schedule.berth_id(idx);
    end
    
    % 提取未完成任务相关的涡轮机
    remaining_turbines = [];
    
    % 创建已完成任务的集合，用于快速查找
    completed_tasks = zeros(length(turbines), length(turbines(1).processes) + 3); % +3 for loading, travel to/from
    
    % 标记已完成和正在进行的任务
    % 修复: 确保将两个索引向量转换为列向量后再连接
    all_indices = [completed_indices(:); ongoing_indices(:)];
    
    for i = 1:length(all_indices)
        idx = all_indices(i);
        t_id = schedule.turbine_id(idx);
        
        % 跳过维修任务（turbine_id为0）
        if t_id == 0
            continue;
        end
        
        p_id = schedule.process_id(idx);
        
        % 将process_id映射到正确的索引
        if p_id == 0       % 装载
            p_idx = 1;
        elseif p_id == -1  % 前往风场
            p_idx = 2;
        elseif p_id == -2  % 返回港口
            p_idx = length(turbines(1).processes) + 3;
        else               % 正常工序
            p_idx = p_id + 2;
        end
        
        completed_tasks(t_id, p_idx) = 1;
    end
    
    % 提取未完成任务相关的涡轮机
    for t = 1:length(turbines)
        for p = 1:(length(turbines(1).processes) + 3)
            if completed_tasks(t, p) == 0
                if ~ismember(t, remaining_turbines)
                    remaining_turbines = [remaining_turbines, t];
                end
                break;
            end
        end
    end
    
    % 修复：正确创建simplified_turbines结构体数组
    simplified_turbines = struct('id', {}, 'processes', {});
    
    % 现在可以安全地添加元素
    for i = 1:length(remaining_turbines)
        t_id = remaining_turbines(i);
        % 使用结构体字段复制而不是直接赋值
       new_turbine = struct('id', turbines(t_id).id, 'processes', turbines(t_id).processes);
simplified_turbines(end+1) = new_turbine;
    end
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, length(vessels));
    berth_avail_time = zeros(1, length(berths));
    
    % 更新船舶和泊位可用时间
    for i = 1:length(complete_schedule.end_time)
        v_id = complete_schedule.vessel_id(i);
        vessel_avail_time(v_id) = max(vessel_avail_time(v_id), complete_schedule.end_time(i));
        
        b_id = complete_schedule.berth_id(i);
        if b_id > 0
            berth_avail_time(b_id) = max(berth_avail_time(b_id), complete_schedule.end_time(i));
        end
    end
    
    % 特别处理故障船舶的可用时间
    vessel_avail_time(affected_vessel) = max(vessel_avail_time(affected_vessel), disruption_time + repair_time);
    
    % 如果还有未完成的任务，调用hybridGAPSO进行优化
    if ~isempty(simplified_turbines)
        % 创建新的优化问题，并设置起始时间为disruption_time
        remaining_schedule = generateRemainingSchedule(simplified_turbines, vessels, berths, ...
                                                     vessel_avail_time, disruption_time);
        
        % 合并已完成/正在进行的任务和新优化的任务
        complete_schedule.turbine_id = [complete_schedule.turbine_id, remaining_schedule.turbine_id];
        complete_schedule.vessel_id = [complete_schedule.vessel_id, remaining_schedule.vessel_id];
        complete_schedule.start_time = [complete_schedule.start_time, remaining_schedule.start_time];
        complete_schedule.end_time = [complete_schedule.end_time, remaining_schedule.end_time];
        complete_schedule.process_id = [complete_schedule.process_id, remaining_schedule.process_id];
        complete_schedule.berth_id = [complete_schedule.berth_id, remaining_schedule.berth_id];
    end
end
function remaining_schedule = generateRemainingSchedule(turbines, vessels, berths, vessel_avail_time, start_time)
    % 使用修改后的hybridGAPSO为剩余任务生成调度计划
    
    % 获取问题规模
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % 获取全局参数
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % 定义较小的种群规模和迭代次数，加快重调度速度
    local_pop_size = max(20, min(POP_SIZE, 30));
    local_max_gen = max(50, min(MAX_GEN, 80));
    
    % 初始化种群
    population = cell(1, local_pop_size);
    fitness = zeros(1, local_pop_size);
    
    % 生成初始种群
    for i = 1:local_pop_size
        % 涡轮机安装顺序随机排列
        turbine_order = randperm(turbine_count);
        % 船舶分配随机选择
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % 初始化PSO参数
    pbest = population;
    pbest_fitness = Inf(1, local_pop_size);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, local_pop_size);
    
    % 初始化速度
    for i = 1:local_pop_size
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % 主循环
    for gen = 1:local_max_gen
        % 评估种群
        for i = 1:local_pop_size
            % 将染色体解码为调度计划，考虑船舶初始可用时间
            schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                turbines, vessels, berths, vessel_avail_time, start_time);
            % 计算工期
            fitness(i) = calculateMakespan(schedule_i);
            
            % 更新个体最优
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % 更新全局最优
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % 监控收敛性
        if mod(gen, 10) == 0
            fprintf('重调度Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % 计算种群多样性
        diversity = calculateDiversity(fitness);
        
        % 如果多样性低于阈值，应用PSO
        if diversity < DIVERSITY_THRESHOLD
            fprintf('重调度过程中检测到低多样性，在第%d代应用PSO...\n', gen);
            
            % 应用PSO进行局部搜索
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:local_pop_size
                    % 更新速度
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % 更新位置
                    % 对于涡轮机顺序（排列），使用特殊方法
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % 确保它仍然是有效的排列
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % 对于船舶分配
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % 确保有效（在1和vessel_count之间）
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % 以较高概率应用变异
                    if rand() < MUTATION_RATE_PSO
                        % 在涡轮机顺序中交换两个位置
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % 更新种群
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % 评估新解
                    schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                        turbines, vessels, berths, vessel_avail_time, start_time);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % 更新个体最优
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % 更新全局最优
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA操作（选择、交叉、变异）
            new_population = cell(1, local_pop_size);
            
            % 精英选择 - 保留最佳解
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % 对剩余个体进行选择、交叉和变异
            for i = 2:local_pop_size
                % 锦标赛选择
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % 交叉
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % 变异
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % 检查终止条件
        if gen >= local_max_gen
            break;
        end
    end
    
    % 将最佳解转换为调度计划
    remaining_schedule = decodeChromosomeWithStartTime(gbest, ...
        turbines, vessels, berths, vessel_avail_time, start_time);
end

function schedule = decodeChromosomeWithStartTime(chromosome, turbines, vessels, berths, vessel_avail_time, start_time)
    % 与decodeChromosome类似，但考虑初始可用时间
    global PORT_TO_FARM_DISTANCE;
    
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % 初始化调度数据结构
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化泊位可用时间
    berth_avail_time = ones(1, berth_count) * start_time;
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % 港口到风场的航行时间
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % 处理每个涡轮机
    for i = 1:turbine_count
        turbine_idx = turbines(i).id;  % 使用正确的涡轮机ID
        vessel_idx = vessel_assignment(i);
        
        % 获取此涡轮机的处理时间
        process_times = turbines(i).processes;  % 使用turbines(i)而不是turbine_idx
        num_processes = length(process_times);
        
        % 处理泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 计算装载开始时间
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        [loading_start, loading_end] = findNextFeasibleWindow(loading_start, vessels(vessel_idx).loading_time);
        
        % 更新泊位可用性
        berth_avail_time(berth_idx) = loading_end;
        
        % 添加装载操作到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0表示装载
        schedule.berth_id(end+1) = berth_idx;
        
        % 前往风场
        travel_start = loading_end;
        [travel_start, travel_end] = findNextFeasibleWindow(travel_start, port_to_farm_time(vessel_idx));
        
        % 添加航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1表示前往风场
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        
        % 处理安装任务
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            [process_start, process_end] = findNextFeasibleWindow(process_start, process_times(p));
            
            % 添加流程到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0表示无泊位
            
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        [travel_back_start, travel_back_end] = findNextFeasibleWindow(travel_back_start, port_to_farm_time(vessel_idx));
        
        % 添加返回航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2表示返回港口
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        
        % 更新船舶可用性
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % 创建专业外观的甘特图
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    
    % 使用更柔和的颜色方案
    process_colors = [
        0.9290, 0.6940, 0.3250;  % 装载 (process 0) - 柔和橙色
        0.4660, 0.7740, 0.8880;  % 前往风场 (process -1) - 柔和蓝色
        0.6350, 0.5040, 0.7410;  % 工序 1 - 柔和紫色
        0.4660, 0.7410, 0.3880;  % 工序 2 - 柔和绿色
        0.8500, 0.3250, 0.3980;  % 工序 3 - 柔和红色
        0.9290, 0.6940, 0.1250;  % 工序 4 - 柔和黄色
        0.4660, 0.7740, 0.8880;  % 返回港口 (process -2) - 柔和蓝色
        1.0000, 0.3000, 0.3000;  % 维修 (process -3) - 醒目但不太刺眼的红色
    ];
    
    % 图例的过程名称，添加维修
    process_names = {'装载', '前往风场', '工序 1', '工序 2', '工序 3', '工序 4', '返回港口', '维修'};
    
    % 创建图例的虚拟对象
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end
    
    % 绘制船舶时间线
    hold on;
    
    % 如果有故障，添加故障标记线
    if ~isempty(disruption)
        % 绘制故障发生的垂直线
        line([disruption.time, disruption.time], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);
        
        % 添加故障标记文本
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end
    
    % 按船舶组织任务
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        y_pos = v;
        
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;
            
            % 确定过程类型
            process_id = schedule.process_id(task_idx);
            turbine_id = schedule.turbine_id(task_idx);
            
            % 判断是否是维修任务 - 只考虑故障船舶
            is_repair = false;
            if ~isempty(disruption) && v == disruption.affected_vessel
                if process_id == -3 || turbine_id == 0
                    is_repair = true;
                end
            end
            
            % 根据任务类型设置颜色和标签
            if is_repair
                color_idx = 8;  % 维修颜色索引
                process_label = 'R';
                label_text = '维修';
                
                % 调试输出
                fprintf('显示维修任务: 船舶%d, 开始时间=%.2f, 结束时间=%.2f\n', v, start_time, end_time);
            else
                % 常规任务的颜色映射
                if process_id == 0  % 装载
                    color_idx = 1;
                    process_label = 'L';
                elseif process_id == -1  % 前往风场
                    color_idx = 2;
                    process_label = 'TF';
                elseif process_id == -2  % 返回港口
                    color_idx = 7;
                    process_label = 'TB';
                else  % 常规工序
                    color_idx = process_id + 2;
                    process_label = ['P', num2str(process_id)];
                end
                
                % 常规任务的标签
                if turbine_id > 0
                    label_text = sprintf('T%d-%s', turbine_id, process_label);
                else
                    label_text = process_label;
                end
            end
            
            % 绘制任务条
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);
            
            % 根据持续时间调整字体大小
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end
            
            % 对于非常短的持续时间，使用文本旋转或外部标签
            if duration < 5
                % 使用带线的外部标签
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % 绘制连接到块的小线
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % 常规内部标签
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end
        
        % 在y轴添加船舶标签
        % 如果这是受影响的船舶，添加标记
        if ~isempty(disruption) && v == disruption.affected_vessel
            text(-20, y_pos, sprintf('船舶 %d (故障)', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r');
        else
            text(-20, y_pos, sprintf('船舶 %d', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % 添加泊位时间线
    berth_count = max(schedule.berth_id);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            y_pos = y_start + b;
            
            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;
                
                % 绘制任务条（装载始终是process_id 0）
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
                
                % 添加适当比例的文本标签
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end
                
                % 创建标签
                label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                
                if duration < 5
                    % 小持续时间的外部标签
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % 常规内部标签
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end
            
            % 在y轴添加泊位标签
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % 使用虚拟句柄添加图例
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');
    
    % 设置绘图属性
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    % 不需要y标签，因为我们有自定义标签
    
    % 计算工期并显示
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    
    % 设置轴限制 - 修正部分，确保显示关键时间范围
    % 找到关键时间点
    if ~isempty(disruption)
        % 显示故障前后的任务
        start_view = max(0, disruption.time - 100); % 显示故障前100小时
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % 对于初始调度，保持原始视图
        xlim([0, makespan * 1.05]);
    end
    ylim([0, length(vessels) + berth_count + 3]);
    
    % 添加更清晰的时间网格线
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');  
    if ~isempty(disruption)
        % 在故障时间附近添加网格线
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % 每20小时一个主网格线
    end
    
    set(gca, 'YTick', []);  % 移除y刻度标签，因为我们有自定义标签
    
    hold off;
end

function [start_time, end_time] = findNextFeasibleWindow(initial_start, duration)
    % 查找考虑天气条件的下一个可行时间窗口
    global WEATHER_DATA;
    
    % 初始化开始时间
    start_time = initial_start;
    
    % 如果超出天气数据时间范围，则假设为良好天气
    if start_time >= length(WEATHER_DATA)
        end_time = start_time + duration;
        return;
    end
    
    % 寻找能够容纳任务的连续良好天气窗口
    while true
        % 将start_time向上舍入到下一个小时边界（如果不是整点）
        hour_start = ceil(start_time);
        
        % 检查是否已超出天气数据
        if hour_start >= length(WEATHER_DATA)
            end_time = start_time + duration;
            return;
        end
        
        % 检查当前小时是否为良好天气
        if WEATHER_DATA(hour_start + 1) == 1  % +1因为MATLAB索引从1开始
            % 初始化连续良好天气计数器
            continuous_good_hours = 0;
            
            % 检查是否有连续的良好天气窗口
            for h = hour_start:min(hour_start + ceil(duration) - 1, length(WEATHER_DATA) - 1)
                if h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 1
                    continuous_good_hours = continuous_good_hours + 1;
                else
                    break;
                end
            end
            
            % 如果找到足够连续的良好天气小时数
            if continuous_good_hours >= duration
                end_time = start_time + duration;
                return;
            else
                % 跳过不良天气
                h = hour_start + continuous_good_hours;
                while h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 0
                    h = h + 1;
                end
                start_time = h;
            end
        else
            % 当前小时是不良天气，寻找下一个良好天气小时
            h = hour_start;
            while h + 1 <= length(WEATHER_DATA) && WEATHER_DATA(h + 1) == 0
                h = h + 1;
            end
            start_time = h;
        end
        
        % 如果超出天气数据，假设为良好天气
        if start_time >= length(WEATHER_DATA)
            end_time = start_time + duration;
            return;
        end
    end
end

function plotWeatherData()
    global WEATHER_DATA;
    
    % 创建天气数据图表
    figure('Position', [100, 100, 800, 300], 'Color', 'white');
    
    % 绘制天气数据
    subplot(2,1,1);
    
    % 创建条形图显示天气状况
    for i = 1:length(WEATHER_DATA)
        if WEATHER_DATA(i) == 1  % 良好天气
            bar(i-1, 1, 'FaceColor', [0.3, 0.6, 0.9], 'EdgeColor', 'none');
            hold on;
        else  % 恶劣天气
            bar(i-1, 1, 'FaceColor', [0.9, 0.3, 0.3], 'EdgeColor', 'none');
            hold on;
        end
    end
    
    % 设置属性
    title('天气状况变化', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('天气状况');
    xlabel('时间(小时)');
    ylim([0, 1.2]);
    xlim([0, length(WEATHER_DATA)]);
    
    % 添加自定义y刻度
    set(gca, 'YTick', [0, 1], 'YTickLabel', {'恶劣', '良好'});
    grid on;
    
    % 添加天数标记
    hold on;
    for day = 1:floor(length(WEATHER_DATA)/24)
        line([day*24, day*24], [0, 1.2], 'Color', [0.5, 0.5, 0.5], 'LineStyle', ':', 'LineWidth', 1);
        text(day*24, 1.1, sprintf('第%d天', day), 'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    
    % 添加天气分布子图
    subplot(2,1,2);
    weather_stats = [sum(WEATHER_DATA == 1), sum(WEATHER_DATA == 0)];
    bar_h = bar(weather_stats, 0.4, 'FaceColor', [0.4, 0.6, 0.8]);
    set(gca, 'XTick', [1, 2], 'XTickLabel', {'良好天气', '恶劣天气'});
    ylabel('小时数');
    title('天气分布统计', 'FontSize', 10);
    grid on;
    
    % 添加百分比标签
    good_pct = weather_stats(1) / sum(weather_stats) * 100;
    bad_pct = weather_stats(2) / sum(weather_stats) * 100;
    
    text(1, weather_stats(1)/2, sprintf('%.1f%%', good_pct), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'w');
    text(2, weather_stats(2)/2, sprintf('%.1f%%', bad_pct), ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'w');
    
    % 添加总小时数文本
    text(1, weather_stats(1)+5, sprintf('%d小时', weather_stats(1)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
    text(2, weather_stats(2)+5, sprintf('%d小时', weather_stats(2)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
    
    % 添加说明注解
    annotation('textbox', [0.15, 0.01, 0.7, 0.05], 'String', ...
        '注：安装、航行等离岸作业在恶劣天气期间无法执行', ...
        'EdgeColor', 'none', 'HorizontalAlignment', 'center', 'FontSize', 8);
end