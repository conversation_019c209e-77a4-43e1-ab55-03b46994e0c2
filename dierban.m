%% 优化版GA-PSO混合算法风机安装调度系统
% 针对大规模风场场景优化，增强甘特图显示

%% 参数设置
TURBINE_COUNT = 130;        % 风机数量
VESSEL_COUNT = 10;          % 船舶数量
BERTH_COUNT = 2;            % 泊位数量
PROCESS_TIMES = [20, 12, 10, 36]; % 四道工序时间(小时)
MAX_CAPACITY = 4;           % 船舶容量

% 距离与时间参数
PORT_TO_FARM_DISTANCE = 80; % 港口到风场距离(km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % 船舶速度均值(km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % 添加随机变化
TURBINE_DISTANCE = 1;       % 风机间距(km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % 基础装载时间
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % 随机调整

% 算法参数 - 针对大规模问题优化
POP_SIZE = 200;             % 种群大小
MAX_GENERATIONS = 100;      % 最大迭代次数
CROSSOVER_RATE = 0.85;      % 交叉概率
MUTATION_RATE = 0.03;       % 基本变异概率
DIVERSITY_THRESHOLD = 0.05; % 种群多样性阈值

% PSO参数
PSO_ITERATIONS = 30;        % PSO迭代次数
W_MAX = 0.9;                % 惯性权重最大值
W_MIN = 0.4;                % 惯性权重最小值
C1 = 1.5;                   % 个体学习因子
C2 = 1.5;                   % 全局学习因子

% 重调度参数
RESCHEDULE_THRESHOLD = 0.3; % 重调度策略选择阈值
FAILURE_TIME = 300;         % 故障发生时间
REPAIR_TIME = 48;           % 修复时间
FAILED_VESSEL = 2;          % 故障船舶ID

% 可视化参数
TASK_COLORS = hsv(VESSEL_COUNT); % 为每个船舶生成不同颜色
PROCESS_NAMES = {'基础安装', '塔架安装', '叶片安装', '调试'};
PROCESS_PATTERNS = {'-', ':', '--', '-.'};  % 工序线型

%% 算法初始化
fprintf('===== 启动大规模风场GA-PSO调度优化 (风机数量: %d, 船舶数量: %d) =====\n', TURBINE_COUNT, VESSEL_COUNT);
tic; % 开始计时

% 初始化种群 - 使用稀疏方式优化内存
fprintf('初始化种群...\n');
population = initializePopulation(POP_SIZE, TURBINE_COUNT, VESSEL_COUNT);

% 记录关键收敛数据
best_fitness_history = zeros(1, MAX_GENERATIONS);
avg_fitness_history = zeros(1, MAX_GENERATIONS);
diversity_history = zeros(1, MAX_GENERATIONS);
best_makespan_history = zeros(1, MAX_GENERATIONS);
pso_intervention_points = []; % 记录PSO介入点

% 计算初始种群适应度
fitness_values = zeros(1, POP_SIZE);
makespan_values = zeros(1, POP_SIZE);

% 串行计算适应度 (不使用parfor)
for i = 1:POP_SIZE
    [fitness_values(i), makespan_values(i)] = evaluateFitness(population(i, :), TURBINE_COUNT, VESSEL_COUNT, BERTH_COUNT, PROCESS_TIMES, MAX_CAPACITY, PORT_TO_FARM_DISTANCE, VESSEL_SPEEDS, TURBINE_DISTANCE, LOADING_TIMES);
end

% 初始化PSO粒子
particles = population;
velocities = zeros(size(population));
p_best = population;
p_best_fitness = fitness_values;
[g_best_fitness, idx] = max(fitness_values);
g_best = population(idx, :);

%% 主循环优化
fprintf('开始主循环优化...\n');

% 进化主循环
for gen = 1:MAX_GENERATIONS
    % 计算当前种群多样性
    diversity = calculateDiversity(fitness_values);
    
    % GA阶段
    % 选择
    parents = tournamentSelection(population, fitness_values, POP_SIZE);
    
    % 交叉
    offspring = crossover(parents, CROSSOVER_RATE, TURBINE_COUNT, VESSEL_COUNT);
    
    % 变异 - 使用自适应变异率
    mutation_rate = MUTATION_RATE * (1 + (1 - diversity) * 2); % 多样性低时提高变异率
    offspring = mutate(offspring, mutation_rate, TURBINE_COUNT, VESSEL_COUNT);
    
    % 计算子代适应度 (串行计算)
    offspring_fitness = zeros(1, size(offspring, 1));
    offspring_makespan = zeros(1, size(offspring, 1));
    
    for i = 1:size(offspring, 1)
        [offspring_fitness(i), offspring_makespan(i)] = evaluateFitness(offspring(i, :), TURBINE_COUNT, VESSEL_COUNT, BERTH_COUNT, PROCESS_TIMES, MAX_CAPACITY, PORT_TO_FARM_DISTANCE, VESSEL_SPEEDS, TURBINE_DISTANCE, LOADING_TIMES);
    end
    
    % 精英保留策略
    [population, fitness_values, makespan_values] = elitism(population, fitness_values, makespan_values, offspring, offspring_fitness, offspring_makespan);
    
    % 如果种群多样性低于阈值，执行PSO重升温
    if diversity < DIVERSITY_THRESHOLD
        fprintf('第%d代: 执行PSO重升温\n', gen);
        pso_intervention_points = [pso_intervention_points, gen];
        
        % 更新PSO参数
        w = W_MAX - (W_MAX - W_MIN) * gen / MAX_GENERATIONS;
        
        % PSO迭代
        for pso_iter = 1:PSO_ITERATIONS
            % 更新速度和位置
            for i = 1:POP_SIZE
                r1 = rand(size(population(i, :)));
                r2 = rand(size(population(i, :)));
                
                % 速度更新
                velocities(i, :) = w * velocities(i, :) + ...
                    C1 * r1 .* (p_best(i, :) - particles(i, :)) + ...
                    C2 * r2 .* (g_best - particles(i, :));
                
                % 位置更新
                particles(i, :) = updateParticlePosition(particles(i, :), velocities(i, :), TURBINE_COUNT, VESSEL_COUNT);
                
                % 评估新位置
                [particle_fitness, particle_makespan] = evaluateFitness(particles(i, :), TURBINE_COUNT, VESSEL_COUNT, BERTH_COUNT, PROCESS_TIMES, MAX_CAPACITY, PORT_TO_FARM_DISTANCE, VESSEL_SPEEDS, TURBINE_DISTANCE, LOADING_TIMES);
                
                % 更新个体最优
                if particle_fitness > p_best_fitness(i)
                    p_best(i, :) = particles(i, :);
                    p_best_fitness(i) = particle_fitness;
                    
                    % 更新全局最优
                    if particle_fitness > g_best_fitness
                        g_best = particles(i, :);
                        g_best_fitness = particle_fitness;
                    end
                end
            end
        end
        
        % 用PSO的p_best替换GA中最差的部分个体
        [~, worst_indices] = sort(fitness_values);
        replace_count = floor(POP_SIZE * 0.2); % 替换20%最差个体
        
        for i = 1:replace_count
            idx = worst_indices(i);
            population(idx, :) = p_best(i, :);
            [fitness_values(idx), makespan_values(idx)] = evaluateFitness(population(idx, :), TURBINE_COUNT, VESSEL_COUNT, BERTH_COUNT, PROCESS_TIMES, MAX_CAPACITY, PORT_TO_FARM_DISTANCE, VESSEL_SPEEDS, TURBINE_DISTANCE, LOADING_TIMES);
        end
    end
    
    % 记录迭代数据
    [best_fitness, best_idx] = max(fitness_values);
    best_fitness_history(gen) = best_fitness;
    avg_fitness_history(gen) = mean(fitness_values);
    diversity_history(gen) = diversity;
    best_makespan_history(gen) = makespan_values(best_idx);
    
    % 显示进度
    if mod(gen, 5) == 0 || gen == 1 || gen == MAX_GENERATIONS
        fprintf('第%3d代: 最佳适应度=%.4f, 最佳完工时间=%.1f小时, 多样性=%.4f\n', ...
            gen, best_fitness, makespan_values(best_idx), diversity);
    end
    
    % 如果收敛稳定，提前结束
    if gen > 20 && std(best_makespan_history(gen-9:gen)) < 0.5
        fprintf('算法已稳定收敛，提前结束于第%d代\n', gen);
        break;
    end
end

%% 获取最优调度方案
fprintf('\n优化完成，耗时: %.2f秒\n', toc);

% 获取最优调度方案
[~, best_idx] = max(fitness_values);
best_solution = population(best_idx, :);
best_makespan = makespan_values(best_idx);

% 解码最优方案获得调度
fprintf('解码最优调度方案...\n');
tic;
[original_schedule, berth_usage] = decodeScheduleDetailed(best_solution, TURBINE_COUNT, VESSEL_COUNT, BERTH_COUNT, PROCESS_TIMES, MAX_CAPACITY, PORT_TO_FARM_DISTANCE, VESSEL_SPEEDS, TURBINE_DISTANCE, LOADING_TIMES);
fprintf('解码完成，耗时: %.2f秒\n', toc);

fprintf('最佳方案完工时间: %.1f小时（%.1f天）\n', best_makespan, best_makespan/24);

%% 故障模拟和重调度
fprintf('\n===== 开始故障模拟与重调度 =====\n');
fprintf('故障时间: %.1f小时, 修复时间: %.1f小时, 故障船舶ID: %d\n', FAILURE_TIME, REPAIR_TIME, FAILED_VESSEL);

% 应用右移重调度策略
fprintf('执行右移重调度策略...\n');
tic;
right_shift_schedule = rightShiftReschedule(original_schedule, FAILED_VESSEL, FAILURE_TIME, REPAIR_TIME);
fprintf('右移重调度完成，耗时: %.2f秒\n', toc);

% 计算右移重调度的完工时间
right_shift_makespan = 0;
for i = 1:length(right_shift_schedule)
    if right_shift_schedule(i).end_time > right_shift_makespan
        right_shift_makespan = right_shift_schedule(i).end_time;
    end
end

% 计算增加的时间占比
right_shift_delay_ratio = (right_shift_makespan - best_makespan) / best_makespan;

% 计算需要完全重调度的条件
remaining_task_count = sum([original_schedule.end_time] > FAILURE_TIME);
remaining_ratio = remaining_task_count / length(original_schedule);
avg_process_time = mean(PROCESS_TIMES);
delay_vs_process_ratio = (right_shift_makespan - best_makespan) / (avg_process_time * remaining_task_count);

% 如果延迟比例超过阈值，执行完全重调度
execute_complete_reschedule = (delay_vs_process_ratio > RESCHEDULE_THRESHOLD);

if execute_complete_reschedule
    fprintf('右移策略延迟较大，执行完全重调度策略...\n');
    tic;
    % 由于完全重调度计算量大，简化问题规模，仅重调度未完成任务
    remaining_turbines = [];
    for i = 1:length(original_schedule)
        if original_schedule(i).end_time > FAILURE_TIME
            remaining_turbines = [remaining_turbines, original_schedule(i).turbine_id];
        end
    end
    
    % 使用简化的完全重调度来降低计算复杂度
    complete_reschedule = simplifiedCompleteReschedule(original_schedule, remaining_turbines, FAILED_VESSEL, FAILURE_TIME, REPAIR_TIME, VESSEL_COUNT, BERTH_COUNT, PROCESS_TIMES, MAX_CAPACITY);
    fprintf('完全重调度完成，耗时: %.2f秒\n', toc);
else
    fprintf('右移策略延迟较小，无需完全重调度\n');
    complete_reschedule = right_shift_schedule;
end

% 计算完全重调度的完工时间
complete_makespan = 0;
for i = 1:length(complete_reschedule)
    if complete_reschedule(i).end_time > complete_makespan
        complete_makespan = complete_reschedule(i).end_time;
    end
end

fprintf('\n调度结果比较:\n');
fprintf('原始调度完工时间: %.1f小时 (%.1f天)\n', best_makespan, best_makespan/24);
fprintf('右移重调度完工时间: %.1f小时 (%.1f天, 增加: %.1f%%)\n', right_shift_makespan, right_shift_makespan/24, 100*(right_shift_makespan - best_makespan)/best_makespan);
fprintf('完全重调度完工时间: %.1f小时 (%.1f天, 增加: %.1f%%)\n', complete_makespan, complete_makespan/24, 100*(complete_makespan - best_makespan)/best_makespan);

%% 可视化 - 增强版甘特图
fprintf('\n===== 生成增强可视化图表 =====\n');

% 1. 绘制迭代收敛图
figure('Position', [100, 600, 900, 400], 'Name', 'GA-PSO算法收敛曲线');
valid_gens = 1:length(best_makespan_history);
valid_gens = valid_gens(best_makespan_history ~= 0); % 排除未执行的代数
best_makespan_history = best_makespan_history(best_makespan_history ~= 0);

plot(valid_gens, best_makespan_history, 'b-', 'LineWidth', 2);
hold on;
% 标记PSO介入点
for i = 1:length(pso_intervention_points)
    if pso_intervention_points(i) <= length(valid_gens)
        idx = find(valid_gens == pso_intervention_points(i));
        if ~isempty(idx)
            plot(valid_gens(idx), best_makespan_history(idx), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
            text(valid_gens(idx), best_makespan_history(idx)-20, 'PSO介入', ...
                'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
        end
    end
end
xlabel('迭代次数');
ylabel('最佳完工时间 (小时)');
title('优化收敛过程');
grid on;

% 2. 船舶分配分析
figure('Position', [100, 100, 900, 400], 'Name', '船舶任务分配');
vessel_loads = zeros(1, VESSEL_COUNT);
for i = 1:length(original_schedule)
    vessel_loads(original_schedule(i).vessel_id) = vessel_loads(original_schedule(i).vessel_id) + 1;
end
bar(1:VESSEL_COUNT, vessel_loads);
for i = 1:VESSEL_COUNT
    text(i, vessel_loads(i)+2, sprintf('%d台', vessel_loads(i)), ...
        'HorizontalAlignment', 'center', 'FontSize', 8);
end
xlabel('船舶ID');
ylabel('分配风机数量');
title('船舶工作负载分布');
grid on;

% 3. 绘制增强型甘特图
fprintf('生成增强甘特图...\n');

% 选择一定数量的任务展示，选择代表性样本
display_schedule = selectRepresentativeSchedule(original_schedule, 40);

% 创建增强型甘特图
figure('Position', [100, 100, 1200, 700], 'Name', '增强型甘特图');
plotEnhancedGantt(display_schedule, VESSEL_COUNT, BERTH_COUNT, TASK_COLORS, PROCESS_NAMES);

% 4. 重调度对比图
figure('Position', [100, 100, 800, 400], 'Name', '重调度效果对比');

makespans = [best_makespan, right_shift_makespan, complete_makespan];
bar_h = bar(makespans/24);
bar_h.FaceColor = 'flat';
bar_h.CData(1,:) = [0.2, 0.6, 0.8]; % 原始调度
bar_h.CData(2,:) = [0.8, 0.4, 0.2]; % 右移调度
bar_h.CData(3,:) = [0.2, 0.8, 0.4]; % 完全重调度

set(gca, 'XTick', 1:3, 'XTickLabel', {'原始调度', '右移重调度', '完全重调度'});
xlabel('调度方案');
ylabel('完工时间（天）');

for i = 1:length(makespans)
    text(i, makespans(i)/24 + 0.2, sprintf('%.1f天', makespans(i)/24), ...
        'HorizontalAlignment', 'center', 'FontSize', 9);
    
    if i > 1
        text(i, makespans(i)/24 - 0.5, sprintf('+%.1f%%', 100*(makespans(i)-makespans(1))/makespans(1)), ...
            'HorizontalAlignment', 'center', 'FontSize', 9, 'Color', 'r');
    end
end

title('重调度效果对比');
grid on;

% 5. 绘制故障发生前后的甘特图对比
if ~isempty(right_shift_schedule)
    % 筛选展示的任务
    right_shift_display = filterScheduleAroundFailure(right_shift_schedule, FAILURE_TIME, 30);
    
    % 创建故障重调度甘特图
    figure('Position', [100, 100, 1200, 700], 'Name', '故障重调度甘特图');
    plotEnhancedGantt(right_shift_display, VESSEL_COUNT, BERTH_COUNT, TASK_COLORS, PROCESS_NAMES, FAILURE_TIME, REPAIR_TIME, FAILED_VESSEL);
end

fprintf('可视化图表生成完毕。\n');
fprintf('\n===== 算法执行结束 =====\n');

%% 辅助函数
function population = initializePopulation(pop_size, turbine_count, vessel_count)
    % 初始化种群 - 优化内存使用
    population = zeros(pop_size, turbine_count * 2);
    
    for i = 1:pop_size
        % 船舶分配部分
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        
        % 操作顺序部分
        operation_sequence = randperm(turbine_count);
        
        % 合并
        population(i, :) = [vessel_assignment, operation_sequence];
    end
end

function [fitness, makespan] = evaluateFitness(chromosome, turbine_count, vessel_count, berth_count, process_times, max_capacity, port_to_farm_distance, vessel_speeds, turbine_distance, loading_times)
    % 计算适应度
    [schedule, makespan] = decodeSchedule(chromosome, turbine_count, vessel_count, berth_count, process_times, max_capacity, port_to_farm_distance, vessel_speeds, turbine_distance, loading_times);
    
    % 适应度计算 - 使用1/(1+Cmax)形式
    fitness = 1 / (1 + makespan/100);
end

function [schedule, makespan] = decodeSchedule(chromosome, turbine_count, vessel_count, berth_count, process_times, max_capacity, port_to_farm_distance, vessel_speeds, turbine_distance, loading_times)
    % 简化版调度解码 - 为大规模问题优化内存使用
    vessel_assignment = chromosome(1:turbine_count);
    operation_sequence = chromosome(turbine_count+1:end);
    
    % 初始化船舶和泊位状态
    vessel_end_times = zeros(1, vessel_count);
    vessel_capacity = zeros(1, vessel_count);
    vessel_location = zeros(1, vessel_count);
    berth_end_times = zeros(1, berth_count);
    
    % 初始化调度记录
    schedule = struct('turbine_id', {}, 'vessel_id', {}, 'start_time', {}, 'end_time', {});
    
    % 处理每个风机
    for i = 1:turbine_count
        turbine_id = operation_sequence(i);
        vessel_id = vessel_assignment(turbine_id);
        
        % 获取船舶状态
        current_time = vessel_end_times(vessel_id);
        current_capacity = vessel_capacity(vessel_id);
        current_location = vessel_location(vessel_id);
        
        % 检查是否需要装载
        if current_capacity >= max_capacity || current_capacity == 0
            % 需要回港
            if current_location > 0
                travel_time = port_to_farm_distance / vessel_speeds(vessel_id);
                current_time = current_time + travel_time;
            end
            
            % 等待泊位
            [min_end_time, earliest_berth_idx] = min(berth_end_times);
            if min_end_time > current_time
                current_time = min_end_time;
            end
            
            % 占用泊位装载
            loading_time = loading_times(vessel_id);
            berth_end_times(earliest_berth_idx) = current_time + loading_time;
            current_time = current_time + loading_time;
            current_capacity = 0;
            current_location = 0;
        end
        
        % 前往风场
        if current_location == 0
            travel_time = port_to_farm_distance / vessel_speeds(vessel_id);
            current_time = current_time + travel_time;
        elseif current_location > 0
            travel_time = turbine_distance * abs(current_location - turbine_id) / vessel_speeds(vessel_id);
            current_time = current_time + travel_time;
        end
        
        % 执行安装工序
        process_time = sum(process_times);
        start_time = current_time;
        current_time = current_time + process_time;
        
        % 记录任务
        task = struct('turbine_id', turbine_id, 'vessel_id', vessel_id, ...
                     'start_time', start_time, 'end_time', current_time);
        schedule = [schedule, task];
        
        % 更新船舶状态
        vessel_end_times(vessel_id) = current_time;
        vessel_capacity(vessel_id) = current_capacity + 1;
        vessel_location(vessel_id) = turbine_id;
    end
    
    % 计算最大完工时间
    if ~isempty(schedule)
        makespan = max([schedule.end_time]);
    else
        makespan = 0;
    end
end

function [schedule, berth_usage] = decodeScheduleDetailed(chromosome, turbine_count, vessel_count, berth_count, process_times, max_capacity, port_to_farm_distance, vessel_speeds, turbine_distance, loading_times)
    % 更详细的调度解码 - 用于最终解决方案
    vessel_assignment = chromosome(1:turbine_count);
    operation_sequence = chromosome(turbine_count+1:end);
    
    % 初始化船舶状态
    vessel_end_times = zeros(1, vessel_count);
    vessel_capacity = zeros(1, vessel_count);
    vessel_location = zeros(1, vessel_count);
    vessel_load_complete = zeros(1, vessel_count);
    
    % 初始化泊位状态
    berth_end_times = zeros(1, berth_count);
    berth_usage = zeros(1, berth_count);
    
    % 初始化调度
    schedule = struct('turbine_id', {}, 'vessel_id', {}, 'start_time', {}, 'end_time', {}, ...
        'process_id', {}, 'process_name', {}, 'process_start', {}, 'process_end', {}, ...
        'berth_used', {}, 'loading_start', {}, 'loading_end', {}, 'travel_time', {}, ...
        'travel_start', {}, 'is_delayed', {}, 'position', {});
    
    % 处理每个风机
    for i = 1:turbine_count
        turbine_id = operation_sequence(i);
        vessel_id = vessel_assignment(turbine_id);
        
        % 获取船舶状态
        current_time = vessel_end_times(vessel_id);
        current_capacity = vessel_capacity(vessel_id);
        current_location = vessel_location(vessel_id);
        
        % 为风机分配位置坐标 (简单模型: 径向分布)
        angle = turbine_id * 2 * pi / turbine_count;
        radius = 5 + 15 * rand(); % 5-20km半径
        position = [radius * cos(angle), radius * sin(angle)];
        
        % 检查是否需要装载
        if current_capacity >= max_capacity || current_capacity == 0 || ~vessel_load_complete(vessel_id)
            % 需要回港
            if current_location > 0
                travel_time = port_to_farm_distance / vessel_speeds(vessel_id);
                current_time = current_time + travel_time;
            end
            
            % 等待泊位
            berth_id = 0;
            berth_start_time = current_time;
            
            [min_end_time, earliest_berth_idx] = min(berth_end_times);
            if min_end_time > current_time
                current_time = min_end_time;
            end
            berth_id = earliest_berth_idx;
            berth_start_time = current_time;
            
            % 占用泊位装载
            loading_time = loading_times(vessel_id);
            berth_end_times(berth_id) = current_time + loading_time;
            berth_usage(berth_id) = berth_usage(berth_id) + loading_time;
            
            % 更新船舶状态
            current_time = current_time + loading_time;
            current_capacity = 1;
            current_location = 0;
            vessel_load_complete(vessel_id) = true;
        else
            current_capacity = current_capacity + 1;
        end
        
        % 前往风场
        if current_location == 0
            travel_time = port_to_farm_distance / vessel_speeds(vessel_id);
        else
            travel_time = turbine_distance * abs(current_location - turbine_id) / vessel_speeds(vessel_id);
        end
        travel_start = current_time;
        current_time = current_time + travel_time;
        
        % 创建任务
        task = struct('turbine_id', turbine_id, 'vessel_id', vessel_id, ...
            'start_time', current_time, 'end_time', 0, ...
            'process_id', [], 'process_name', {{}}, 'process_start', [], 'process_end', [], ...
            'berth_used', 0, 'loading_start', 0, 'loading_end', 0, ...
            'travel_time', travel_time, 'travel_start', travel_start, 'is_delayed', false, ...
            'position', position);
        
        % 执行工序
        process_start = zeros(1, 4);
        process_end = zeros(1, 4);
        process_names = {'基础安装', '塔架安装', '叶片安装', '调试'};
        
        for p = 1:4
            process_start(p) = current_time;
            current_time = current_time + process_times(p);
            process_end(p) = current_time;
        end
        
        % 更新任务信息
        task.end_time = current_time;
        task.process_id = 1:4;
        task.process_name = process_names;
        task.process_start = process_start;
        task.process_end = process_end;
        
        % 记录装载信息
        if vessel_load_complete(vessel_id) && current_capacity <= 1
            task.berth_used = berth_id;
            task.loading_start = berth_start_time;
            task.loading_end = berth_start_time + loading_times(vessel_id);
            vessel_load_complete(vessel_id) = false;
        end
        
        % 添加到调度
        schedule = [schedule, task];
        
        % 更新船舶状态
        vessel_end_times(vessel_id) = current_time;
        vessel_capacity(vessel_id) = current_capacity;
        vessel_location(vessel_id) = turbine_id;
    end
end

function selected = tournamentSelection(population, fitness, pop_size)
    % 锦标赛选择
    tournament_size = 3;
    selected = zeros(size(population));
    
    for i = 1:pop_size
        candidates = randi(pop_size, 1, tournament_size);
        [~, idx] = max(fitness(candidates));
        best_candidate = candidates(idx);
        selected(i, :) = population(best_candidate, :);
    end
end

function offspring = crossover(parents, crossover_rate, turbine_count, vessel_count)
    % 交叉操作
    pop_size = size(parents, 1);
    offspring = parents;
    
    for i = 1:2:pop_size-1
        if i+1 <= pop_size && rand() <= crossover_rate
            parent1 = parents(i, :);
            parent2 = parents(i+1, :);
            
            % 分离船舶分配和操作顺序
            p1_vessel = parent1(1:turbine_count);
            p1_sequence = parent1(turbine_count+1:end);
            p2_vessel = parent2(1:turbine_count);
            p2_sequence = parent2(turbine_count+1:end);
            
            % 单点交叉船舶分配
            crossover_point = randi(turbine_count);
            c1_vessel = [p1_vessel(1:crossover_point), p2_vessel(crossover_point+1:end)];
            c2_vessel = [p2_vessel(1:crossover_point), p1_vessel(crossover_point+1:end)];
            
            % 顺序交叉(OX)操作顺序
            if turbine_count > 2
                % 选取交叉区间
                cut1 = randi([1, turbine_count-1]);
                cut2 = randi([cut1+1, turbine_count]);
                
                % 子代1
                segment1 = p1_sequence(cut1:cut2);
                remaining1 = setdiff(p2_sequence, segment1, 'stable');
                c1_sequence = [remaining1(1:cut1-1), segment1, remaining1(cut1:end)];
                
                % 子代2
                segment2 = p2_sequence(cut1:cut2);
                remaining2 = setdiff(p1_sequence, segment2, 'stable');
                c2_sequence = [remaining2(1:cut1-1), segment2, remaining2(cut1:end)];
            else
                c1_sequence = p2_sequence;
                c2_sequence = p1_sequence;
            end
            
            % 合并
            offspring(i, :) = [c1_vessel, c1_sequence];
            offspring(i+1, :) = [c2_vessel, c2_sequence];
        end
    end
end

function mutated = mutate(population, mutation_rate, turbine_count, vessel_count)
    % 变异操作
    [pop_size, ~] = size(population);
    mutated = population;
    
    for i = 1:pop_size
        % 船舶分配变异
        for j = 1:turbine_count
            if rand() <= mutation_rate
                mutated(i, j) = randi(vessel_count);
            end
        end
        
        % 操作顺序变异 - 交换两个位置
        if rand() <= mutation_rate
            pos1 = turbine_count + randi(turbine_count);
            pos2 = turbine_count + randi(turbine_count);
            mutated(i, [pos1, pos2]) = mutated(i, [pos2, pos1]);
        end
    end
end

function [new_population, new_fitness, new_makespan] = elitism(population, fitness, makespan, offspring, offspring_fitness, offspring_makespan)
    % 精英保留策略
    combined_population = [population; offspring];
    combined_fitness = [fitness, offspring_fitness];
    combined_makespan = [makespan, offspring_makespan];
    
    [sorted_fitness, indices] = sort(combined_fitness, 'descend');
    sorted_population = combined_population(indices, :);
    sorted_makespan = combined_makespan(indices);
    
    pop_size = size(population, 1);
    new_population = sorted_population(1:pop_size, :);
    new_fitness = sorted_fitness(1:pop_size);
    new_makespan = sorted_makespan(1:pop_size);
end

function diversity = calculateDiversity(fitness)
    % 计算种群多样性
    if mean(fitness) > 0
        diversity = std(fitness) / mean(fitness);
    else
        diversity = 0;
    end
end

function new_position = updateParticlePosition(position, velocity, turbine_count, vessel_count)
    % 更新粒子位置
    new_position = position;
    
    % 速度大小决定交换次数
    swap_count = max(1, min(5, ceil(sum(abs(velocity)) / 20)));
    
    % 船舶分配部分更新
    for i = 1:turbine_count
        if abs(velocity(i)) > rand()
            new_position(i) = randi(vessel_count);
        end
    end
    
    % 操作顺序部分更新 - 随机交换
    for i = 1:swap_count
        idx1 = turbine_count + randi(turbine_count);
        idx2 = turbine_count + randi(turbine_count);
        new_position([idx1, idx2]) = new_position([idx2, idx1]);
    end
end

function right_shifted = rightShiftReschedule(schedule, failed_vessel, failure_time, repair_time)
    % 右移重调度策略
    right_shifted = schedule;
    affected_tasks = [];
    
    % 找出受影响的任务
    for i = 1:length(right_shifted)
        % 如果是故障船舶的任务且在故障时间后结束
        if right_shifted(i).vessel_id == failed_vessel && right_shifted(i).end_time > failure_time
            affected_tasks = [affected_tasks, i];
            right_shifted(i).is_delayed = true;
            
            % 如果任务已经开始但未完成
            if right_shifted(i).start_time <= failure_time
                % 计算故障发生时已经进行的时间
                elapsed = failure_time - right_shifted(i).start_time;
                
                % 更新任务开始和结束时间
                total_duration = right_shifted(i).end_time - right_shifted(i).start_time;
                remaining_duration = total_duration - elapsed;
                
                right_shifted(i).start_time = failure_time + repair_time;
                right_shifted(i).end_time = right_shifted(i).start_time + remaining_duration;
                
                % 更新工序时间
                if isfield(right_shifted(i), 'process_start') && isfield(right_shifted(i), 'process_end')
                    % 找到当前工序
                    current_process = 1;
                    for p = 1:length(right_shifted(i).process_end)
                        if right_shifted(i).process_end(p) > failure_time
                            current_process = p;
                            break;
                        end
                    end
                    
                    % 更新当前和后续工序时间
                    shift_amount = repair_time + (failure_time - right_shifted(i).process_start(current_process));
                    for p = current_process:4
                        right_shifted(i).process_start(p) = right_shifted(i).process_start(p) + shift_amount;
                        right_shifted(i).process_end(p) = right_shifted(i).process_end(p) + shift_amount;
                    end
                end
            else
                % 整个任务还未开始，全部推迟
                delay = repair_time;
                right_shifted(i).start_time = right_shifted(i).start_time + delay;
                right_shifted(i).end_time = right_shifted(i).end_time + delay;
                
                if isfield(right_shifted(i), 'process_start') && isfield(right_shifted(i), 'process_end')
                    right_shifted(i).process_start = right_shifted(i).process_start + delay;
                    right_shifted(i).process_end = right_shifted(i).process_end + delay;
                end
                
                if isfield(right_shifted(i), 'loading_start') && isfield(right_shifted(i), 'loading_end')
                    right_shifted(i).loading_start = right_shifted(i).loading_start + delay;
                    right_shifted(i).loading_end = right_shifted(i).loading_end + delay;
                end
                
                if isfield(right_shifted(i), 'travel_start')
                    right_shifted(i).travel_start = right_shifted(i).travel_start + delay;
                end
            end
        end
    end
end

function rescheduled = simplifiedCompleteReschedule(schedule, remaining_turbines, failed_vessel, failure_time, repair_time, vessel_count, berth_count, process_times, max_capacity)
    % 简化版完全重调度
    % 保留已完成的任务
    completed = [];
    remaining = [];
    
    % 分离完成和未完成的任务
    for i = 1:length(schedule)
        if schedule(i).end_time <= failure_time
            completed = [completed, schedule(i)];
        end
    end
    
    % 简单地根据船舶可用性重新分配
    % 确定故障后各船舶的可用时间
    vessel_available_time = zeros(1, vessel_count);
    vessel_available_time(failed_vessel) = failure_time + repair_time;
    
    % 对每个剩余风机，分配给最早可用的船舶
    for i = 1:length(remaining_turbines)
        turbine_id = remaining_turbines(i);
        
        % 排除故障船舶并找出最早可用的船舶
        available_vessels = 1:vessel_count;
        if failure_time + repair_time > max(vessel_available_time)
            available_vessels = setdiff(available_vessels, failed_vessel);
        end
        
        [earliest_time, idx] = min(vessel_available_time(available_vessels));
        vessel_id = available_vessels(idx);
        
        % 创建工序时间
        process_start = zeros(1, 4);
        process_end = zeros(1, 4);
        current_time = earliest_time;
        
        for p = 1:4
            process_start(p) = current_time;
            current_time = current_time + process_times(p);
            process_end(p) = current_time;
        end
        
        % 创建任务记录
        task = struct(...
            'turbine_id', turbine_id, ...
            'vessel_id', vessel_id, ...
            'start_time', earliest_time, ...
            'end_time', current_time, ...
            'process_id', 1:4, ...
            'process_name', {{'基础安装', '塔架安装', '叶片安装', '调试'}}, ...
            'process_start', process_start, ...
            'process_end', process_end, ...
            'is_delayed', true);
        
        % 更新此船舶的可用时间
        vessel_available_time(vessel_id) = task.end_time;
        
        % 添加到重调度任务中
        remaining = [remaining, task];
    end
    
    % 合并已完成和重调度的任务
    rescheduled = [completed, remaining];
end

function display_schedule = selectRepresentativeSchedule(schedule, display_count)
    % 选择代表性任务展示
    % 按时间分段选取，确保覆盖整个时间范围
    
    if length(schedule) <= display_count
        display_schedule = schedule;
        return;
    end
    
    % 计算时间范围
    start_times = [schedule.start_time];
    end_times = [schedule.end_time];
    min_time = min(start_times);
    max_time = max(end_times);
    time_range = max_time - min_time;
    
    % 划分时间段
    segment_count = display_count;
    segment_length = time_range / segment_count;
    
    % 从每个时间段中选取代表性任务
    selected_indices = [];
    
    for i = 1:segment_count
        segment_start = min_time + (i-1) * segment_length;
        segment_end = min_time + i * segment_length;
        
        % 找出此时间段的任务
        segment_tasks = [];
        for j = 1:length(schedule)
            if (schedule(j).start_time <= segment_end && schedule(j).end_time >= segment_start)
                segment_tasks = [segment_tasks, j];
            end
        end
        
        % 如果有任务，随机选择一个
        if ~isempty(segment_tasks)
            selected_idx = segment_tasks(randi(length(segment_tasks)));
            selected_indices = [selected_indices, selected_idx];
        end
    end
    
    % 确保选择了足够多的任务
    remaining_count = display_count - length(selected_indices);
    if remaining_count > 0
        % 从未选择的任务中随机选择
        unselected = setdiff(1:length(schedule), selected_indices);
        
        if length(unselected) > remaining_count
            additional = randsample(unselected, remaining_count);
        else
            additional = unselected;
        end
        
        selected_indices = [selected_indices, additional];
    end
    
    % 返回选中的任务
    display_schedule = schedule(selected_indices);
end

function filtered_schedule = filterScheduleAroundFailure(schedule, failure_time, window)
    % 筛选故障前后的任务
    filtered_indices = [];
    
    for i = 1:length(schedule)
        if (schedule(i).start_time >= failure_time - window && ...
            schedule(i).start_time <= failure_time + window) || ...
           (schedule(i).end_time >= failure_time - window && ...
            schedule(i).end_time <= failure_time + window) || ...
           (schedule(i).start_time <= failure_time - window && ...
            schedule(i).end_time >= failure_time + window)
            filtered_indices = [filtered_indices, i];
        end
    end
    
    filtered_schedule = schedule(filtered_indices);
end

function plotEnhancedGantt(schedule, vessel_count, berth_count, vessel_colors, process_names, failure_time, repair_time, failed_vessel)
    % 增强型甘特图 - 详细展示工序和资源关系
    
    if nargin < 6
        failure_time = [];
        repair_time = [];
        failed_vessel = [];
    end
    
    % 为更复杂的视图创建子图布局
    subplot(5, 1, 1:4);  % 主甘特图区域
    
    % 计算最大结束时间
    if ~isempty(schedule)
        all_end_times = [schedule.end_time];
        max_time = max(all_end_times);
        min_time = min([schedule.start_time]);
    else
        max_time = 100;
        min_time = 0;
    end
    
    % 绘制甘特图背景
    hold on;
    
    % 绘制船舶活动区域
    for v = 1:vessel_count
        % 绘制船舶区域背景条
        y_pos = v * 2 - 1;
        rectangle('Position', [min_time, y_pos-0.8, max_time-min_time, 1.6], ...
            'FaceColor', [0.97, 0.97, 0.97], 'EdgeColor', 'none');
        
        % 添加船舶标签
        text(min_time-5, y_pos, sprintf('船舶%d', v), ...
            'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 9);
    end
    
    % 绘制泊位区域
    for b = 1:berth_count
        y_pos = vessel_count * 2 + b;
        rectangle('Position', [min_time, y_pos-0.4, max_time-min_time, 0.8], ...
            'FaceColor', [0.95, 0.95, 0.95], 'EdgeColor', 'none');
        text(min_time-5, y_pos, sprintf('泊位%d', b), ...
            'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 9);
    end
    
    % 绘制天数网格线
    day_interval = 24;
    for day = 0:day_interval:ceil(max_time/day_interval)*day_interval
        line([day, day], [0, vessel_count*2+berth_count+1], 'Color', [0.9, 0.9, 0.9], 'LineStyle', ':');
        text(day, 0.5, sprintf('第%d天', day/24), 'HorizontalAlignment', 'center', 'FontSize', 8);
    end
    
    % 如果有故障信息，绘制故障区域
    if ~isempty(failure_time) && ~isempty(repair_time) && ~isempty(failed_vessel)
        % 绘制故障时间线
        line([failure_time, failure_time], [0, vessel_count*2+berth_count+1], ...
            'Color', 'r', 'LineWidth', 1.5, 'LineStyle', '-');
        text(failure_time, 0.2, '故障', 'Color', 'r', 'FontWeight', 'bold', ...
            'HorizontalAlignment', 'right', 'Rotation', 90, 'FontSize', 9);
        
        % 绘制修复结束线
        repair_end = failure_time + repair_time;
        line([repair_end, repair_end], [0, vessel_count*2+berth_count+1], ...
            'Color', 'g', 'LineWidth', 1.5, 'LineStyle', '--');
        text(repair_end, 0.2, '修复完成', 'Color', 'g', 'FontWeight', 'bold', ...
            'HorizontalAlignment', 'left', 'Rotation', 90, 'FontSize', 9);
        
        % 标记故障船舶区域
        y_pos = failed_vessel * 2 - 1;
        rectangle('Position', [failure_time, y_pos-0.8, repair_time, 1.6], ...
            'FaceColor', [1, 0.8, 0.8], 'EdgeColor', 'r', 'LineStyle', ':', 'FaceAlpha', 0.3);
        text(failure_time + repair_time/2, y_pos, '维修中', ...
            'Color', 'r', 'FontWeight', 'bold', 'FontSize', 10, ...
            'HorizontalAlignment', 'center');
    end
    
    % 绘制各任务
    for i = 1:length(schedule)
        task = schedule(i);
        vessel_id = task.vessel_id;
        y_pos = vessel_id * 2 - 1;
        
        % 获取船舶颜色
        vessel_color = vessel_colors(mod(vessel_id-1, size(vessel_colors, 1))+1, :);
        
        % 绘制主任务框
        if isfield(task, 'is_delayed') && task.is_delayed
            edge_style = '--';
            edge_width = 1.5;
        else
            edge_style = '-';
            edge_width = 1;
        end
        
        % 绘制装载和航行连接线
        if isfield(task, 'berth_used') && task.berth_used > 0
            berth_id = task.berth_used;
            berth_y = vessel_count * 2 + berth_id;
            
            % 绘制泊位占用块
            rectangle('Position', [task.loading_start, berth_y-0.3, task.loading_end-task.loading_start, 0.6], ...
                'FaceColor', vessel_color, 'EdgeColor', 'k');
            text(task.loading_start + (task.loading_end-task.loading_start)/2, berth_y, ...
                sprintf('V%d-T%d', vessel_id, task.turbine_id), ...
                'HorizontalAlignment', 'center', 'FontSize', 7, 'Color', 'w', 'FontWeight', 'bold');
            
            % 绘制从泊位到风场的连接线
            if isfield(task, 'travel_start') && task.travel_start > 0
                % 航行线
                line([task.loading_end, task.travel_start], [berth_y, y_pos], ...
                    'Color', [0.7, 0.7, 0.7], 'LineStyle', ':');
                
                % 航行箭头
                line([task.travel_start, task.start_time], [y_pos, y_pos], ...
                    'Color', [0.6, 0.6, 0.6], 'LineWidth', 1.5);
                text(task.travel_start + (task.start_time-task.travel_start)/2, y_pos-0.3, ...
                    sprintf('航行 %.1fh', task.travel_time), 'FontSize', 7, 'Color', [0.5, 0.5, 0.5]);
                
                % 绘制箭头
                arrow_length = min(5, (task.start_time-task.travel_start)/10);
                if task.start_time - task.travel_start > 5
                    x_arrow = task.start_time - arrow_length;
                    y_arrow = y_pos;
                    dx = arrow_length;
                    dy = 0;
                    plotArrow(x_arrow, y_arrow, dx, dy, [0.6, 0.6, 0.6]);
                end
            end
        end
        
        % 绘制详细工序
        if isfield(task, 'process_start') && isfield(task, 'process_end') && ...
           length(task.process_start) >= 4 && length(task.process_end) >= 4
            % 绘制四种工序，使用不同颜色深浅和边框样式
            for p = 1:4
                % 确保颜色有区分度
                process_color = vessel_color * (1 - (p-1)*0.15);
                
                % 绘制工序块
                rectangle('Position', [task.process_start(p), y_pos-0.4, ...
                    task.process_end(p)-task.process_start(p), 0.8], ...
                    'FaceColor', process_color, 'EdgeColor', 'k', ...
                    'LineStyle', edge_style, 'LineWidth', edge_width);
                
                % 仅对长度足够的工序添加标签
                if task.process_end(p) - task.process_start(p) > 8
                    text(task.process_start(p) + (task.process_end(p)-task.process_start(p))/2, ...
                        y_pos, [process_names{p}], ...
                        'HorizontalAlignment', 'center', 'FontSize', 7, 'FontWeight', 'bold', 'Color', 'w');
                end
            end
            
            % 添加风机ID标签
            text(task.start_time + (task.end_time-task.start_time)/2, y_pos-0.6, ...
                sprintf('T%d', task.turbine_id), ...
                'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
        else
            % 简化绘制，不显示详细工序
            rectangle('Position', [task.start_time, y_pos-0.4, ...
                task.end_time-task.start_time, 0.8], ...
                'FaceColor', vessel_color, 'EdgeColor', 'k', 'LineStyle', edge_style);
            
            text(task.start_time + (task.end_time-task.start_time)/2, y_pos, ...
                sprintf('T%d', task.turbine_id), ...
                'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold', 'Color', 'w');
        end
    end
    
    % 设置坐标轴范围和标签
    xlim([min_time-10, max_time+10]);
    ylim([0, vessel_count*2+berth_count+1]);
    
    % 设置Y轴标签
    set(gca, 'YTick', []);  % 移除Y轴刻度
    
    % 添加X轴标签
    xlabel('时间（小时）');
    
    % 添加标题
    if ~isempty(failure_time)
        title(sprintf('详细甘特图 (故障时间: %.1f小时, 修复时间: %.1f小时)', failure_time, repair_time), 'FontSize', 12);
    else
        title('详细甘特图', 'FontSize', 12);
    end
    
    % 添加图例
    legend_items = cell(1, 4);
    legend_handles = zeros(1, 4);
    
    for p = 1:4
        legend_handles(p) = rectangle('Position', [0, 0, 1, 1], 'FaceColor', [0.3, 0.6, 0.9] * (1 - (p-1)*0.15));
        legend_items{p} = process_names{p};
    end
    
    legend(legend_handles, legend_items, 'Location', 'northoutside', 'Orientation', 'horizontal');
    
    % 绘制泊位使用情况
    subplot(5, 1, 5);
    plotBerthUsage(schedule, berth_count, max_time, failure_time, repair_time);
    
    hold off;
end

function plotBerthUsage(schedule, berth_count, max_time, failure_time, repair_time)
    % 绘制泊位使用情况
    if nargin < 4
        failure_time = [];
        repair_time = [];
    end
    
    hold on;
    
    % 收集泊位使用数据
    berth_data = cell(1, berth_count);
    for i = 1:length(schedule)
        if isfield(schedule(i), 'berth_used') && schedule(i).berth_used > 0 && ...
           isfield(schedule(i), 'loading_start') && isfield(schedule(i), 'loading_end')
            berth_id = schedule(i).berth_used;
            if berth_id <= berth_count
                berth_data{berth_id} = [berth_data{berth_id}; ...
                    schedule(i).loading_start, schedule(i).loading_end, ...
                    schedule(i).vessel_id, schedule(i).turbine_id];
            end
        end
    end
    
    % 为每个泊位计算利用率
    total_time = max_time;
    berth_usage_time = zeros(1, berth_count);
    berth_usage_percent = zeros(1, berth_count);
    
    for b = 1:berth_count
        if ~isempty(berth_data{b})
            berth_usage_time(b) = sum(berth_data{b}(:,2) - berth_data{b}(:,1));
            berth_usage_percent(b) = berth_usage_time(b) / total_time * 100;
        end
    end
    
    % 绘制泊位使用时间轴
    for b = 1:berth_count
        y_pos = b;
        
        % 绘制背景条
        rectangle('Position', [0, y_pos-0.4, max_time, 0.8], ...
            'FaceColor', [0.95, 0.95, 0.95], 'EdgeColor', 'none');
        
        % 绘制使用率标签
        text(max_time+5, y_pos, sprintf('利用率: %.1f%%', berth_usage_percent(b)), ...
            'HorizontalAlignment', 'left', 'FontSize', 8);
        
        % 绘制各使用段
        if ~isempty(berth_data{b})
            for i = 1:size(berth_data{b}, 1)
                start_time = berth_data{b}(i, 1);
                end_time = berth_data{b}(i, 2);
                vessel_id = berth_data{b}(i, 3);
                turbine_id = berth_data{b}(i, 4);
                
                % 根据船舶ID选择颜色
                color_idx = mod(vessel_id-1, 5) + 1;
                colors = [0.3, 0.6, 0.9; 0.9, 0.5, 0.2; 0.2, 0.7, 0.4; 0.8, 0.3, 0.6; 0.5, 0.5, 0.5];
                block_color = colors(color_idx, :);
                
                             % 绘制使用块
                rectangle('Position', [start_time, y_pos-0.3, end_time-start_time, 0.6], ...
                    'FaceColor', block_color, 'EdgeColor', 'k');
                
                % 添加标签
                if end_time - start_time > 5
                    text(start_time + (end_time-start_time)/2, y_pos, ...
                        sprintf('V%d-T%d', vessel_id, turbine_id), ...
                        'HorizontalAlignment', 'center', 'FontSize', 7, ...
                        'Color', 'w', 'FontWeight', 'bold');
                end
            end
        end
    end
    
    % 绘制时间刻度线
    day_interval = 24;
    for day = 0:day_interval:ceil(max_time/day_interval)*day_interval
        line([day, day], [0, berth_count+1], 'Color', [0.9, 0.9, 0.9], 'LineStyle', ':');
    end
    
    % 如果有故障信息，绘制故障区域
    if ~isempty(failure_time) && ~isempty(repair_time)
        % 绘制故障时间线
        line([failure_time, failure_time], [0, berth_count+1], ...
            'Color', 'r', 'LineWidth', 1.5, 'LineStyle', '-');
        
        % 绘制修复结束线
        repair_end = failure_time + repair_time;
        line([repair_end, repair_end], [0, berth_count+1], ...
            'Color', 'g', 'LineWidth', 1.5, 'LineStyle', '--');
        
        % 标记故障区间
        rectangle('Position', [failure_time, 0, repair_time, berth_count+1], ...
            'FaceColor', [1, 0.9, 0.9], 'EdgeColor', 'none', 'FaceAlpha', 0.2);
    end
    
    % 设置坐标轴
    xlim([0, max_time+20]);
    ylim([0.5, berth_count+0.5]);
    set(gca, 'YTick', 1:berth_count, 'YTickLabel', arrayfun(@(x) sprintf('泊位%d', x), 1:berth_count, 'UniformOutput', false));
    xlabel('时间（小时）');
    title('泊位使用情况');
    grid off;
    
    hold off;
end

function plotArrow(x, y, dx, dy, color)
    % 绘制箭头的简化函数
    % 参数：
    % x, y - 起点坐标
    % dx, dy - 箭头方向和长度
    % color - 箭头颜色
    
    if nargin < 5
        color = 'k';
    end
    
    arrow_head_size = min(abs(dx), abs(dy)) * 0.3 + 0.5;
    
    % 计算箭头方向
    if dx ~= 0 || dy ~= 0
        angle = atan2(dy, dx);
    else
        angle = 0;
    end
    
    % 绘制箭头主体
    line([x, x+dx], [y, y+dy], 'Color', color, 'LineWidth', 1.5);
    
    % 绘制箭头头部
    head_angle1 = angle + 2.5;
    head_angle2 = angle - 2.5;
    
    head_x1 = x + dx - arrow_head_size * cos(head_angle1);
    head_y1 = y + dy - arrow_head_size * sin(head_angle1);
    head_x2 = x + dx - arrow_head_size * cos(head_angle2);
    head_y2 = y + dy - arrow_head_size * sin(head_angle2);
    
    line([x+dx, head_x1], [y+dy, head_y1], 'Color', color, 'LineWidth', 1.5);
    line([x+dx, head_x2], [y+dy, head_y2], 'Color', color, 'LineWidth', 1.5);
end

function vessel_stats = analyzeVesselUtilization(schedule, vessel_count)
    % 分析船舶利用率
    
    % 初始化统计数据
    vessel_stats = struct('vessel_id', {}, 'turbine_count', {}, ...
        'total_work_time', {}, 'total_travel_time', {}, 'utilization', {});
    
    % 收集各船舶任务数据
    for v = 1:vessel_count
        % 筛选该船舶的任务
        vessel_tasks = [];
        for i = 1:length(schedule)
            if schedule(i).vessel_id == v
                vessel_tasks = [vessel_tasks, i];
            end
        end
        
        turbine_count = length(vessel_tasks);
        work_time = 0;
        travel_time = 0;
        
        % 计算工作和航行时间
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            work_time = work_time + (schedule(task_idx).end_time - schedule(task_idx).start_time);
            
            if isfield(schedule(task_idx), 'travel_time')
                travel_time = travel_time + schedule(task_idx).travel_time;
            end
        end
        
        % 计算利用率（假设总时间为最后一个任务的结束时间）
        if ~isempty(vessel_tasks)
            end_times = zeros(1, length(vessel_tasks));
            for i = 1:length(vessel_tasks)
                end_times(i) = schedule(vessel_tasks(i)).end_time;
            end
            total_time = max(end_times);
            utilization = (work_time + travel_time) / total_time * 100;
        else
            total_time = 0;
            utilization = 0;
        end
        
        % 添加到统计数据
        vessel_stats(v).vessel_id = v;
        vessel_stats(v).turbine_count = turbine_count;
        vessel_stats(v).total_work_time = work_time;
        vessel_stats(v).total_travel_time = travel_time;
        vessel_stats(v).utilization = utilization;
    end
end

function plotVesselUtilization(vessel_stats)
    % 绘制船舶利用率图表
    
    vessel_count = length(vessel_stats);
    vessel_ids = 1:vessel_count;
    turbine_counts = zeros(1, vessel_count);
    utilizations = zeros(1, vessel_count);
    
    for i = 1:vessel_count
        turbine_counts(i) = vessel_stats(i).turbine_count;
        utilizations(i) = vessel_stats(i).utilization;
    end
    
    % 创建双Y轴图
    yyaxis left;
    bar(vessel_ids, turbine_counts, 0.5, 'FaceColor', [0.3, 0.6, 0.9]);
    ylabel('安装风机数量');
    ylim([0, max(turbine_counts) * 1.2]);
    
    yyaxis right;
    plot(vessel_ids, utilizations, 'ro-', 'LineWidth', 2, 'MarkerFaceColor', 'r');
    ylabel('利用率 (%)');
    ylim([0, 100]);
    
    % 添加数据标签
    for i = 1:vessel_count
        text(i, turbine_counts(i)+1, sprintf('%d台', turbine_counts(i)), ...
            'HorizontalAlignment', 'center', 'FontSize', 8);
        text(i, utilizations(i)+5, sprintf('%.1f%%', utilizations(i)), ...
            'HorizontalAlignment', 'center', 'FontSize', 8, 'Color', 'r');
    end
    
    % 设置图表属性
    xlabel('船舶ID');
    title('船舶工作量与利用率分析');
    set(gca, 'XTick', 1:vessel_count);
    grid on;
    
    % 添加图例
    legend({'风机数量', '利用率'}, 'Location', 'northwest');
end

function [optimality_gap, theoretical_lb] = calculateOptimalityGap(makespan, turbine_count, vessel_count, process_times)
    % 计算优化间隙 (与理论下界的比较)
    
    % 计算理论下界
    total_work = turbine_count * sum(process_times);
    min_makespan_per_vessel = total_work / vessel_count;
    
    % 考虑返回港口的约束
    avg_process_time = sum(process_times);
    
    % 理论下界取两者的最大值
    theoretical_lb = max(min_makespan_per_vessel, avg_process_time);
    
    % 计算优化间隙
    optimality_gap = (makespan - theoretical_lb) / theoretical_lb * 100;
end