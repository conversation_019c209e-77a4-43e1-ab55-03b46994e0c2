%% Hybrid GA-PSO Algorithm for Offshore Wind Turbine Installation Scheduling
% This code implements a hybrid GA-PSO algorithm for scheduling offshore wind
% turbine installation tasks with disruption handling and rescheduling.

clear all;
close all;
clc;
rng(42); % For reproducibility

%% Problem Parameters
TURBINE_COUNT = 40;        % Wind turbine count
VESSEL_COUNT = 10;         % Vessel count
BERTH_COUNT = 2;           % Berth count
PROCESS_TIMES = [20, 12, 10, 36]; % Process times (hours)
MAX_CAPACITY = 4;          % Max vessel capacity (turbines)

% Distance and time parameters
global PORT_TO_FARM_DISTANCE; % Define as global so functions can access it
PORT_TO_FARM_DISTANCE = 80; % Port to farm distance (km)
VESSEL_SPEEDS = ones(1, VESSEL_COUNT) * 15; % Vessel speeds (km/h)
VESSEL_SPEEDS = VESSEL_SPEEDS .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation
TURBINE_DISTANCE = 1;      % Turbine distance (km)
LOADING_TIMES = 6 * ones(1, VESSEL_COUNT); % Base loading time
LOADING_TIMES = LOADING_TIMES .* (0.8 + 0.4*rand(1, VESSEL_COUNT)); % Random variation

% Algorithm parameters
global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
      MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
POP_SIZE = 50;            % Population size
MAX_GEN = 100;            % Maximum generations
PSO_ITERATIONS = 50;      % PSO iterations
CROSSOVER_RATE = 0.8;     % Crossover rate
MUTATION_RATE_GA = 0.01;  % GA mutation rate
MUTATION_RATE_PSO = 0.19;  % PSO mutation rate
INERTIA_WEIGHT = 0.78;     % Inertia weight
C1 = 1.4;                 % Cognitive parameter
C2 = 1.5;                 % Social parameter
DIVERSITY_THRESHOLD = 0.05; % Diversity threshold for PSO intervention

% Disruption parameters
DISRUPTION_TIME = 100;    % Time of disruption (hours) - changed to occur earlier
REPAIR_TIME = 50;         % Repair time (hours)
AFFECTED_VESSEL = 3;      % Affected vessel ID

%% Data Structures
% Define turbine installation tasks
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'processes', cell(1, TURBINE_COUNT));

% Initialize process times for each turbine
for i = 1:TURBINE_COUNT
    % Add slight variations to process times (±10%)
    variation = 0.9 + 0.2*rand(1, length(PROCESS_TIMES));
    turbines(i).processes = PROCESS_TIMES .* variation;
end

% Define vessels
vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                'speed', num2cell(VESSEL_SPEEDS), ...
                'capacity', num2cell(ones(1, VESSEL_COUNT) * MAX_CAPACITY), ...
                'loading_time', num2cell(LOADING_TIMES));

% Define berths
berths = struct('id', num2cell(1:BERTH_COUNT));

%% Hybrid GA-PSO Algorithm for Initial Schedule
fprintf('Generating initial schedule using Hybrid GA-PSO algorithm...\n');
initial_schedule = hybridGAPSO(turbines, vessels, berths);
makespan_initial = calculateMakespan(initial_schedule);
fprintf('Initial schedule makespan: %.2f hours\n', makespan_initial);

%% Generate Disruption
fprintf('Simulating disruption at time %.2f hours for vessel %d...\n', ...
    DISRUPTION_TIME, AFFECTED_VESSEL);
disruption = struct('time', DISRUPTION_TIME, ...
                    'repair_time', REPAIR_TIME, ...
                    'affected_vessel', AFFECTED_VESSEL);

%% Right-Shift Rescheduling
fprintf('Applying right-shift rescheduling...\n');
right_shift_schedule = rightShiftRescheduling(initial_schedule, disruption);
makespan_right_shift = calculateMakespan(right_shift_schedule);
fprintf('Right-shift schedule makespan: %.2f hours\n', makespan_right_shift);

%% Complete Rescheduling
fprintf('Applying complete rescheduling...\n');
complete_schedule = completeRescheduling(initial_schedule, disruption, ...
    turbines, vessels, berths);
makespan_complete = calculateMakespan(complete_schedule);
fprintf('Complete rescheduling makespan: %.2f hours\n', makespan_complete);

%% Plot Results
% Plot three Gantt charts
plotGanttChart(initial_schedule, 'Initial Schedule', turbines, vessels, []);
plotGanttChart(right_shift_schedule, 'Right-Shift Rescheduling', turbines, vessels, disruption);
plotGanttChart(complete_schedule, 'Complete Rescheduling', turbines, vessels, disruption);

% Plot convergence curve if available
if exist('convergence_curve', 'var')
    figure;
    plot(convergence_curve, 'LineWidth', 2);
    xlabel('Generation');
    ylabel('Best Makespan (hours)');
    title('Convergence Curve of Hybrid GA-PSO Algorithm');
    grid on;
end

%% Hybrid GA-PSO Algorithm Function
function schedule = hybridGAPSO(turbines, vessels, berths)
    % Access global variables
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % Define if not in global scope
    if isempty(POP_SIZE), POP_SIZE = 50; end
    if isempty(MAX_GEN), MAX_GEN = 200; end
    if isempty(PSO_ITERATIONS), PSO_ITERATIONS = 50; end
    if isempty(CROSSOVER_RATE), CROSSOVER_RATE = 0.8; end
    if isempty(MUTATION_RATE_GA), MUTATION_RATE_GA = 0.01; end
    if isempty(MUTATION_RATE_PSO), MUTATION_RATE_PSO = 0.2; end
    if isempty(INERTIA_WEIGHT), INERTIA_WEIGHT = 0.8; end
    if isempty(C1), C1 = 1.5; end
    if isempty(C2), C2 = 1.5; end
    if isempty(DIVERSITY_THRESHOLD), DIVERSITY_THRESHOLD = 0.05; end
    
    % Get problem dimensions
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % Initialize population - Each individual is a permutation of turbines
    % and assignment of vessels to turbines
    population = cell(1, POP_SIZE);
    fitness = zeros(1, POP_SIZE);
    
    % Generate initial population
    for i = 1:POP_SIZE
        % Random permutation of turbines
        turbine_order = randperm(turbine_count);
        % Random vessel assignment
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % Initialize PSO parameters
    pbest = population;
    pbest_fitness = Inf(1, POP_SIZE);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, POP_SIZE);
    
    % Initialize velocities
    for i = 1:POP_SIZE
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % Convergence tracking
    convergence_curve = zeros(1, MAX_GEN);
    
    % Main loop
    for gen = 1:MAX_GEN
        % Evaluate population
        for i = 1:POP_SIZE
            % Convert chromosome to schedule
            schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
            % Calculate makespan
            fitness(i) = calculateMakespan(schedule_i);
            
            % Update personal best
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % Update global best
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % Store best fitness for convergence curve
        convergence_curve(gen) = gbest_fitness;
        
        % Display progress
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % Check diversity
        diversity = calculateDiversity(fitness);
        
        % Apply PSO if diversity is below threshold
        if diversity < DIVERSITY_THRESHOLD
            fprintf('Low diversity detected at generation %d. Applying PSO...\n', gen);
            
            % Apply PSO for some iterations
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:POP_SIZE
                    % Update velocity
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % Update position
                    % For turbine order (permutation), we use a special approach
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % Ensure it remains a valid permutation
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % For vessel assignment
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % Ensure it remains valid (between 1 and vessel_count)
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % Apply mutation with higher rate
                    if rand() < MUTATION_RATE_PSO
                        % Swap two positions in turbine order
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % Update population
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % Evaluate new solution
                    schedule_i = decodeChromosome(population{i}, turbines, vessels, berths);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % Update personal best
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % Update global best
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA operations (selection, crossover, mutation)
            new_population = cell(1, POP_SIZE);
            
            % Elitism - keep best solution
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % Selection, crossover and mutation for the rest
            for i = 2:POP_SIZE
                % Tournament selection
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(POP_SIZE, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % Crossover
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % Mutation
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % Check termination criteria
        if gen >= MAX_GEN
            break;
        end
    end
    
    % Convert best solution to schedule
    schedule = decodeChromosome(gbest, turbines, vessels, berths);
    
    % Store convergence curve in global variable for plotting
    assignin('base', 'convergence_curve', convergence_curve);
end

%% Helper Functions
function schedule = decodeChromosome(chromosome, turbines, vessels, berths)
    % Access the global PORT_TO_FARM_DISTANCE
    global PORT_TO_FARM_DISTANCE;
    
    % Decodes chromosome to a feasible schedule
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % Initialize schedule data structure
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % Initialize vessel and berth availability times
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % Initialize schedule
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % Port to farm travel time for each vessel
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        % Extract vessel speed as scalar
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % Process each turbine in order
    for i = 1:turbine_count
        turbine_idx = turbine_order(i);
        vessel_idx = vessel_assignment(i);
        
        % Get process times for this turbine
        process_times = turbines(turbine_idx).processes;
        num_processes = length(process_times);
        
        % Handle berth assignment for loading (process 1)
        % Find earliest available berth
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % Calculate loading start time (max of vessel and berth availability)
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % Update berth availability
        berth_avail_time(berth_idx) = loading_end;
        
        % Add loading operation to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0 for loading
        schedule.berth_id(end+1) = berth_idx;
        
        % Travel to farm
        travel_start = loading_end;
        travel_end = travel_start + port_to_farm_time(vessel_idx);
        
        % Add travel to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1 for travel
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Process installation tasks
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            process_end = process_start + process_times(p);
            
            % Add process to schedule
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0 for no berth
            
            current_time = process_end;
        end
        
        % Travel back to port
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);
        
        % Add travel back to schedule
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2 for travel back
        schedule.berth_id(end+1) = 0;  % 0 for no berth
        
        % Update vessel availability
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function makespan = calculateMakespan(schedule)
    % Calculate makespan (maximum completion time)
    makespan = max(schedule.end_time);
end

function diversity = calculateDiversity(fitness)
    % Calculate population diversity based on fitness variance
    mean_fitness = mean(fitness);
    normalized_std = std(fitness) / mean_fitness;
    diversity = normalized_std;
end

function child = crossover(parent1, parent2, turbine_count)
    % Order Crossover (OX) for turbine_order
    % Select two crossover points
    points = sort(randperm(turbine_count, 2));
    p1 = points(1);
    p2 = points(2);
    
    % Initialize child
    child = struct('turbine_order', zeros(1, turbine_count), ...
                  'vessel_assignment', zeros(1, turbine_count));
    
    % Copy segment from parent1
    child.turbine_order(p1:p2) = parent1.turbine_order(p1:p2);
    
    % Fill remaining positions with elements from parent2
    remaining = setdiff(parent2.turbine_order, parent1.turbine_order(p1:p2));
    
    % Fill positions before p1
    child.turbine_order(1:p1-1) = remaining(1:(p1-1));
    
    % Fill positions after p2
    child.turbine_order(p2+1:end) = remaining(p1:end);
    
    % Uniform crossover for vessel assignment
    mask = rand(1, turbine_count) > 0.5;
    child.vessel_assignment = parent1.vessel_assignment;
    child.vessel_assignment(mask) = parent2.vessel_assignment(mask);
end

function child = mutate(child, vessel_count, turbine_count)
    % Swap mutation for turbine order
    idx = randperm(turbine_count, 2);
    temp = child.turbine_order(idx(1));
    child.turbine_order(idx(1)) = child.turbine_order(idx(2));
    child.turbine_order(idx(2)) = temp;
    
    % Random reassignment for vessel assignment
    idx = randi(turbine_count);
    child.vessel_assignment(idx) = randi(vessel_count);
end

function right_shift_schedule = rightShiftRescheduling(schedule, disruption)
    % 基于受影响工序的右移重调度
    % 根据式(3.22)和式(3.23)实现
    
    % 初始化右移调度表 - 先复制原始调度
    right_shift_schedule = schedule;
    
    affected_vessel = disruption.affected_vessel;
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    
    % 找出受故障船舶影响的所有任务
    % 1. 找出故障发生时正在进行的任务
    ongoing_indices = find(schedule.vessel_id == affected_vessel & ...
                          schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);
    
    % 2. 找出故障船舶后续的任务
    future_indices = find(schedule.vessel_id == affected_vessel & ...
                         schedule.start_time >= disruption_time);
    
    % 3. 找出依赖于故障船舶的泊位竞争任务
    % 首先识别故障船舶使用的泊位
    affected_berths = unique(schedule.berth_id(schedule.vessel_id == affected_vessel & ...
                                              schedule.berth_id > 0));
    
    % 找出其他船舶使用相同泊位的任务，且开始时间晚于故障时间
    berth_conflict_indices = [];
    for b = 1:length(affected_berths)
        berth_indices = find(schedule.berth_id == affected_berths(b) & ...
                             schedule.vessel_id ~= affected_vessel & ...
                             schedule.start_time >= disruption_time);
        berth_conflict_indices = [berth_conflict_indices, berth_indices];
    end
    
    % 添加维修任务到调度计划中
    repair_task_idx = length(right_shift_schedule.turbine_id) + 1;
    right_shift_schedule.turbine_id(repair_task_idx) = 0; % 0表示非特定涡轮机
    right_shift_schedule.vessel_id(repair_task_idx) = affected_vessel;
    right_shift_schedule.start_time(repair_task_idx) = disruption_time;
    right_shift_schedule.end_time(repair_task_idx) = disruption_time + repair_time;
    right_shift_schedule.process_id(repair_task_idx) = -3; % -3 表示维修过程
    right_shift_schedule.berth_id(repair_task_idx) = 0;  % 维修不占用泊位
    
    % 1. 调整正在进行的任务 - 仅延长结束时间
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        remaining_time = schedule.end_time(idx) - disruption_time;
        right_shift_schedule.end_time(idx) = disruption_time + repair_time + remaining_time;
    end
    
    % 2. 调整未开始的任务 - 简单右移
    for i = 1:length(future_indices)
        idx = future_indices(i);
        right_shift_schedule.start_time(idx) = schedule.start_time(idx) + repair_time;
        right_shift_schedule.end_time(idx) = schedule.end_time(idx) + repair_time;
    end
    
    % 3. 调整泊位竞争任务 - 简单右移
    for i = 1:length(berth_conflict_indices)
        idx = berth_conflict_indices(i);
        right_shift_schedule.start_time(idx) = schedule.start_time(idx) + repair_time;
        right_shift_schedule.end_time(idx) = schedule.end_time(idx) + repair_time;
    end
    
    % 4. 解决冲突 - 使用更简单、更直接的方法
    % 我们只需要确保同一船舶的任务不重叠，并保持工序顺序
    % 不再基于任务ID决定优先级，而是基于原始调度中的顺序
    
    % 为了方便查找，创建一个映射表记录每个任务在原始调度中的索引
    original_idx_map = containers.Map('KeyType', 'double', 'ValueType', 'double');
    for i = 1:length(schedule.turbine_id)
        key = i;  % 原始索引作为键
        original_idx_map(key) = i;  % 值也是原始索引
    end
    
    % 特殊处理：为新添加的维修任务分配一个合适的原始索引
    original_idx_map(repair_task_idx) = -1;  % 给维修任务一个特殊的低优先级值
    
    % 然后检查并解决冲突
    adjusted = true;
    while adjusted
        adjusted = false;
        
        % 检查每个船舶
        for v = 1:length(unique(right_shift_schedule.vessel_id))
            vessel_tasks = find(right_shift_schedule.vessel_id == v);
            if isempty(vessel_tasks)
                continue;
            end
            
            % 按开始时间排序
            [sorted_times, sorted_idx] = sort(right_shift_schedule.start_time(vessel_tasks));
            tasks_by_time = vessel_tasks(sorted_idx);
            
            % 检查相邻任务是否重叠
            for i = 1:length(tasks_by_time)-1
                current_task = tasks_by_time(i);
                next_task = tasks_by_time(i+1);
                
                % 检查是否重叠
                if right_shift_schedule.end_time(current_task) > right_shift_schedule.start_time(next_task)
                    % 特殊情况：如果当前任务是维修，不移动它
                    if right_shift_schedule.process_id(current_task) == -3
                        % 将下一个任务推迟
                        shift = right_shift_schedule.end_time(current_task) - right_shift_schedule.start_time(next_task);
                        right_shift_schedule.start_time(next_task) = right_shift_schedule.start_time(next_task) + shift;
                        right_shift_schedule.end_time(next_task) = right_shift_schedule.end_time(next_task) + shift;
                        adjusted = true;
                    else
                        % 正常情况：保持时间顺序
                        duration = right_shift_schedule.end_time(next_task) - right_shift_schedule.start_time(next_task);
                        right_shift_schedule.start_time(next_task) = right_shift_schedule.end_time(current_task);
                        right_shift_schedule.end_time(next_task) = right_shift_schedule.start_time(next_task) + duration;
                        adjusted = true;
                    end
                end
            end
        end
        
        % 检查泊位冲突
        for b = 1:max(right_shift_schedule.berth_id)
            berth_tasks = find(right_shift_schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;
            end
            
            % 按开始时间排序
            [sorted_times, sorted_idx] = sort(right_shift_schedule.start_time(berth_tasks));
            tasks_by_time = berth_tasks(sorted_idx);
            
            % 检查相邻任务是否重叠
            for i = 1:length(tasks_by_time)-1
                current_task = tasks_by_time(i);
                next_task = tasks_by_time(i+1);
                
                % 检查是否重叠
                if right_shift_schedule.end_time(current_task) > right_shift_schedule.start_time(next_task)
                    duration = right_shift_schedule.end_time(next_task) - right_shift_schedule.start_time(next_task);
                    right_shift_schedule.start_time(next_task) = right_shift_schedule.end_time(current_task);
                    right_shift_schedule.end_time(next_task) = right_shift_schedule.start_time(next_task) + duration;
                    adjusted = true;
                end
            end
        end
        
        % 检查工序前后关系
        for t = 1:max(right_shift_schedule.turbine_id)
            turbine_tasks = find(right_shift_schedule.turbine_id == t);
            if isempty(turbine_tasks)
                continue;
            end
            
            for i = turbine_tasks
                process_i = right_shift_schedule.process_id(i);
                for j = turbine_tasks
                    if i ~= j
                        process_j = right_shift_schedule.process_id(j);
                        % 如果工序j应该在工序i之前
                        if process_j < process_i && right_shift_schedule.start_time(i) < right_shift_schedule.end_time(j)
                            duration = right_shift_schedule.end_time(i) - right_shift_schedule.start_time(i);
                            right_shift_schedule.start_time(i) = right_shift_schedule.end_time(j);
                            right_shift_schedule.end_time(i) = right_shift_schedule.start_time(i) + duration;
                            adjusted = true;
                        end
                    end
                end
            end
        end
    end
end

function complete_schedule = completeRescheduling(schedule, disruption, turbines, vessels, berths)
    % 基于未施工工序的完全重调度
    % 将未开始的工序重新调度为新的优化问题
    
    % 获取故障时间点和修复时间
    disruption_time = disruption.time;
    repair_time = disruption.repair_time;
    affected_vessel = disruption.affected_vessel;
    
    % 1. 识别已完成的任务（结束时间 < 故障时间）
    completed_indices = find(schedule.end_time <= disruption_time);
    
    % 2. 识别故障时正在进行的任务
    ongoing_indices = find(schedule.start_time < disruption_time & ...
                          schedule.end_time > disruption_time);
    
    % 3. 识别还未开始的任务
    future_indices = find(schedule.start_time >= disruption_time);
    
    % 从原始调度计划复制已完成的任务
    complete_schedule = struct('turbine_id', schedule.turbine_id(completed_indices), ...
                              'vessel_id', schedule.vessel_id(completed_indices), ...
                              'start_time', schedule.start_time(completed_indices), ...
                              'end_time', schedule.end_time(completed_indices), ...
                              'process_id', schedule.process_id(completed_indices), ...
                              'berth_id', schedule.berth_id(completed_indices));
    
    % 添加维修任务到调度计划中
    repair_task_idx = length(complete_schedule.turbine_id) + 1;
    complete_schedule.turbine_id(repair_task_idx) = 0; % 0表示非特定涡轮机
    complete_schedule.vessel_id(repair_task_idx) = affected_vessel;
    complete_schedule.start_time(repair_task_idx) = disruption_time;
    complete_schedule.end_time(repair_task_idx) = disruption_time + repair_time;
    complete_schedule.process_id(repair_task_idx) = -3; % -3 表示维修过程
    complete_schedule.berth_id(repair_task_idx) = 0;  % 维修不占用泊位
    
    % 处理正在进行的任务 - 调整其结束时间
    for i = 1:length(ongoing_indices)
        idx = ongoing_indices(i);
        remaining_time = schedule.end_time(idx) - disruption_time;
        
        new_end_time = disruption_time;
        if schedule.vessel_id(idx) == affected_vessel
            % 如果是故障船舶的任务，需要加上修复时间
            new_end_time = new_end_time + repair_time;
        end
        new_end_time = new_end_time + remaining_time;
        
        % 添加到新的调度计划
        complete_schedule.turbine_id(end+1) = schedule.turbine_id(idx);
        complete_schedule.vessel_id(end+1) = schedule.vessel_id(idx);
        complete_schedule.start_time(end+1) = schedule.start_time(idx);
        complete_schedule.end_time(end+1) = new_end_time;
        complete_schedule.process_id(end+1) = schedule.process_id(idx);
        complete_schedule.berth_id(end+1) = schedule.berth_id(idx);
    end
    
    % 提取未完成任务相关的涡轮机
    remaining_turbines = [];
    
    % 创建已完成任务的集合，用于快速查找
    completed_tasks = zeros(length(turbines), length(turbines(1).processes) + 3); % +3 for loading, travel to/from
    
    % 标记已完成和正在进行的任务
    % 修复: 确保将两个索引向量转换为列向量后再连接
    all_indices = [completed_indices(:); ongoing_indices(:)];
    
    for i = 1:length(all_indices)
        idx = all_indices(i);
        t_id = schedule.turbine_id(idx);
        
        % 跳过维修任务（turbine_id为0）
        if t_id == 0
            continue;
        end
        
        p_id = schedule.process_id(idx);
        
        % 将process_id映射到正确的索引
        if p_id == 0       % 装载
            p_idx = 1;
        elseif p_id == -1  % 前往风场
            p_idx = 2;
        elseif p_id == -2  % 返回港口
            p_idx = length(turbines(1).processes) + 3;
        else               % 正常工序
            p_idx = p_id + 2;
        end
        
        completed_tasks(t_id, p_idx) = 1;
    end
    
    % 提取未完成任务相关的涡轮机
    for t = 1:length(turbines)
        for p = 1:(length(turbines(1).processes) + 3)
            if completed_tasks(t, p) == 0
                if ~ismember(t, remaining_turbines)
                    remaining_turbines = [remaining_turbines, t];
                end
                break;
            end
        end
    end
    
    % 修复：正确创建simplified_turbines结构体数组
    simplified_turbines = struct('id', {}, 'processes', {});
    
    % 现在可以安全地添加元素
    for i = 1:length(remaining_turbines)
        t_id = remaining_turbines(i);
        % 使用结构体字段复制而不是直接赋值
       new_turbine = struct('id', turbines(t_id).id, 'processes', turbines(t_id).processes);
simplified_turbines(end+1) = new_turbine;
    end
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, length(vessels));
    berth_avail_time = zeros(1, length(berths));
    
    % 更新船舶和泊位可用时间
    for i = 1:length(complete_schedule.end_time)
        v_id = complete_schedule.vessel_id(i);
        vessel_avail_time(v_id) = max(vessel_avail_time(v_id), complete_schedule.end_time(i));
        
        b_id = complete_schedule.berth_id(i);
        if b_id > 0
            berth_avail_time(b_id) = max(berth_avail_time(b_id), complete_schedule.end_time(i));
        end
    end
    
    % 特别处理故障船舶的可用时间
    vessel_avail_time(affected_vessel) = max(vessel_avail_time(affected_vessel), disruption_time + repair_time);
    
    % 如果还有未完成的任务，调用hybridGAPSO进行优化
    if ~isempty(simplified_turbines)
        % 创建新的优化问题，并设置起始时间为disruption_time
        remaining_schedule = generateRemainingSchedule(simplified_turbines, vessels, berths, ...
                                                     vessel_avail_time, disruption_time);
        
        % 合并已完成/正在进行的任务和新优化的任务
        complete_schedule.turbine_id = [complete_schedule.turbine_id, remaining_schedule.turbine_id];
        complete_schedule.vessel_id = [complete_schedule.vessel_id, remaining_schedule.vessel_id];
        complete_schedule.start_time = [complete_schedule.start_time, remaining_schedule.start_time];
        complete_schedule.end_time = [complete_schedule.end_time, remaining_schedule.end_time];
        complete_schedule.process_id = [complete_schedule.process_id, remaining_schedule.process_id];
        complete_schedule.berth_id = [complete_schedule.berth_id, remaining_schedule.berth_id];
    end
end
function remaining_schedule = generateRemainingSchedule(turbines, vessels, berths, vessel_avail_time, start_time)
    % 使用修改后的hybridGAPSO为剩余任务生成调度计划
    
    % 获取问题规模
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    
    % 获取全局参数
    global POP_SIZE MAX_GEN PSO_ITERATIONS CROSSOVER_RATE MUTATION_RATE_GA...
           MUTATION_RATE_PSO INERTIA_WEIGHT C1 C2 DIVERSITY_THRESHOLD;
    
    % 定义较小的种群规模和迭代次数，加快重调度速度
    local_pop_size = max(20, min(POP_SIZE, 30));
    local_max_gen = max(50, min(MAX_GEN, 80));
    
    % 初始化种群
    population = cell(1, local_pop_size);
    fitness = zeros(1, local_pop_size);
    
    % 生成初始种群
    for i = 1:local_pop_size
        % 涡轮机安装顺序随机排列
        turbine_order = randperm(turbine_count);
        % 船舶分配随机选择
        vessel_assignment = randi(vessel_count, 1, turbine_count);
        population{i} = struct('turbine_order', turbine_order, ...
                              'vessel_assignment', vessel_assignment);
    end
    
    % 初始化PSO参数
    pbest = population;
    pbest_fitness = Inf(1, local_pop_size);
    gbest = [];
    gbest_fitness = Inf;
    velocity = cell(1, local_pop_size);
    
    % 初始化速度
    for i = 1:local_pop_size
        velocity{i} = struct('turbine_order', zeros(1, turbine_count), ...
                            'vessel_assignment', zeros(1, turbine_count));
    end
    
    % 主循环
    for gen = 1:local_max_gen
        % 评估种群
        for i = 1:local_pop_size
            % 将染色体解码为调度计划，考虑船舶初始可用时间
            schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                turbines, vessels, berths, vessel_avail_time, start_time);
            % 计算工期
            fitness(i) = calculateMakespan(schedule_i);
            
            % 更新个体最优
            if fitness(i) < pbest_fitness(i)
                pbest{i} = population{i};
                pbest_fitness(i) = fitness(i);
            end
        end
        
        % 更新全局最优
        [min_fitness, min_idx] = min(fitness);
        if min_fitness < gbest_fitness
            gbest = population{min_idx};
            gbest_fitness = min_fitness;
        end
        
        % 监控收敛性
        if mod(gen, 10) == 0
            fprintf('重调度Generation %d: Best makespan = %.2f hours\n', gen, gbest_fitness);
        end
        
        % 计算种群多样性
        diversity = calculateDiversity(fitness);
        
        % 如果多样性低于阈值，应用PSO
        if diversity < DIVERSITY_THRESHOLD
            fprintf('重调度过程中检测到低多样性，在第%d代应用PSO...\n', gen);
            
            % 应用PSO进行局部搜索
            for pso_iter = 1:PSO_ITERATIONS
                for i = 1:local_pop_size
                    % 更新速度
                    velocity{i}.turbine_order = INERTIA_WEIGHT * velocity{i}.turbine_order + ...
                        C1 * rand() * (pbest{i}.turbine_order - population{i}.turbine_order) + ...
                        C2 * rand() * (gbest.turbine_order - population{i}.turbine_order);
                    
                    velocity{i}.vessel_assignment = INERTIA_WEIGHT * velocity{i}.vessel_assignment + ...
                        C1 * rand() * (pbest{i}.vessel_assignment - population{i}.vessel_assignment) + ...
                        C2 * rand() * (gbest.vessel_assignment - population{i}.vessel_assignment);
                    
                    % 更新位置
                    % 对于涡轮机顺序（排列），使用特殊方法
                    new_turbine_order = population{i}.turbine_order + round(velocity{i}.turbine_order);
                    % 确保它仍然是有效的排列
                    [~, new_turbine_order] = sort(new_turbine_order);
                    
                    % 对于船舶分配
                    new_vessel_assignment = population{i}.vessel_assignment + round(velocity{i}.vessel_assignment);
                    % 确保有效（在1和vessel_count之间）
                    new_vessel_assignment = max(1, min(vessel_count, new_vessel_assignment));
                    
                    % 以较高概率应用变异
                    if rand() < MUTATION_RATE_PSO
                        % 在涡轮机顺序中交换两个位置
                        idx = randperm(turbine_count, 2);
                        temp = new_turbine_order(idx(1));
                        new_turbine_order(idx(1)) = new_turbine_order(idx(2));
                        new_turbine_order(idx(2)) = temp;
                    end
                    
                    % 更新种群
                    population{i}.turbine_order = new_turbine_order;
                    population{i}.vessel_assignment = new_vessel_assignment;
                    
                    % 评估新解
                    schedule_i = decodeChromosomeWithStartTime(population{i}, ...
                        turbines, vessels, berths, vessel_avail_time, start_time);
                    fitness(i) = calculateMakespan(schedule_i);
                    
                    % 更新个体最优
                    if fitness(i) < pbest_fitness(i)
                        pbest{i} = population{i};
                        pbest_fitness(i) = fitness(i);
                    end
                end
                
                % 更新全局最优
                [min_fitness, min_idx] = min(fitness);
                if min_fitness < gbest_fitness
                    gbest = population{min_idx};
                    gbest_fitness = min_fitness;
                end
            end
        else
            % GA操作（选择、交叉、变异）
            new_population = cell(1, local_pop_size);
            
            % 精英选择 - 保留最佳解
            [~, elite_idx] = min(fitness);
            new_population{1} = population{elite_idx};
            
            % 对剩余个体进行选择、交叉和变异
            for i = 2:local_pop_size
                % 锦标赛选择
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent1 = population{tour_idx(1)};
                else
                    parent1 = population{tour_idx(2)};
                end
                
                tour_idx = randperm(local_pop_size, 2);
                if fitness(tour_idx(1)) < fitness(tour_idx(2))
                    parent2 = population{tour_idx(1)};
                else
                    parent2 = population{tour_idx(2)};
                end
                
                % 交叉
                if rand() < CROSSOVER_RATE
                    child = crossover(parent1, parent2, turbine_count);
                else
                    child = parent1;
                end
                
                % 变异
                if rand() < MUTATION_RATE_GA
                    child = mutate(child, vessel_count, turbine_count);
                end
                
                new_population{i} = child;
            end
            
            population = new_population;
        end
        
        % 检查终止条件
        if gen >= local_max_gen
            break;
        end
    end
    
    % 将最佳解转换为调度计划
    remaining_schedule = decodeChromosomeWithStartTime(gbest, ...
        turbines, vessels, berths, vessel_avail_time, start_time);
end

function schedule = decodeChromosomeWithStartTime(chromosome, turbines, vessels, berths, vessel_avail_time, start_time)
    % 与decodeChromosome类似，但考虑初始可用时间
    global PORT_TO_FARM_DISTANCE;
    
    turbine_order = chromosome.turbine_order;
    vessel_assignment = chromosome.vessel_assignment;
    
    % 初始化调度数据结构
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化泊位可用时间
    berth_avail_time = ones(1, berth_count) * start_time;
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', []);
    
    % 港口到风场的航行时间
    port_to_farm_time = zeros(1, vessel_count);
    for v = 1:vessel_count
        vessel_speed = vessels(v).speed;
        port_to_farm_time(v) = PORT_TO_FARM_DISTANCE / vessel_speed;
    end
    
    % 处理每个涡轮机
    for i = 1:turbine_count
        turbine_idx = turbines(i).id;  % 使用正确的涡轮机ID
        vessel_idx = vessel_assignment(i);
        
        % 获取此涡轮机的处理时间
        process_times = turbines(i).processes;  % 使用turbines(i)而不是turbine_idx
        num_processes = length(process_times);
        
        % 处理泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 计算装载开始时间
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % 更新泊位可用性
        berth_avail_time(berth_idx) = loading_end;
        
        % 添加装载操作到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = loading_start;
        schedule.end_time(end+1) = loading_end;
        schedule.process_id(end+1) = 0; % 0表示装载
        schedule.berth_id(end+1) = berth_idx;
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + port_to_farm_time(vessel_idx);
        
        % 添加航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_start;
        schedule.end_time(end+1) = travel_end;
        schedule.process_id(end+1) = -1; % -1表示前往风场
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        
        % 处理安装任务
        current_time = travel_end;
        for p = 1:num_processes
            process_start = current_time;
            process_end = process_start + process_times(p);
            
            % 添加流程到调度
            schedule.turbine_id(end+1) = turbine_idx;
            schedule.vessel_id(end+1) = vessel_idx;
            schedule.start_time(end+1) = process_start;
            schedule.end_time(end+1) = process_end;
            schedule.process_id(end+1) = p;
            schedule.berth_id(end+1) = 0;  % 0表示无泊位
            
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + port_to_farm_time(vessel_idx);
        
        % 添加返回航行到调度
        schedule.turbine_id(end+1) = turbine_idx;
        schedule.vessel_id(end+1) = vessel_idx;
        schedule.start_time(end+1) = travel_back_start;
        schedule.end_time(end+1) = travel_back_end;
        schedule.process_id(end+1) = -2; % -2表示返回港口
        schedule.berth_id(end+1) = 0;  % 0表示无泊位
        
        % 更新船舶可用性
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

function plotGanttChart(schedule, title_text, turbines, vessels, disruption)
    % Create a professional-looking Gantt chart
    figure('Position', [100, 100, 1200, 800], 'Color', 'white');
    
    % Check if the schedule is empty
    if isempty(schedule) || isempty(schedule.turbine_id)
        text(0.5, 0.5, '没有调度数据可显示', 'HorizontalAlignment', 'center', 'FontSize', 14);
        title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
        return;
    end
    
    % Use a softer color palette
    process_colors = [
        0.9290, 0.6940, 0.3250;  % Loading (process 0) - soft orange
        0.4660, 0.7740, 0.8880;  % Travel to farm (process -1) - soft blue
        0.6350, 0.5040, 0.7410;  % Process 1 - soft purple
        0.4660, 0.7410, 0.3880;  % Process 2 - soft green
        0.8500, 0.3250, 0.3980;  % Process 3 - soft red
        0.9290, 0.6940, 0.1250;  % Process 4 - soft yellow
        0.4660, 0.7740, 0.8880;  % Return to port (process -2) - soft blue
        1.0000, 0.3000, 0.3000;  % Repair (process -3) - noticeable but not too bright red
    ];
    
    % Process names for the legend
    process_names = {'装载', '前往风场', '工序 1', '工序 2', '工序 3', '工序 4', '返回港口', '维修'};
    
    % Create dummy objects for the legend
    dummy_handles = zeros(1, length(process_names));
    for i = 1:length(process_names)
        dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        hold on;
    end
    
    % Draw vessel timelines
    hold on;
    
    % If there is a disruption, add a disruption marker line
    if ~isempty(disruption) && isstruct(disruption) && isfield(disruption, 'time')
        % Draw a vertical line at the time of disruption
        line([disruption.time, disruption.time], [0, length(vessels)+3], ...
            'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);
        
        % Add disruption marker text
        text(disruption.time, length(vessels)+2.5, '故障发生', ...
            'HorizontalAlignment', 'center', 'Color', 'r', ...
            'FontWeight', 'bold', 'VerticalAlignment', 'bottom');
    end
    
    % Pre-identify repair tasks
    repair_indices = [];
    affected_vessel = -1;
    
    if ~isempty(disruption) && isstruct(disruption)
        % Try to find repair tasks based on turbine_id = 0 (no specific turbine)
        repair_by_turbine = find(schedule.turbine_id == 0);
        
        % Try to find repair tasks based on process_id = -3
        repair_by_process = find(schedule.process_id == -3);
        
        % Combine the results, prioritizing process_id = -3
        if ~isempty(repair_by_process)
            repair_indices = repair_by_process;
        elseif ~isempty(repair_by_turbine)
            repair_indices = repair_by_turbine;
        end
        
        % If we have a disruption structure with affected_vessel field
        if isfield(disruption, 'affected_vessel') && ~isempty(disruption.affected_vessel)
            affected_vessel = disruption.affected_vessel;
            
            % If we still don't have repair tasks, try to find them by vessel and time
            if isempty(repair_indices) && affected_vessel > 0
                % Find tasks for the affected vessel near the disruption time
                potential_repair = find(schedule.vessel_id == affected_vessel & ...
                                       abs(schedule.start_time - disruption.time) < 5);
                if ~isempty(potential_repair)
                    repair_indices = [repair_indices, potential_repair];
                    repair_indices = unique(repair_indices); % Remove duplicates
                end
            end
        end
    end
    
    % Process tasks by vessel
    for v = 1:length(vessels)
        vessel_tasks = find(schedule.vessel_id == v);
        if isempty(vessel_tasks)
            continue;  % Skip if no tasks for this vessel
        end
        
        y_pos = v;
        
        % Sort tasks by start time to ensure proper sequence
        [~, sorted_idx] = sort(schedule.start_time(vessel_tasks));
        vessel_tasks = vessel_tasks(sorted_idx);
        
        for i = 1:length(vessel_tasks)
            task_idx = vessel_tasks(i);
            start_time = schedule.start_time(task_idx);
            end_time = schedule.end_time(task_idx);
            duration = end_time - start_time;
            
            % Skip zero-duration tasks
            if duration <= 0
                continue;
            end
            
            % Determine if this is a repair task using multiple criteria
            is_repair = false;
            
            % Check if this task index is in our pre-identified repair indices
            if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                is_repair = true;
            end
            
            % Check if process_id indicates repair
            if schedule.process_id(task_idx) == -3
                is_repair = true;
            end
            
            % Check if this is a zero turbine_id task for affected vessel
            if schedule.turbine_id(task_idx) == 0 && v == affected_vessel
                is_repair = true;
            end
            
            % Check if this task starts at or very near the disruption time on the affected vessel
            if ~isempty(disruption) && isfield(disruption, 'time') && ...
               v == affected_vessel && abs(start_time - disruption.time) < 2
                is_repair = true;
            end
            
            % Set color and label based on task type
            if is_repair
                % Force this task to be marked as repair
                color_idx = 8;  % Repair color
                label_text = '维修';
            else
                % Determine regular process type
                process_id = schedule.process_id(task_idx);
                
                % Map process_id to color index
                if process_id == 0      % Loading
                    color_idx = 1;
                    process_label = 'L';
                elseif process_id == -1  % Travel to farm
                    color_idx = 2;
                    process_label = 'TF';
                elseif process_id == -2  % Return to port
                    color_idx = 7;
                    process_label = 'TB';
                elseif process_id > 0   % Regular process
                    color_idx = min(process_id + 2, size(process_colors, 1)); % Prevent index out of bounds
                    process_label = ['P', num2str(process_id)];
                else                    % Handle unexpected cases
                    color_idx = 1;      % Default to loading color
                    process_label = '?';
                end
                
                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-%s', schedule.turbine_id(task_idx), process_label);
                else
                    % If turbine_id is 0 but not repair, just show process label
                    label_text = process_label;
                end
            end
            
            % Draw task bar
            rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                'FaceColor', process_colors(color_idx,:), ...
                'EdgeColor', 'k', 'LineWidth', 0.5);
            
            % Adjust font size based on duration
            if duration > 30
                font_size = 8;
            elseif duration > 15
                font_size = 7;
            else
                font_size = 6;
            end
            
            % For very short durations, use rotated or external labels
            if duration < 5
                % External label with line
                text(start_time + duration/2, y_pos + 0.5, label_text, ...
                    'HorizontalAlignment', 'left', ...
                    'VerticalAlignment', 'bottom', ...
                    'FontSize', font_size, ...
                    'Rotation', 45);
                % Draw small line connecting to block
                line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
            else
                % Regular internal label
                text(start_time + duration/2, y_pos, label_text, ...
                    'HorizontalAlignment', 'center', ...
                    'VerticalAlignment', 'middle', ...
                    'FontSize', font_size);
            end
        end
        
        % Add vessel label on y-axis
        % If this is the affected vessel, add marker
        if v == affected_vessel
            text(-20, y_pos, sprintf('船舶 %d (故障)', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'Color', 'r');
        else
            text(-20, y_pos, sprintf('船舶 %d', v), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Add berth timelines - ensure no repair tasks are displayed
    berth_count = max(max(schedule.berth_id), 0);
    if berth_count > 0
        y_start = length(vessels) + 1;
        for b = 1:berth_count
            berth_tasks = find(schedule.berth_id == b);
            if isempty(berth_tasks)
                continue;  % Skip if no tasks for this berth
            end
            
            y_pos = y_start + b;
            
            % Sort by start time
            [~, sorted_idx] = sort(schedule.start_time(berth_tasks));
            berth_tasks = berth_tasks(sorted_idx);
            
            for i = 1:length(berth_tasks)
                task_idx = berth_tasks(i);
                
                % Skip repair tasks in berth display
                if ~isempty(repair_indices) && ismember(task_idx, repair_indices)
                    continue;
                end
                
                if schedule.process_id(task_idx) == -3
                    continue;
                end
                
                if schedule.turbine_id(task_idx) == 0 && ...
                   (schedule.vessel_id(task_idx) == affected_vessel || ...
                    (~isempty(disruption) && isfield(disruption, 'time') && ...
                    abs(schedule.start_time(task_idx) - disruption.time) < 2))
                    continue;
                end
                
                start_time = schedule.start_time(task_idx);
                end_time = schedule.end_time(task_idx);
                duration = end_time - start_time;
                
                % Skip zero-duration tasks
                if duration <= 0
                    continue;
                end
                
                % Draw task bar (loading is always process_id 0)
                rectangle('Position', [start_time, y_pos-0.4, duration, 0.8], ...
                    'FaceColor', process_colors(1,:), ...
                    'EdgeColor', 'k', 'LineWidth', 0.5);
                
                % Adjust font size
                if duration > 30
                    font_size = 8;
                elseif duration > 15
                    font_size = 7;
                else
                    font_size = 6;
                end
                
                % Create label
                if schedule.turbine_id(task_idx) > 0
                    label_text = sprintf('T%d-L', schedule.turbine_id(task_idx));
                else
                    label_text = 'L';
                end
                
                if duration < 5
                    % External label for small durations
                    text(start_time + duration/2, y_pos + 0.5, label_text, ...
                        'HorizontalAlignment', 'left', ...
                        'VerticalAlignment', 'bottom', ...
                        'FontSize', font_size, ...
                        'Rotation', 45);
                    line([start_time + duration/2, start_time + duration/2], [y_pos, y_pos + 0.4], 'Color', 'k', 'LineStyle', ':');
                else
                    % Regular internal label
                    text(start_time + duration/2, y_pos, label_text, ...
                        'HorizontalAlignment', 'center', ...
                        'VerticalAlignment', 'middle', ...
                        'FontSize', font_size);
                end
            end
            
            % Add berth label on y-axis
            text(-20, y_pos, sprintf('泊位 %d', b), ...
                'HorizontalAlignment', 'right', 'FontWeight', 'bold');
        end
    end
    
    % Add legend with dummy handles
    legend(dummy_handles, process_names, 'Location', 'southoutside', 'Orientation', 'horizontal');
    
    % Set plot properties
    grid on;
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    % No y-axis label needed as we have custom labels
    
    % Calculate and display makespan
    makespan = max(schedule.end_time);
    text(20, length(vessels) + berth_count + 2, ...
        sprintf('工期: %.2f 小时', makespan), ...
        'FontSize', 12, 'FontWeight', 'bold');
    
    % Set axis limits - corrected part to ensure display of critical time range
    % Find critical time points
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Show tasks before and after disruption
        start_view = max(0, disruption.time - 100); % Show 100 hours before disruption
        end_view = makespan * 1.05;
        xlim([start_view, end_view]);
    else
        % For initial schedule, maintain original view
        xlim([0, makespan * 1.05]);
    end
    
    % Ensure reasonable y-axis range
    ylim([0, length(vessels) + max(berth_count, 1) + 3]);
    
    % Add clearer time grid lines
    set(gca, 'XGrid', 'on', 'XMinorGrid', 'on');
    if ~isempty(disruption) && isfield(disruption, 'time')
        % Add grid lines near disruption time
        set(gca, 'XTick', (floor(disruption.time/20)-5)*20:20:ceil(makespan/20)*20);
    else
        set(gca, 'XTick', 0:20:ceil(makespan/20)*20);  % Major grid line every 20 hours
    end
    
    set(gca, 'YTick', []);  % Remove y-tick labels as we have custom labels
    
    hold off;
end