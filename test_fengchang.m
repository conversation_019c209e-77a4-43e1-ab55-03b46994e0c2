%% 简化测试版本 - 验证风机类型显示
clear all; close all; clc;

% 创建简单的测试数据
turbines = struct('id', {1, 2, 3, 4}, ...
                 'type', {1, 2, 1, 2}, ...
                 'power', {3.5, 8.0, 3.5, 8.0});

vessels = struct('id', {1, 2}, ...
                'type', {1, 2});

% 创建简单的调度数据
schedule = struct();
schedule.turbine_id = [1, 2, 3, 4];
schedule.vessel_id = [1, 2, 1, 2];
schedule.start_time = [0, 10, 20, 30];
schedule.end_time = [8, 18, 28, 38];
schedule.process_id = [1, 1, 1, 1];
schedule.berth_id = [0, 0, 0, 0];
schedule.sea_condition = [0.2, 0.5, 0.3, 0.4];
schedule.assembly_level = [0, 2, 0, 2];

% 测试甘特图绘制
figure('Position', [100, 100, 1200, 600], 'Color', 'white');

% 工序颜色
process_colors = [
    0.9290, 0.6940, 0.3250;  % 装载
    0.4660, 0.7740, 0.8880;  % 前往风场
    0.6350, 0.5040, 0.7410;  % 基础安装
    0.4660, 0.7410, 0.3880;  % 塔筒安装
];

% 创建图例
dummy_handles = zeros(1, 4);
process_names = {'装载', '前往风场', '基础安装', '塔筒安装'};
for i = 1:4
    dummy_handles(i) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
    hold on;
end

% 风机类型图例
turbine_type_handles = zeros(1, 2);
turbine_type_handles(1) = plot(NaN, NaN, 'LineWidth', 4, 'Color', [0.2, 0.6, 0.9], 'LineStyle', '-');
turbine_type_handles(2) = plot(NaN, NaN, 'LineWidth', 4, 'Color', [0.9, 0.2, 0.2], 'LineStyle', '-');
turbine_type_names = {'小型风机(3.5MW)', '大型风机(8.0MW)'};

% 绘制任务条
for i = 1:length(schedule.turbine_id)
    turbine_id = schedule.turbine_id(i);
    vessel_id = schedule.vessel_id(i);
    start_time = schedule.start_time(i);
    end_time = schedule.end_time(i);
    duration = end_time - start_time;
    
    % 确定颜色
    color_idx = schedule.process_id(i) + 2; % 调整索引
    if color_idx > size(process_colors, 1)
        color_idx = 1;
    end
    
    % 根据风机类型确定边框
    edge_color = 'k';
    edge_width = 0.5;
    if turbine_id <= length(turbines)
        if turbines(turbine_id).type == 1 % 小型风机
            edge_color = [0.2, 0.6, 0.9]; % 蓝色边框
            edge_width = 1.5;
        else % 大型风机
            edge_color = [0.9, 0.2, 0.2]; % 红色边框
            edge_width = 2.0;
        end
    end
    
    % 绘制任务条
    rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
        'FaceColor', process_colors(color_idx,:), ...
        'EdgeColor', edge_color, 'LineWidth', edge_width);
    
    % 添加标签
    turbine_type_str = '';
    if turbine_id <= length(turbines)
        if turbines(turbine_id).type == 1
            turbine_type_str = '[S]';
        else
            turbine_type_str = '[L]';
        end
    end
    label_text = sprintf('T%d%s-P%d', turbine_id, turbine_type_str, schedule.process_id(i));
    
    text(start_time + duration/2, vessel_id, label_text, ...
        'HorizontalAlignment', 'center', ...
        'VerticalAlignment', 'middle', ...
        'FontSize', 8, 'FontWeight', 'bold');
end

% 添加船舶标签
for v = 1:length(vessels)
    vessel_type_str = '';
    if vessels(v).type == 1
        vessel_type_str = '单体船';
    else
        vessel_type_str = '双体船';
    end
    text(-5, v, sprintf('船舶 %d (%s)', v, vessel_type_str), ...
        'HorizontalAlignment', 'right', 'FontWeight', 'bold', 'FontSize', 10);
end

% 设置图例
all_handles = [dummy_handles, turbine_type_handles];
all_names = [process_names, turbine_type_names];
legend(all_handles, all_names, 'Location', 'southoutside', 'Orientation', 'horizontal', 'FontSize', 8);

% 设置图表属性
grid on;
title('海上风机安装调度计划 - 风机类型区分测试', 'FontSize', 14, 'FontWeight', 'bold');
xlabel('时间 (小时)', 'FontSize', 12);
xlim([0, 45]);
ylim([0, 3]);
set(gca, 'YTick', []);

% 添加统计信息
text(5, 2.7, '小型风机: 蓝色边框 [S]', 'FontSize', 10, 'Color', [0.2, 0.6, 0.9], 'FontWeight', 'bold');
text(5, 2.5, '大型风机: 红色边框 [L]', 'FontSize', 10, 'Color', [0.9, 0.2, 0.2], 'FontWeight', 'bold');

hold off;

fprintf('测试完成！\n');
fprintf('图表显示了风机类型的区分：\n');
fprintf('- 小型风机：蓝色边框，标签带[S]\n');
fprintf('- 大型风机：红色边框，标签带[L]\n');
