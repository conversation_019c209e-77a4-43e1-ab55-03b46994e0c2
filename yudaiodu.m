%% 具有多船舶优化功能的海上风电场安装调度器
% 此版本仅保留甘特图可视化功能

%% 输入数据定义

% 船舶数据
vessel_values = [
    4, 12, 10; % 传输速度
    1, 6, 4; % 容器容量基础
    1, 6, 4; % 轮机的容器容量
    15, 15, 15; % 锤速
    NaN, NaN, NaN; % 空行
    0, 0, 20; % 气隙百分比
    0, 0, 5; % 穿透深度边
    0, 0, 30; % 顶升速度
    NaN, NaN, NaN; % 空行
    NaN, NaN, NaN; % 空行
    150, 400, 200; % 包机成本
    NaN, NaN, NaN; % 空行
    0, 0, 1; % 自升式
    1, 0, 0 % 供应容器
];

% 操作数据
operation_values = [
    0, 2, 2; % 加载时间 地基
    0, 3, 3; % 涡轮机加载时间
    2, 8, 4; % 容器定位时间
    2, 2, 2; % 提升操作时间 基础
    1, 1, 1; % 提升操作时间 过渡片
    2, 2, 2; % 灌浆时间过渡片
    1, 1, 1; % 升降操作时间 塔
    2, 2, 2; % 起重操作时间 机舱
    1, 1, 1; % 提升操作时间 叶片
    2, 2, 2 % 刀片安装时间
];

% 风电场数据
farm_values = [
    30; % 要安装的基础数
    30; % 要安装的涡轮机数量
    178.3; % 涡轮转子直径
    0.5; % 安装位置之间的距离
    25; % 水深
    29; % 单桩穿透深度
    1; % 每个涡轮机的塔架件数
    3; % 每个涡轮机的叶片数
    54 % 农场到饲养港的距离
];

% 配置组合 - 仅使用一个配置以简化
config_values = [
    1, 1, 1;
];

%% 任务定义 - 为操作创建数据结构

% 定义每个任务类型的操作数
NUM_FDN_OPERATIONS = 4; % 基础操作：定位、顶升、安装、过渡件
NUM_WTB_OPERATIONS = 4; % 涡轮机操作：塔架、机舱、叶片、最终检查

% 定义用于标识的操作类型
OP_TYPE = struct(...
    'LOADING', 1, ...
    'POSITIONING', 2, ...
    'JACKING_UP', 3, ...
    'INSTALLATION', 4, ...
    'JACKING_DOWN', 5, ...
    'TRANSIT', 6, ...
    'REPOSITIONING', 7);

% 遗传算法的参数
GA_PARAMS = struct(...
    'POPULATION_SIZE', 50, ...
    'MAX_GENERATIONS', 100, ...
    'CROSSOVER_RATE', 0.8, ...
    'MUTATION_RATE', 0.2, ...
    'ELITE_COUNT', 5);

%% 辅助函数

function vessels = createVesselObjects(vessel_values, operation_values, vessel_types)
    % 创建包含所有相关参数的容器对象
    num_vessels = length(vessel_types);
    vessels = struct('id', {}, 'type', {}, 'transit_velocity', {}, 'capacity_fdn', {}, ...
        'capacity_wtb', {}, 'hammer_speed', {}, 'airgap', {}, ...
        'pen_leg', {}, 'jacking_speed', {}, 'charter_cost', {}, ...
        'is_jacking', {}, 'is_supply', {}, 'loading_time_fdn', {}, ...
        'loading_time_wtb', {}, 'positioning_time', {}, 'lifting_time_fdn', {}, ...
        'lifting_time_tp', {}, 'grouting_time_tp', {}, 'lifting_time_tower', {}, ...
        'lifting_time_nacelle', {}, 'lifting_time_blade', {}, 'installation_time_blade', {}, ...
        'current_tasks', {}, 'schedule', {}, 'failure_history', {});
    for i = 1:num_vessels
        vessel_type = vessel_types(i);
        vessels(i).id = i;
        vessels(i).type = vessel_type;
        vessels(i).transit_velocity = vessel_values(1, vessel_type);
        vessels(i).capacity_fdn = vessel_values(2, vessel_type);
        vessels(i).capacity_wtb = vessel_values(3, vessel_type);
        vessels(i).hammer_speed = vessel_values(4, vessel_type);
        vessels(i).airgap = vessel_values(6, vessel_type);
        vessels(i).pen_leg = vessel_values(7, vessel_type);
        vessels(i).jacking_speed = vessel_values(8, vessel_type);
        vessels(i).charter_cost = vessel_values(11, vessel_type);
        vessels(i).is_jacking = vessel_values(13, vessel_type);
        vessels(i).is_supply = vessel_values(14, vessel_type);
        vessels(i).loading_time_fdn = operation_values(1, vessel_type);
        vessels(i).loading_time_wtb = operation_values(2, vessel_type);
        vessels(i).positioning_time = operation_values(3, vessel_type);
        vessels(i).lifting_time_fdn = operation_values(4, vessel_type);
        vessels(i).lifting_time_tp = operation_values(5, vessel_type);
        vessels(i).grouting_time_tp = operation_values(6, vessel_type);
        vessels(i).lifting_time_tower = operation_values(7, vessel_type);
        vessels(i).lifting_time_nacelle = operation_values(8, vessel_type);
        vessels(i).lifting_time_blade = operation_values(9, vessel_type);
        vessels(i).installation_time_blade = operation_values(10, vessel_type);
        vessels(i).current_tasks = [];
        vessels(i).schedule = [];
        vessels(i).failure_history = [];
    end
end

function [fdn_tasks, wtb_tasks] = createTaskObjects(N_FDN, N_WTB, vessels, farm_values, operation_values)
    % 创建基础和涡轮机的任务对象
    WD = farm_values(5); % 水深
    PEN_FDN = farm_values(6); % 单桩贯入深度
    N_TOW = farm_values(7); % 每个涡轮机的塔架件数
    N_BLADE = farm_values(8); % 每个涡轮机的叶片数
    for i = 1:N_FDN
        fdn_tasks(i).id = i;
        fdn_tasks(i).type = 'Foundation';
        fdn_tasks(i).location = i;
        fdn_tasks(i).status = 'pending';
        fdn_tasks(i).assigned_vessel = 0;
        fdn_tasks(i).operations = struct(...
            'positioning', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0), ...
            'jacking_up', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0), ...
            'installation', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0), ...
            'transition_piece', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0));
        fdn_tasks(i).vessel_durations = repmat(struct('positioning', 0, 'jacking_up', 0, 'installation', 0, 'transition_piece', 0), 1, length(vessels));
    end
    for i = 1:N_WTB
        wtb_tasks(i).id = i;
        wtb_tasks(i).type = 'Turbine';
        wtb_tasks(i).location = i;
        wtb_tasks(i).status = 'pending';
        wtb_tasks(i).assigned_vessel = 0;
        wtb_tasks(i).operations = struct(...
            'tower', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0), ...
            'nacelle', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0), ...
            'blades', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0), ...
            'commissioning', struct('duration', 0, 'status', 'pending', 'start_time', 0, 'end_time', 0));
        wtb_tasks(i).vessel_durations = repmat(struct('tower', 0, 'nacelle', 0, 'blades', 0, 'commissioning', 0), 1, length(vessels));
    end
    for v = 1:length(vessels)
        vessel = vessels(v);
        for i = 1:N_FDN
            pos_duration = vessel.positioning_time;
            jack_duration = 0;
            if vessel.is_jacking == 1
                jack_duration = (vessel.pen_leg + vessel.airgap + WD) / vessel.jacking_speed;
            end
            install_duration = vessel.lifting_time_fdn + (PEN_FDN / vessel.hammer_speed);
            tp_duration = vessel.lifting_time_tp + vessel.grouting_time_tp;
            fdn_tasks(i).vessel_durations(v).positioning = pos_duration;
            fdn_tasks(i).vessel_durations(v).jacking_up = jack_duration;
            fdn_tasks(i).vessel_durations(v).installation = install_duration;
            fdn_tasks(i).vessel_durations(v).transition_piece = tp_duration;
        end
        for i = 1:N_WTB
            tower_duration = N_TOW * vessel.lifting_time_tower;
            nacelle_duration = vessel.lifting_time_nacelle;
            blade_duration = N_BLADE * (vessel.lifting_time_blade + vessel.installation_time_blade);
            commissioning_duration = 8; % 调试的固定时间 (8 小时)
            wtb_tasks(i).vessel_durations(v).tower = tower_duration;
            wtb_tasks(i).vessel_durations(v).nacelle = nacelle_duration;
            wtb_tasks(i).vessel_durations(v).blades = blade_duration;
            wtb_tasks(i).vessel_durations(v).commissioning = commissioning_duration;
        end
    end
end

function [schedule, best_fitness, convergence_data] = scheduleTasksGA(fdn_tasks, wtb_tasks, vessels, farm_values, ga_params)
    % 使用遗传算法安排任务
    N_FDN = length(fdn_tasks);
    N_WTB = length(wtb_tasks);
    num_vessels = length(vessels);
    population_size = ga_params.POPULATION_SIZE;
    max_generations = ga_params.MAX_GENERATIONS;
    crossover_rate = ga_params.CROSSOVER_RATE;
    mutation_rate = ga_params.MUTATION_RATE;
    elite_count = ga_params.ELITE_COUNT;
    population = initializePopulation(population_size, N_FDN, N_WTB, num_vessels);
    fitness_values = zeros(population_size, 1);
    for i = 1:population_size
        fitness_values(i) = evaluateFitness(population(i, :), fdn_tasks, wtb_tasks, vessels, farm_values);
    end
    convergence_data = struct('best_fitness', zeros(max_generations, 1), ...
        'avg_fitness', zeros(max_generations, 1), ...
        'generation', 1:max_generations, ...
        'max_generations', max_generations);
    for gen = 1:max_generations
        parents = selectParents(population, fitness_values, population_size);
        offspring = createOffspring(parents, N_FDN, N_WTB, num_vessels, crossover_rate, mutation_rate);
        offspring_fitness = zeros(size(offspring, 1), 1);
        for i = 1:size(offspring, 1)
            offspring_fitness(i) = evaluateFitness(offspring(i, :), fdn_tasks, wtb_tasks, vessels, farm_values);
        end
        [sorted_fitness, indices] = sort(fitness_values);
        elite_indices = indices(1:elite_count);
        elite_solutions = population(elite_indices, :);
        [combined_pop, combined_fitness] = combinePopulations(population, fitness_values, offspring, offspring_fitness);
        [population, fitness_values] = selectNextGeneration(combined_pop, combined_fitness, population_size, elite_solutions);
        [best_fitness, best_idx] = min(fitness_values);
        convergence_data.best_fitness(gen) = best_fitness;
        convergence_data.avg_fitness(gen) = mean(fitness_values);
        if mod(gen, 10) == 0
            fprintf('Generation %d: Best Fitness = %.2f, Avg Fitness = %.2f\n', ...
                gen, best_fitness, convergence_data.avg_fitness(gen));
        end
    end
    [best_fitness, best_idx] = min(fitness_values);
    best_solution = population(best_idx, :);
    schedule = decodeSolution(best_solution, fdn_tasks, wtb_tasks, vessels, farm_values);
end

function population = initializePopulation(population_size, N_FDN, N_WTB, num_vessels)
    total_tasks = N_FDN + N_WTB;
    population = zeros(population_size, total_tasks * 2);
    for i = 1:population_size
        task_assignments = randi(num_vessels, 1, total_tasks);
        task_sequence = randperm(total_tasks);
        chromosome = [task_assignments, task_sequence];
        population(i, :) = chromosome;
    end
end

function fitness = evaluateFitness(chromosome, fdn_tasks, wtb_tasks, vessels, farm_values)
    schedule = decodeSolution(chromosome, fdn_tasks, wtb_tasks, vessels, farm_values);
    [makespan, total_cost, ~] = calculatePerformanceMetrics(schedule, vessels);
    makespan_weight = 0.6;
    cost_weight = 0.4;
    norm_makespan = makespan / (30 * 24); % 按30天标准化
    norm_cost = total_cost / 10000; % 标准化10000个单位
    fitness = makespan_weight * norm_makespan + cost_weight * norm_cost;
end

function schedule = decodeSolution(chromosome, fdn_tasks, wtb_tasks, vessels, farm_values)
    N_FDN = length(fdn_tasks);
    N_WTB = length(wtb_tasks);
    total_tasks = N_FDN + N_WTB;
    PARK_DIS = farm_values(9); % 港口到农场的距离
    INT_D = farm_values(4); % 安装位置之间的距离
    WD = farm_values(5); % 水深
    task_assignments = chromosome(1:total_tasks);
    task_sequence = chromosome(total_tasks+1:end);
    schedule = struct();
    schedule.tasks = struct('id', {}, 'type', {}, 'vessel_id', {}, 'location', {}, 'start_time', {}, 'end_time', {}, 'operations', {});
    all_tasks = [fdn_tasks, wtb_tasks];
    vessel_schedules = cell(length(vessels), 1);
    vessel_locations = zeros(length(vessels), 1); % 从港口开始（位置0）
    vessel_times = zeros(length(vessels), 1); % 从时间0开始
    for i = 1:length(task_sequence)
        task_idx = task_sequence(i);
        vessel_idx = task_assignments(task_idx);
        task = all_tasks(task_idx);
        is_foundation = task_idx <= N_FDN;
        vessel = vessels(vessel_idx);
        current_time = vessel_times(vessel_idx);
        current_location = vessel_locations(vessel_idx);
        if current_location == 0 % 来自港口
            transit_time = PARK_DIS / vessel.transit_velocity;
        else % 来自另一个安装位置
            distance = INT_D * abs(current_location - task.location);
            transit_time = distance / vessel.transit_velocity;
        end
        arrival_time = current_time + transit_time;
        task_schedule = struct();
        task_schedule.id = task.id;
        task_schedule.type = task.type;
        task_schedule.vessel_id = vessel_idx;
        task_schedule.location = task.location;
        if is_foundation
            operations = struct();
            pos_start = arrival_time;
            pos_duration = vessel.positioning_time;
            pos_end = pos_start + pos_duration;
            operations.positioning = struct('start_time', pos_start, 'end_time', pos_end, 'duration', pos_duration);
            current_time = pos_end;
            jack_duration = 0;
            if vessel.is_jacking == 1
                jack_duration = (vessel.pen_leg + vessel.airgap + WD) / vessel.jacking_speed;
                jack_start = current_time;
                jack_end = jack_start + jack_duration;
                operations.jacking_up = struct('start_time', jack_start, 'end_time', jack_end, 'duration', jack_duration);
                current_time = jack_end;
            end
            install_start = current_time;
            install_duration = vessel.lifting_time_fdn + (farm_values(6) / vessel.hammer_speed);
            install_end = install_start + install_duration;
            operations.installation = struct('start_time', install_start, 'end_time', install_end, 'duration', install_duration);
            current_time = install_end;
            tp_start = current_time;
            tp_duration = vessel.lifting_time_tp + vessel.grouting_time_tp;
            tp_end = tp_start + tp_duration;
            operations.transition_piece = struct('start_time', tp_start, 'end_time', tp_end, 'duration', tp_duration);
            current_time = tp_end;
            if vessel.is_jacking == 1 && jack_duration > 0
                jack_down_start = current_time;
                jack_down_end = jack_down_start + jack_duration;
                operations.jacking_down = struct('start_time', jack_down_start, 'end_time', jack_down_end, 'duration', jack_duration);
                current_time = jack_down_end;
            end
            task_schedule.operations = operations;
            task_schedule.start_time = pos_start;
            task_schedule.end_time = current_time;
        else
            operations = struct();
            pos_start = arrival_time;
            pos_duration = vessel.positioning_time;
            pos_end = pos_start + pos_duration;
            operations.positioning = struct('start_time', pos_start, 'end_time', pos_end, 'duration', pos_duration);
            current_time = pos_end;
            jack_duration = 0;
            if vessel.is_jacking == 1
                jack_duration = (vessel.pen_leg + vessel.airgap + WD) / vessel.jacking_speed;
                jack_start = current_time;
                jack_end = jack_start + jack_duration;
                operations.jacking_up = struct('start_time', jack_start, 'end_time', jack_end, 'duration', jack_duration);
                current_time = jack_end;
            end
            tower_start = current_time;
            tower_duration = farm_values(7) * vessel.lifting_time_tower;
            tower_end = tower_start + tower_duration;
            operations.tower = struct('start_time', tower_start, 'end_time', tower_end, 'duration', tower_duration);
            current_time = tower_end;
            nacelle_start = current_time;
            nacelle_duration = vessel.lifting_time_nacelle;
            nacelle_end = nacelle_start + nacelle_duration;
            operations.nacelle = struct('start_time', nacelle_start, 'end_time', nacelle_end, 'duration', nacelle_duration);
            current_time = nacelle_end;
            blades_start = current_time;
            blades_duration = farm_values(8) * (vessel.lifting_time_blade + vessel.installation_time_blade);
            blades_end = blades_start + blades_duration;
            operations.blades = struct('start_time', blades_start, 'end_time', blades_end, 'duration', blades_duration);
            current_time = blades_end;
            if vessel.is_jacking == 1 && jack_duration > 0
                jack_down_start = current_time;
                jack_down_end = jack_down_start + jack_duration;
                operations.jacking_down = struct('start_time', jack_down_start, 'end_time', jack_down_end, 'duration', jack_duration);
                current_time = jack_down_end;
            end
            task_schedule.operations = operations;
            task_schedule.start_time = pos_start;
            task_schedule.end_time = current_time;
        end
        vessel_times(vessel_idx) = current_time;
        vessel_locations(vessel_idx) = task.location;
        vessel_schedules{vessel_idx} = [vessel_schedules{vessel_idx}, task_schedule];
        schedule.tasks = [schedule.tasks, task_schedule];
    end
    schedule.vessel_schedules = vessel_schedules;
end

function [makespan, total_cost, vessel_utilization] = calculatePerformanceMetrics(schedule, vessels)
    end_times = [schedule.tasks.end_time];
    makespan = max(end_times);
    vessel_utilization = zeros(1, length(vessels));
    vessel_finish_times = zeros(1, length(vessels));
    vessel_tasks = cell(length(vessels), 1);
    for i = 1:length(schedule.tasks)
        task = schedule.tasks(i);
        vessel_id = task.vessel_id;
        if vessel_id > 0 && vessel_id <= length(vessel_tasks)
            vessel_tasks{vessel_id} = [vessel_tasks{vessel_id}, task];
            vessel_finish_times(vessel_id) = max(vessel_finish_times(vessel_id), task.end_time);
        end
    end
    total_cost = 0;
    for v = 1:length(vessels)
        vessel = vessels(v);
        if ~isempty(vessel_tasks{v})
            active_time = 0;
            v_tasks = vessel_tasks{v};
            for i = 1:length(v_tasks)
                task_duration = v_tasks(i).end_time - v_tasks(i).start_time;
                active_time = active_time + task_duration;
            end
            vessel_utilization(v) = active_time / vessel_finish_times(v);
            vessel_cost = (vessel_finish_times(v) / 24) * vessel.charter_cost;
            total_cost = total_cost + vessel_cost;
        else
            vessel_utilization(v) = 0;
        end
    end
end

function parents = selectParents(population, fitness_values, population_size)
    % 锦标赛选择
    tournament_size = 3;
    num_parents = population_size;
    parents = zeros(num_parents, size(population, 2));
    for i = 1:num_parents
        tournament_indices = randperm(population_size, tournament_size);
        tournament_fitness = fitness_values(tournament_indices);
        [~, best_idx] = min(tournament_fitness);
        parents(i, :) = population(tournament_indices(best_idx), :);
    end
end

function offspring = createOffspring(parents, N_FDN, N_WTB, num_vessels, crossover_rate, mutation_rate)
    % 交叉和变异操作
    num_parents = size(parents, 1);
    total_tasks = N_FDN + N_WTB;
    offspring = zeros(num_parents, total_tasks * 2);
    for i = 1:2:num_parents-1
        parent1 = parents(i, :);
        parent2 = parents(i+1, :);
        if rand < crossover_rate
            % 单点交叉
            crossover_point = randi(total_tasks * 2 - 1);
            offspring(i, :) = [parent1(1:crossover_point), parent2(crossover_point+1:end)];
            offspring(i+1, :) = [parent2(1:crossover_point), parent1(crossover_point+1:end)];
        else
            offspring(i, :) = parent1;
            offspring(i+1, :) = parent2;
        end
        % 变异
        for j = i:i+1
            if rand < mutation_rate
                mutation_point = randi(total_tasks);
                offspring(j, mutation_point) = randi(num_vessels);
            end
            % 修复任务序列
            task_seq = offspring(j, total_tasks+1:end);
            [~, idx] = sort(task_seq);
            offspring(j, total_tasks+1:end) = idx;
        end
    end
    if mod(num_parents, 2) == 1
        offspring(end, :) = parents(end, :);
    end
end

function [combined_pop, combined_fitness] = combinePopulations(population, fitness_values, offspring, offspring_fitness)
    % 合并种群
    combined_pop = [population; offspring];
    combined_fitness = [fitness_values; offspring_fitness];
end

function [new_population, new_fitness] = selectNextGeneration(combined_pop, combined_fitness, population_size, elite_solutions)
    % 选择下一代
    [~, idx] = sort(combined_fitness);
    new_population = [elite_solutions; combined_pop(idx(1:population_size-size(elite_solutions, 1)), :)];
    new_fitness = combined_fitness(idx(1:population_size));
end

%% 甘特图可视化函数

function visualizeSchedule(schedule, vessels, reschedule_stats)
    if nargin < 3
        reschedule_stats = [];
    end
    figure('Position', [100, 100, 1200, 600], 'Name', 'Installation Schedule');
    colors = struct('Foundation', [0.3, 0.6, 0.9], 'Turbine', [0.9, 0.6, 0.3], 'rescheduled', [0.2, 0.8, 0.3]);
    hold on;
    num_vessels = length(vessels);
    vessel_positions = zeros(num_vessels, 2);
    for v = 1:num_vessels
        vessel_positions(v, 1) = v * 2 - 0.25; % Foundation position
        vessel_positions(v, 2) = v * 2 + 0.25; % Turbine position
    end
    rescheduled_tasks = [];
    if ~isempty(reschedule_stats)
        for i = 1:length(reschedule_stats)
            event = reschedule_stats(i);
            event_time = event.time;
            for j = 1:length(schedule.tasks)
                task = schedule.tasks(j);
                if (strcmp(event.strategy, 'right-shift') && task.vessel_id == event.vessel && task.start_time >= event_time) || ...
                   (strcmp(event.strategy, 'complete-reschedule') && task.start_time >= event_time)
                    rescheduled_tasks = [rescheduled_tasks j];
                end
            end
        end
        rescheduled_tasks = unique(rescheduled_tasks);
    end
    for i = 1:length(schedule.tasks)
        task = schedule.tasks(i);
        v_id = task.vessel_id;
        if v_id <= 0 || v_id > num_vessels
            continue;
        end
        if strcmp(task.type, 'Foundation')
            y_pos = vessel_positions(v_id, 1);
            color = colors.Foundation;
        else
            y_pos = vessel_positions(v_id, 2);
            color = colors.Turbine;
        end
        if ismember(i, rescheduled_tasks)
            edgeColor = colors.rescheduled;
            lineWidth = 2;
        else
            edgeColor = 'k';
            lineWidth = 1;
        end
        bar_start = task.start_time;
        bar_width = task.end_time - task.start_time;
        rectangle('Position', [bar_start, y_pos-0.2, bar_width, 0.4], ...
            'FaceColor', color, 'EdgeColor', edgeColor, 'LineWidth', lineWidth);
        task_label = '';
        if strcmp(task.type, 'Foundation')
            task_label = sprintf('F%d', task.id);
        else
            task_label = sprintf('T%d', task.id);
        end
        text(bar_start + bar_width/2, y_pos, task_label, ...
            'HorizontalAlignment', 'center', 'FontSize', 8, 'FontWeight', 'bold');
    end
    y_ticks = [];
    y_labels = {};
    for v = 1:num_vessels
        y_ticks = [y_ticks, v*2];
        y_labels{end+1} = sprintf('Vessel %d', v);
    end
    set(gca, 'YTick', y_ticks, 'YTickLabel', y_labels);
    ylim([0, max(y_ticks) + 1]);
    grid on;
    title('Offshore Wind Farm Installation Schedule', 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('Time (hours)');
    ylabel('Vessels');
    hold off;
end

%% 主循环 - 处理配置选项

option_idx = 1;
option = config_values(option_idx, :);
VT_FDN = option(2); % 基础的容器类型
VT_WTB = option(3); % 涡轮机的容器类型

fprintf('Processing configuration option %d: Foundation vessel type %d, Turbine vessel type %d\n', ...
    option_idx, VT_FDN, VT_WTB);

% 提取风电场参数
N_FDN = farm_values(1); % 要安装的基础数
N_WTB = farm_values(2); % 要安装的涡轮机数量

% 创建船舶对象
vessels = createVesselObjects(vessel_values, operation_values, [VT_FDN, VT_WTB]);

% 创建任务对象
[fdn_tasks, wtb_tasks] = createTaskObjects(N_FDN, N_WTB, vessels, farm_values, operation_values);

% 使用遗传算法进行调度
fprintf('Running scheduling optimization...\n');
[schedule, ~, ~] = scheduleTasksGA(fdn_tasks, wtb_tasks, vessels, farm_values, GA_PARAMS);

% 可视化调度
visualizeSchedule(schedule, vessels, []);