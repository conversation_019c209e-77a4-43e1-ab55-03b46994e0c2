%% 单体船 vs 双体船效率对比分析（清洁版）
% 10条单体船 vs 5条双体船（拼接而成）运送20台风机
% 重点分析双体船甲板组装优势和拼接时间成本
% 删除故障和重调度功能

clear all;
close all;
clc;
rng(42);

%% 问题参数设置
TURBINE_COUNT = 20;        % 风机总数
VESSEL_COUNT = 10;         % 船舶总数（单体船模式）
BERTH_COUNT = 5;           % 泊位数量

% 风机参数（统一大型风机以突出双体船优势）
TURBINE_POWER = 8.0;       % MW
TURBINE_PROCESS_TIMES = [30, 20, 15, 45]; % [基础, 塔筒, 机舱, 叶片] 安装时间(小时)

% 船舶和组装参数
SHIP_ASSEMBLY_TIME = 35;   % 船上组装时间(小时)
FINAL_INSTALLATION_TIME = 12; % 完全组装后的最终安装时间(小时)
WELDING_TIME = 6;          % 双体船拼接焊接时间(小时)

% 距离和速度参数
PORT_TO_FARM_DISTANCE = 80; % 港口到风场距离(km)
VESSEL_SPEED = 15;          % 船舶航行速度(km/h)
LOADING_TIME = 8;           % 装载时间(小时)

%% 数据结构初始化
% 初始化风机数据
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'power', num2cell(ones(1, TURBINE_COUNT) * TURBINE_POWER), ...
                 'processes', cell(1, TURBINE_COUNT));

% 为每台风机设置工序时间（添加±10%随机变化）
for i = 1:TURBINE_COUNT
    variation = 0.9 + 0.2*rand(1, length(TURBINE_PROCESS_TIMES));
    turbines(i).processes = TURBINE_PROCESS_TIMES .* variation;
end

% 初始化单体船数据
single_vessels = struct('id', num2cell(1:VESSEL_COUNT), ...
                       'speed', num2cell(ones(1, VESSEL_COUNT) * VESSEL_SPEED), ...
                       'loading_time', num2cell(ones(1, VESSEL_COUNT) * LOADING_TIME));

% 初始化双体船数据（由单体船拼接而成）
dual_vessel_count = VESSEL_COUNT / 2; % 5条双体船
dual_vessels = struct('id', num2cell(1:dual_vessel_count), ...
                     'speed', num2cell(ones(1, dual_vessel_count) * VESSEL_SPEED), ...
                     'loading_time', num2cell(ones(1, dual_vessel_count) * LOADING_TIME), ...
                     'welding_time', num2cell(ones(1, dual_vessel_count) * WELDING_TIME), ...
                     'assembly_time', num2cell(ones(1, dual_vessel_count) * SHIP_ASSEMBLY_TIME), ...
                     'final_install_time', num2cell(ones(1, dual_vessel_count) * FINAL_INSTALLATION_TIME));

% 初始化泊位
berths = struct('id', num2cell(1:BERTH_COUNT));

%% 场景1：10条单体船运送20台风机
fprintf('=== 场景1：10条单体船运送20台风机 ===\n');
fprintf('船舶配置：%d条单体船\n', VESSEL_COUNT);
fprintf('风机配置：%d台大型风机(%.1fMW)\n', TURBINE_COUNT, TURBINE_POWER);

% 生成单体船调度
single_schedule = generateSingleVesselSchedule(turbines, single_vessels, berths);
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时\n', single_makespan);

% 绘制单体船甘特图
plotSingleVesselGantt(single_schedule, single_vessels, berths, single_makespan, turbines);

%% 场景2：5条双体船（拼接而成）运送20台风机
fprintf('\n=== 场景2：5条双体船运送20台风机 ===\n');
fprintf('船舶配置：%d条双体船（由%d条单体船拼接而成）\n', dual_vessel_count, VESSEL_COUNT);
fprintf('风机配置：%d台大型风机(%.1fMW)\n', TURBINE_COUNT, TURBINE_POWER);

% 生成双体船调度（包含拼接时间）
dual_schedule = generateDualVesselSchedule(turbines, dual_vessels, berths);
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时（包含拼接时间）\n', dual_makespan);

% 绘制双体船甘特图
plotDualVesselGantt(dual_schedule, dual_vessels, berths, dual_makespan, turbines);

%% 效率对比分析
fprintf('\n=== 效率对比分析 ===\n');
efficiency_improvement = (single_makespan - dual_makespan) / single_makespan * 100;

if efficiency_improvement > 0
    fprintf('✓ 双体船效率提升：%.2f%%\n', efficiency_improvement);
    fprintf('✓ 节省时间：%.2f 小时\n', single_makespan - dual_makespan);
    fprintf('✓ 双体船方案更优！\n');
else
    fprintf('✗ 单体船效率更高：%.2f%%\n', -efficiency_improvement);
    fprintf('✗ 双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
    fprintf('✗ 单体船方案更优！\n');
end

% 详细时间分析
analyzeTimeBreakdown(single_schedule, dual_schedule, single_makespan, dual_makespan);

%% 单体船调度生成函数
function schedule = generateSingleVesselSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 航行时间
    travel_time = 80 / 15; % 港口到风场时间
    
    % 为每台风机分配船舶（轮询分配）
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 泊位分配（选择最早可用的泊位）
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载阶段
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        
        % 添加装载任务
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 1);
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 1);
        
        % 执行所有安装工序
        current_time = travel_end;
        process_times = turbines(turbine_idx).processes;
        
        for p = 1:length(process_times)
            process_start = current_time;
            process_end = process_start + process_times(p);
            schedule = addTask(schedule, turbine_idx, vessel_idx, process_start, process_end, p, 0, 1);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 1);
        
        % 更新船舶可用时间
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 双体船调度生成函数
function schedule = generateDualVesselSchedule(turbines, vessels, berths)
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 首先添加拼接时间（所有双体船需要拼接时间）
    welding_time = vessels(1).welding_time;
    for v = 1:vessel_count
        vessel_avail_time(v) = welding_time;
        % 添加拼接任务到调度
        schedule = addTask(schedule, 0, v, 0, welding_time, -3, 0, 2); % -3表示拼接
    end
    
    % 航行时间
    travel_time = 80 / 15;
    
    % 为每台风机分配船舶（轮询分配）
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载阶段
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        
        % 添加装载任务
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 2);
        
        % 船上组装阶段
        assembly_start = loading_end;
        assembly_end = assembly_start + vessels(vessel_idx).assembly_time;
        berth_avail_time(berth_idx) = assembly_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, assembly_start, assembly_end, 5, berth_idx, 2);
        
        % 前往风场
        travel_start = assembly_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 2);
        
        % 最终安装（替代所有常规工序）
        final_start = travel_end;
        final_end = final_start + vessels(vessel_idx).final_install_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, final_start, final_end, 6, 0, 2);
        
        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 2);
        
        % 更新船舶可用时间
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 添加任务到调度的辅助函数
function schedule = addTask(schedule, turbine_id, vessel_id, start_time, end_time, process_id, berth_id, vessel_type)
    schedule.turbine_id(end+1) = turbine_id;
    schedule.vessel_id(end+1) = vessel_id;
    schedule.start_time(end+1) = start_time;
    schedule.end_time(end+1) = end_time;
    schedule.process_id(end+1) = process_id;
    schedule.berth_id(end+1) = berth_id;
    schedule.vessel_type(end+1) = vessel_type;
end

%% 时间构成分析函数
function analyzeTimeBreakdown(single_schedule, dual_schedule, single_makespan, dual_makespan)
    fprintf('\n=== 详细时间构成分析 ===\n');

    % 分析单体船时间构成
    single_loading_time = sum((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
    single_travel_time = sum((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time));
    single_install_time = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time));

    fprintf('单体船时间构成：\n');
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', single_loading_time, single_loading_time/single_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', single_travel_time, single_travel_time/single_makespan*100);
    fprintf('  安装时间：%.2f 小时 (%.1f%%)\n', single_install_time, single_install_time/single_makespan*100);

    % 分析双体船时间构成
    dual_welding_time = sum((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_loading_time = sum((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_assembly_time = sum((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_travel_time = sum((dual_schedule.process_id == -1 | dual_schedule.process_id == -2) .* (dual_schedule.end_time - dual_schedule.start_time));
    dual_install_time = sum((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));

    fprintf('\n双体船时间构成：\n');
    fprintf('  拼接时间：%.2f 小时 (%.1f%%)\n', dual_welding_time, dual_welding_time/dual_makespan*100);
    fprintf('  装载时间：%.2f 小时 (%.1f%%)\n', dual_loading_time, dual_loading_time/dual_makespan*100);
    fprintf('  船上组装：%.2f 小时 (%.1f%%)\n', dual_assembly_time, dual_assembly_time/dual_makespan*100);
    fprintf('  航行时间：%.2f 小时 (%.1f%%)\n', dual_travel_time, dual_travel_time/dual_makespan*100);
    fprintf('  最终安装：%.2f 小时 (%.1f%%)\n', dual_install_time, dual_install_time/dual_makespan*100);

    % 关键优势分析
    fprintf('\n=== 关键技术优势分析 ===\n');
    install_efficiency = (single_install_time - dual_install_time) / single_install_time * 100;
    fprintf('海上安装时间节省：%.2f%% (%.2f → %.2f 小时)\n', ...
        install_efficiency, single_install_time, dual_install_time);

    total_assembly_time = dual_assembly_time + dual_install_time;
    overall_install_efficiency = (single_install_time - total_assembly_time) / single_install_time * 100;
    fprintf('整体安装效率提升：%.2f%% (%.2f → %.2f 小时)\n', ...
        overall_install_efficiency, single_install_time, total_assembly_time);

    if dual_welding_time > 0
        roi = (single_install_time - dual_install_time) / dual_welding_time;
        fprintf('拼接时间投资回报：%.2f倍 (节省%.2f小时 vs 投入%.2f小时)\n', ...
            roi, single_install_time - dual_install_time, dual_welding_time);
    end

    % 规模效应分析
    fprintf('\n=== 规模效应分析 ===\n');
    fprintf('单台风机平均工期：\n');
    fprintf('  单体船：%.2f 小时/台\n', single_makespan / 20);
    fprintf('  双体船：%.2f 小时/台\n', dual_makespan / 20);
end

%% 单体船甘特图绘制函数
function plotSingleVesselGantt(schedule, vessels, berths, makespan, turbines)
    figure('Position', [50, 50, 1400, 800], 'Color', 'white');

    % 工序颜色定义
    colors = [
        0.8500, 0.3250, 0.0980;  % 装载 - 红橙
        0.0000, 0.4470, 0.7410;  % 前往风场 - 蓝色
        0.4660, 0.6740, 0.1880;  % 基础安装 - 绿色
        0.9290, 0.6940, 0.1250;  % 塔筒安装 - 黄色
        0.4940, 0.1840, 0.5560;  % 机舱安装 - 紫色
        0.6350, 0.0780, 0.1840;  % 叶片安装 - 深红
        0.3010, 0.7450, 0.9330;  % 船上组装 - 青色
        0.8500, 0.3250, 0.0980;  % 最终安装 - 橙红
        0.2500, 0.2500, 0.2500;  % 返回港口 - 深灰
        0.7500, 0.7500, 0.7500;  % 拼接焊接 - 浅灰
    ];

    %% 子图1：船舶甘特图
    subplot(2, 1, 1);
    hold on;

    vessel_count = length(vessels);

    for i = 1:length(schedule.turbine_id)
        vessel_id = schedule.vessel_id(i);
        start_time = schedule.start_time(i);
        end_time = schedule.end_time(i);
        duration = end_time - start_time;
        process_id = schedule.process_id(i);
        berth_id = schedule.berth_id(i);
        turbine_id = schedule.turbine_id(i);

        % 确定颜色索引
        color_idx = getColorIndex(process_id);

        % 根据是否使用泊位调整边框样式
        if berth_id > 0
            edge_style = '-';
            edge_width = 1.2;
        else
            edge_style = '--';
            edge_width = 0.8;
        end

        % 绘制任务条
        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', ...
            'LineWidth', edge_width, 'LineStyle', edge_style);

        % 添加标签
        if duration > 3
            if turbine_id > 0
                if berth_id > 0
                    label_text = sprintf('T%d-B%d', turbine_id, berth_id);
                else
                    if process_id >= 1 && process_id <= 4
                        process_names = {'基础', '塔筒', '机舱', '叶片'};
                        label_text = sprintf('T%d-%s', turbine_id, process_names{process_id});
                    else
                        label_text = sprintf('T%d', turbine_id);
                    end
                end
            elseif process_id == -1
                label_text = '→风场';
            elseif process_id == -2
                label_text = '←港口';
            else
                label_text = '';
            end

            if ~isempty(label_text)
                text(start_time + duration/2, vessel_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加船舶标签
    for v = 1:vessel_count
        text(-makespan*0.02, v, sprintf('船%d', v), ...
            'HorizontalAlignment', 'right', 'FontSize', 10, 'FontWeight', 'bold');
    end

    title(sprintf('单体船调度方案 - 总工期: %.2f小时', makespan), ...
        'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, vessel_count + 1]);
    xlim([0, makespan * 1.05]);
    grid on;

    %% 子图2：泊位利用率
    subplot(2, 1, 2);
    hold on;

    berth_count = length(berths);

    for i = 1:length(schedule.turbine_id)
        berth_id = schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            start_time = schedule.start_time(i);
            end_time = schedule.end_time(i);
            duration = end_time - start_time;
            process_id = schedule.process_id(i);
            turbine_id = schedule.turbine_id(i);
            vessel_id = schedule.vessel_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, berth_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 1.0);

            if turbine_id > 0 && duration > 5
                label_text = sprintf('V%d-T%d', vessel_id, turbine_id);
                text(start_time + duration/2, berth_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加泊位标签
    for b = 1:berth_count
        text(-makespan*0.02, b, sprintf('泊位%d', b), ...
            'HorizontalAlignment', 'right', 'FontSize', 10, 'FontWeight', 'bold');
    end

    title('单体船方案 - 泊位利用情况', 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('泊位编号', 'FontSize', 12);
    ylim([0, berth_count + 1]);
    xlim([0, makespan * 1.05]);
    grid on;

    % 添加图例
    addLegend(colors);

    hold off;
end

%% 双体船甘特图绘制函数
function plotDualVesselGantt(schedule, vessels, berths, makespan, turbines)
    figure('Position', [100, 100, 1400, 800], 'Color', 'white');

    % 工序颜色定义
    colors = [
        0.8500, 0.3250, 0.0980;  % 装载 - 红橙
        0.0000, 0.4470, 0.7410;  % 前往风场 - 蓝色
        0.4660, 0.6740, 0.1880;  % 基础安装 - 绿色
        0.9290, 0.6940, 0.1250;  % 塔筒安装 - 黄色
        0.4940, 0.1840, 0.5560;  % 机舱安装 - 紫色
        0.6350, 0.0780, 0.1840;  % 叶片安装 - 深红
        0.3010, 0.7450, 0.9330;  % 船上组装 - 青色
        0.8500, 0.3250, 0.0980;  % 最终安装 - 橙红
        0.2500, 0.2500, 0.2500;  % 返回港口 - 深灰
        0.7500, 0.7500, 0.7500;  % 拼接焊接 - 浅灰
    ];

    %% 子图1：船舶甘特图
    subplot(2, 1, 1);
    hold on;

    vessel_count = length(vessels);

    for i = 1:length(schedule.turbine_id)
        vessel_id = schedule.vessel_id(i);
        start_time = schedule.start_time(i);
        end_time = schedule.end_time(i);
        duration = end_time - start_time;
        process_id = schedule.process_id(i);
        berth_id = schedule.berth_id(i);
        turbine_id = schedule.turbine_id(i);

        % 确定颜色索引
        color_idx = getColorIndex(process_id);

        % 根据是否使用泊位调整边框样式
        if berth_id > 0
            edge_style = '-';
            edge_width = 1.2;
        else
            edge_style = '--';
            edge_width = 0.8;
        end

        % 绘制任务条
        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', ...
            'LineWidth', edge_width, 'LineStyle', edge_style);

        % 添加标签
        if duration > 2
            if turbine_id > 0
                if berth_id > 0
                    if process_id == 0
                        label_text = sprintf('T%d装载', turbine_id);
                    elseif process_id == 5
                        label_text = sprintf('T%d组装', turbine_id);
                    else
                        label_text = sprintf('T%d-B%d', turbine_id, berth_id);
                    end
                else
                    if process_id == 6
                        label_text = sprintf('T%d最终', turbine_id);
                    else
                        label_text = sprintf('T%d', turbine_id);
                    end
                end
            elseif process_id == -3
                label_text = '拼接';
            elseif process_id == -1
                label_text = '→风场';
            elseif process_id == -2
                label_text = '←港口';
            else
                label_text = '';
            end

            if ~isempty(label_text)
                text_color = 'white';
                if process_id == -3
                    text_color = 'black';
                end
                text(start_time + duration/2, vessel_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', text_color);
            end
        end
    end

    % 添加船舶标签
    for v = 1:vessel_count
        text(-makespan*0.02, v, sprintf('双船%d', v), ...
            'HorizontalAlignment', 'right', 'FontSize', 10, 'FontWeight', 'bold');
    end

    title(sprintf('双体船调度方案 - 总工期: %.2f小时', makespan), ...
        'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, vessel_count + 1]);
    xlim([0, makespan * 1.05]);
    grid on;

    %% 子图2：泊位利用率
    subplot(2, 1, 2);
    hold on;

    berth_count = length(berths);

    for i = 1:length(schedule.turbine_id)
        berth_id = schedule.berth_id(i);
        if berth_id > 0 && berth_id <= berth_count
            start_time = schedule.start_time(i);
            end_time = schedule.end_time(i);
            duration = end_time - start_time;
            process_id = schedule.process_id(i);
            turbine_id = schedule.turbine_id(i);
            vessel_id = schedule.vessel_id(i);

            color_idx = getColorIndex(process_id);

            rectangle('Position', [start_time, berth_id-0.4, duration, 0.8], ...
                'FaceColor', colors(color_idx,:), 'EdgeColor', 'k', 'LineWidth', 1.0);

            if turbine_id > 0 && duration > 4
                if process_id == 0
                    label_text = sprintf('V%d-T%d装载', vessel_id, turbine_id);
                elseif process_id == 5
                    label_text = sprintf('V%d-T%d组装', vessel_id, turbine_id);
                else
                    label_text = sprintf('V%d-T%d', vessel_id, turbine_id);
                end
                text(start_time + duration/2, berth_id, label_text, ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                    'FontSize', 8, 'FontWeight', 'bold', 'Color', 'white');
            end
        end
    end

    % 添加泊位标签
    for b = 1:berth_count
        text(-makespan*0.02, b, sprintf('泊位%d', b), ...
            'HorizontalAlignment', 'right', 'FontSize', 10, 'FontWeight', 'bold');
    end

    title('双体船方案 - 泊位利用情况', 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('泊位编号', 'FontSize', 12);
    ylim([0, berth_count + 1]);
    xlim([0, makespan * 1.05]);
    grid on;

    % 添加图例
    addLegend(colors);

    hold off;
end

%% 添加图例函数
function addLegend(colors)
    % 创建图例句柄
    legend_handles = [];
    legend_names = {};

    % 工序图例
    process_legend = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', ...
                     '叶片安装', '船上组装', '最终安装', '返回港口', '拼接焊接'};

    for i = 1:length(process_legend)
        legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 8, 'Color', colors(i,:));
        legend_names{end+1} = process_legend{i};
    end

    % 添加边框样式图例
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '-');
    legend_names{end+1} = '使用泊位';
    legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 3, 'Color', 'k', 'LineStyle', '--');
    legend_names{end+1} = '海上作业';

    % 添加图例
    legend(legend_handles, legend_names, 'Location', 'southoutside', ...
        'Orientation', 'horizontal', 'FontSize', 9, 'NumColumns', 6);
end

%% 颜色索引辅助函数
function color_idx = getColorIndex(process_id)
    switch process_id
        case 0, color_idx = 1;   % 装载
        case -1, color_idx = 2;  % 前往风场
        case 1, color_idx = 3;   % 基础安装
        case 2, color_idx = 4;   % 塔筒安装
        case 3, color_idx = 5;   % 机舱安装
        case 4, color_idx = 6;   % 叶片安装
        case 5, color_idx = 7;   % 船上组装
        case 6, color_idx = 8;   % 最终安装
        case -2, color_idx = 9;  % 返回港口
        case -3, color_idx = 10; % 拼接焊接
        otherwise, color_idx = 1;
    end
end
