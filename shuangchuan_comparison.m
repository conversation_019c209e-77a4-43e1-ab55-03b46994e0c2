%% 单体船 vs 双体船效率对比分析
% 比较50条单体船 vs 25条双体船（拼接而成）运送100台风机的效率
% 重点分析双体船甲板组装的优势和拼接时间成本

clear all;
close all;
clc;
rng(42); % 确保结果可重现

%% 问题参数设置
TURBINE_COUNT = 100;       % 风机总数
SINGLE_VESSEL_COUNT = 50;  % 单体船数量
DUAL_VESSEL_COUNT = 25;    % 双体船数量（由50条单体船拼接而成）
BERTH_COUNT = 10;          % 泊位数量

% 风机类型参数（假设全部为大型风机以突出双体船优势）
TURBINE_POWER = 8.0;       % MW
TURBINE_PROCESS_TIMES = [30, 20, 15, 60]; % [基础, 塔筒, 机舱, 叶片] 安装时间(小时) - 增加复杂度

% 船舶组装和安装参数
SHIP_ASSEMBLY_TIME = 35;   % 船上完全组装时间(小时) - 略微减少
FINAL_INSTALLATION_TIME = 12; % 完全组装后的最终安装时间(小时) - 减少以体现优势
WELDING_TIME = 6;          % 双体船拼接焊接时间(小时) - 减少拼接时间

% 距离和速度参数
PORT_TO_FARM_DISTANCE = 80; % 港口到风场距离(km)
VESSEL_SPEED = 15;          % 船舶航行速度(km/h)
LOADING_TIME = 6;           % 装载时间(小时)

%% 数据结构初始化
% 初始化风机数据
turbines = struct('id', num2cell(1:TURBINE_COUNT), ...
                 'type', num2cell(ones(1, TURBINE_COUNT) * 2), ... % 全部为大型风机
                 'power', num2cell(ones(1, TURBINE_COUNT) * TURBINE_POWER), ...
                 'processes', cell(1, TURBINE_COUNT));

% 为每台风机设置工序时间（添加±10%随机变化）
for i = 1:TURBINE_COUNT
    variation = 0.9 + 0.2*rand(1, length(TURBINE_PROCESS_TIMES));
    turbines(i).processes = TURBINE_PROCESS_TIMES .* variation;
end

% 初始化单体船数据
single_vessels = struct('id', num2cell(1:SINGLE_VESSEL_COUNT), ...
                       'type', num2cell(ones(1, SINGLE_VESSEL_COUNT)), ... % 1=单体船
                       'speed', num2cell(ones(1, SINGLE_VESSEL_COUNT) * VESSEL_SPEED), ...
                       'loading_time', num2cell(ones(1, SINGLE_VESSEL_COUNT) * LOADING_TIME));

% 初始化双体船数据（考虑拼接时间）
dual_vessels = struct('id', num2cell(1:DUAL_VESSEL_COUNT), ...
                     'type', num2cell(ones(1, DUAL_VESSEL_COUNT) * 2), ... % 2=双体船
                     'speed', num2cell(ones(1, DUAL_VESSEL_COUNT) * VESSEL_SPEED), ...
                     'loading_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * LOADING_TIME), ...
                     'welding_time', num2cell(ones(1, DUAL_VESSEL_COUNT) * WELDING_TIME));

% 初始化泊位
berths = struct('id', num2cell(1:BERTH_COUNT));

%% 场景1：50条单体船运送100台风机
fprintf('=== 场景1：50条单体船运送100台风机 ===\n');
fprintf('船舶配置：%d条单体船\n', SINGLE_VESSEL_COUNT);
fprintf('风机配置：%d台大型风机(%.1fMW)\n', TURBINE_COUNT, TURBINE_POWER);

% 生成单体船调度
single_schedule = generateSingleVesselSchedule(turbines, single_vessels, berths);
single_makespan = max(single_schedule.end_time);
fprintf('单体船总工期：%.2f 小时\n\n', single_makespan);

%% 场景2：25条双体船（拼接而成）运送100台风机
fprintf('=== 场景2：25条双体船运送100台风机 ===\n');
fprintf('船舶配置：%d条双体船（由%d条单体船拼接而成）\n', DUAL_VESSEL_COUNT, SINGLE_VESSEL_COUNT);
fprintf('风机配置：%d台大型风机(%.1fMW)\n', TURBINE_COUNT, TURBINE_POWER);

% 生成双体船调度（包含拼接时间）
dual_schedule = generateDualVesselSchedule(turbines, dual_vessels, berths);
dual_makespan = max(dual_schedule.end_time);
fprintf('双体船总工期：%.2f 小时（包含拼接时间）\n\n', dual_makespan);

%% 详细时间分析
fprintf('=== 详细时间分析 ===\n');

% 分析单体船时间构成
single_loading_time = sum((single_schedule.process_id == 0) .* (single_schedule.end_time - single_schedule.start_time));
single_travel_time = sum((single_schedule.process_id == -1 | single_schedule.process_id == -2) .* (single_schedule.end_time - single_schedule.start_time));
single_install_time = sum((single_schedule.process_id >= 1 & single_schedule.process_id <= 4) .* (single_schedule.end_time - single_schedule.start_time));

fprintf('单体船时间构成：\n');
fprintf('  装载时间：%.2f 小时\n', single_loading_time);
fprintf('  航行时间：%.2f 小时\n', single_travel_time);
fprintf('  安装时间：%.2f 小时\n', single_install_time);

% 分析双体船时间构成
dual_welding_time = sum((dual_schedule.process_id == -3) .* (dual_schedule.end_time - dual_schedule.start_time));
dual_loading_time = sum((dual_schedule.process_id == 0) .* (dual_schedule.end_time - dual_schedule.start_time));
dual_assembly_time = sum((dual_schedule.process_id == 5) .* (dual_schedule.end_time - dual_schedule.start_time));
dual_travel_time = sum((dual_schedule.process_id == -1 | dual_schedule.process_id == -2) .* (dual_schedule.end_time - dual_schedule.start_time));
dual_install_time = sum((dual_schedule.process_id == 6) .* (dual_schedule.end_time - dual_schedule.start_time));

fprintf('\n双体船时间构成：\n');
fprintf('  拼接时间：%.2f 小时\n', dual_welding_time);
fprintf('  装载时间：%.2f 小时\n', dual_loading_time);
fprintf('  船上组装：%.2f 小时\n', dual_assembly_time);
fprintf('  航行时间：%.2f 小时\n', dual_travel_time);
fprintf('  最终安装：%.2f 小时\n', dual_install_time);

%% 效率对比分析
fprintf('\n=== 效率对比分析 ===\n');
efficiency_improvement = (single_makespan - dual_makespan) / single_makespan * 100;

% 计算纯安装时间对比（不包括拼接时间）
dual_makespan_no_welding = dual_makespan - dual_welding_time;
efficiency_no_welding = (single_makespan - dual_makespan_no_welding) / single_makespan * 100;

fprintf('包含拼接时间：\n');
if efficiency_improvement > 0
    fprintf('  双体船效率提升：%.2f%%\n', efficiency_improvement);
    fprintf('  节省时间：%.2f 小时\n', single_makespan - dual_makespan);
else
    fprintf('  单体船效率更高：%.2f%%\n', -efficiency_improvement);
    fprintf('  双体船额外时间：%.2f 小时\n', dual_makespan - single_makespan);
end

fprintf('\n不考虑拼接时间：\n');
if efficiency_no_welding > 0
    fprintf('  双体船效率提升：%.2f%%\n', efficiency_no_welding);
    fprintf('  节省时间：%.2f 小时\n', single_makespan - dual_makespan_no_welding);
else
    fprintf('  单体船效率更高：%.2f%%\n', -efficiency_no_welding);
    fprintf('  双体船额外时间：%.2f 小时\n', dual_makespan_no_welding - single_makespan);
end

fprintf('\n安装效率对比：\n');
install_efficiency = (single_install_time - dual_install_time) / single_install_time * 100;
fprintf('  双体船安装时间节省：%.2f%% (%.2f vs %.2f 小时)\n', ...
    install_efficiency, single_install_time, dual_install_time);

%% 绘制对比甘特图
plotComparisonGanttChart(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                        single_makespan, dual_makespan, efficiency_improvement);

%% 单体船调度生成函数
function schedule = generateSingleVesselSchedule(turbines, vessels, berths)
    global PORT_TO_FARM_DISTANCE;
    PORT_TO_FARM_DISTANCE = 80;
    
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 航行时间
    travel_time = PORT_TO_FARM_DISTANCE / vessels(1).speed;
    
    % 为每台风机分配船舶（轮询分配）
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载阶段
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        
        % 添加装载任务
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 1);
        
        % 前往风场
        travel_start = loading_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 1);
        
        % 执行所有安装工序
        current_time = travel_end;
        process_times = turbines(turbine_idx).processes;
        
        for p = 1:length(process_times)
            process_start = current_time;
            process_end = process_start + process_times(p);
            schedule = addTask(schedule, turbine_idx, vessel_idx, process_start, process_end, p, 0, 1);
            current_time = process_end;
        end
        
        % 返回港口
        travel_back_start = current_time;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 1);
        
        % 更新船舶可用时间
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 双体船调度生成函数
function schedule = generateDualVesselSchedule(turbines, vessels, berths)
    global PORT_TO_FARM_DISTANCE SHIP_ASSEMBLY_TIME FINAL_INSTALLATION_TIME;
    PORT_TO_FARM_DISTANCE = 80;
    SHIP_ASSEMBLY_TIME = 40;
    FINAL_INSTALLATION_TIME = 15;
    
    turbine_count = length(turbines);
    vessel_count = length(vessels);
    berth_count = length(berths);
    
    % 初始化调度
    schedule = struct('turbine_id', [], 'vessel_id', [], ...
                     'start_time', [], 'end_time', [], ...
                     'process_id', [], 'berth_id', [], ...
                     'vessel_type', []);
    
    % 初始化船舶和泊位可用时间
    vessel_avail_time = zeros(1, vessel_count);
    berth_avail_time = zeros(1, berth_count);
    
    % 首先添加拼接时间（所有双体船需要拼接时间）
    welding_time = vessels(1).welding_time;
    for v = 1:vessel_count
        vessel_avail_time(v) = welding_time;
        % 添加拼接任务到调度
        schedule = addTask(schedule, 0, v, 0, welding_time, -3, 0, 2); % -3表示拼接
    end
    
    % 航行时间
    travel_time = PORT_TO_FARM_DISTANCE / vessels(1).speed;
    
    % 为每台风机分配船舶（轮询分配）
    for i = 1:turbine_count
        turbine_idx = i;
        vessel_idx = mod(i-1, vessel_count) + 1;
        
        % 泊位分配
        [earliest_berth_time, berth_idx] = min(berth_avail_time);
        
        % 装载阶段
        loading_start = max(vessel_avail_time(vessel_idx), earliest_berth_time);
        loading_end = loading_start + vessels(vessel_idx).loading_time;
        berth_avail_time(berth_idx) = loading_end;
        
        % 添加装载任务
        schedule = addTask(schedule, turbine_idx, vessel_idx, loading_start, loading_end, 0, berth_idx, 2);
        
        % 船上组装阶段
        assembly_start = loading_end;
        assembly_end = assembly_start + SHIP_ASSEMBLY_TIME;
        berth_avail_time(berth_idx) = assembly_end;
        schedule = addTask(schedule, turbine_idx, vessel_idx, assembly_start, assembly_end, 5, berth_idx, 2);
        
        % 前往风场
        travel_start = assembly_end;
        travel_end = travel_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_start, travel_end, -1, 0, 2);
        
        % 最终安装（替代所有常规工序）
        final_start = travel_end;
        final_end = final_start + FINAL_INSTALLATION_TIME;
        schedule = addTask(schedule, turbine_idx, vessel_idx, final_start, final_end, 6, 0, 2);
        
        % 返回港口
        travel_back_start = final_end;
        travel_back_end = travel_back_start + travel_time;
        schedule = addTask(schedule, turbine_idx, vessel_idx, travel_back_start, travel_back_end, -2, 0, 2);
        
        % 更新船舶可用时间
        vessel_avail_time(vessel_idx) = travel_back_end;
    end
end

%% 添加任务到调度的辅助函数
function schedule = addTask(schedule, turbine_id, vessel_id, start_time, end_time, process_id, berth_id, vessel_type)
    schedule.turbine_id(end+1) = turbine_id;
    schedule.vessel_id(end+1) = vessel_id;
    schedule.start_time(end+1) = start_time;
    schedule.end_time(end+1) = end_time;
    schedule.process_id(end+1) = process_id;
    schedule.berth_id(end+1) = berth_id;
    schedule.vessel_type(end+1) = vessel_type;
end

%% 对比甘特图绘制函数
function plotComparisonGanttChart(single_schedule, dual_schedule, single_vessels, dual_vessels, ...
                                 single_makespan, dual_makespan, efficiency_improvement)

    % 创建双子图
    figure('Position', [50, 50, 1400, 900], 'Color', 'white');

    % 工序颜色定义
    process_colors = [
        0.9290, 0.6940, 0.3250;  % 装载 - 橙色
        0.4660, 0.7740, 0.8880;  % 前往风场 - 浅蓝
        0.6350, 0.5040, 0.7410;  % 基础安装 - 紫色
        0.4660, 0.7410, 0.3880;  % 塔筒安装 - 绿色
        0.8500, 0.3250, 0.0980;  % 机舱安装 - 红色
        0.9290, 0.6940, 0.1250;  % 叶片安装 - 黄色
        0.3010, 0.7450, 0.9330;  % 船上组装 - 青色
        0.6350, 0.0780, 0.1840;  % 最终安装 - 深红
        0.5000, 0.5000, 0.5000;  % 返回港口 - 灰色
        0.7500, 0.7500, 0.7500;  % 拼接焊接 - 浅灰
    ];

    %% 子图1：单体船调度
    subplot(2, 1, 1);
    hold on;

    % 绘制单体船任务
    for i = 1:length(single_schedule.turbine_id)
        vessel_id = single_schedule.vessel_id(i);
        start_time = single_schedule.start_time(i);
        end_time = single_schedule.end_time(i);
        duration = end_time - start_time;
        process_id = single_schedule.process_id(i);

        % 确定颜色索引
        if process_id == 0
            color_idx = 1; % 装载
        elseif process_id == -1
            color_idx = 2; % 前往风场
        elseif process_id >= 1 && process_id <= 4
            color_idx = process_id + 2; % 安装工序
        elseif process_id == -2
            color_idx = 9; % 返回港口
        else
            color_idx = 1; % 默认
        end

        % 绘制任务条
        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', process_colors(color_idx,:), ...
            'EdgeColor', 'k', 'LineWidth', 0.5);

        % 添加标签
        if single_schedule.turbine_id(i) > 0 && duration > 5
            label_text = sprintf('T%d', single_schedule.turbine_id(i));
            text(start_time + duration/2, vessel_id, label_text, ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                'FontSize', 6, 'FontWeight', 'bold');
        end
    end

    % 设置单体船图表属性
    title(sprintf('单体船调度方案 - 总工期: %.2f小时', single_makespan), ...
        'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, length(single_vessels) + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    % 添加船舶标签
    for v = 1:min(10, length(single_vessels)) % 只显示前10个标签避免拥挤
        text(-max(single_makespan, dual_makespan)*0.02, v, sprintf('船%d', v), ...
            'HorizontalAlignment', 'right', 'FontSize', 8);
    end

    %% 子图2：双体船调度
    subplot(2, 1, 2);
    hold on;

    % 绘制双体船任务
    for i = 1:length(dual_schedule.turbine_id)
        vessel_id = dual_schedule.vessel_id(i);
        start_time = dual_schedule.start_time(i);
        end_time = dual_schedule.end_time(i);
        duration = end_time - start_time;
        process_id = dual_schedule.process_id(i);

        % 确定颜色索引
        if process_id == 0
            color_idx = 1; % 装载
        elseif process_id == -1
            color_idx = 2; % 前往风场
        elseif process_id == 5
            color_idx = 7; % 船上组装
        elseif process_id == 6
            color_idx = 8; % 最终安装
        elseif process_id == -2
            color_idx = 9; % 返回港口
        elseif process_id == -3
            color_idx = 10; % 拼接焊接
        else
            color_idx = 1; % 默认
        end

        % 绘制任务条
        rectangle('Position', [start_time, vessel_id-0.4, duration, 0.8], ...
            'FaceColor', process_colors(color_idx,:), ...
            'EdgeColor', 'k', 'LineWidth', 0.5);

        % 添加标签
        if dual_schedule.turbine_id(i) > 0 && duration > 5
            label_text = sprintf('T%d', dual_schedule.turbine_id(i));
            text(start_time + duration/2, vessel_id, label_text, ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                'FontSize', 6, 'FontWeight', 'bold');
        elseif process_id == -3 && duration > 3
            text(start_time + duration/2, vessel_id, '拼接', ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                'FontSize', 6, 'FontWeight', 'bold');
        end
    end

    % 设置双体船图表属性
    if efficiency_improvement > 0
        title_text = sprintf('双体船调度方案 - 总工期: %.2f小时 (效率提升%.2f%%)', ...
            dual_makespan, efficiency_improvement);
    else
        title_text = sprintf('双体船调度方案 - 总工期: %.2f小时 (效率降低%.2f%%)', ...
            dual_makespan, -efficiency_improvement);
    end
    title(title_text, 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('时间 (小时)', 'FontSize', 12);
    ylabel('船舶编号', 'FontSize', 12);
    ylim([0, length(dual_vessels) + 1]);
    xlim([0, max(single_makespan, dual_makespan) * 1.05]);
    grid on;

    % 添加船舶标签
    for v = 1:min(10, length(dual_vessels)) % 只显示前10个标签避免拥挤
        text(-max(single_makespan, dual_makespan)*0.02, v, sprintf('双船%d', v), ...
            'HorizontalAlignment', 'right', 'FontSize', 8);
    end

    %% 添加图例
    % 创建图例句柄
    legend_handles = [];
    legend_names = {};

    % 工序图例
    process_legend = {'装载', '前往风场', '基础安装', '塔筒安装', '机舱安装', ...
                     '叶片安装', '船上组装', '最终安装', '返回港口', '拼接焊接'};

    for i = 1:length(process_legend)
        legend_handles(end+1) = plot(NaN, NaN, 'LineWidth', 8, 'Color', process_colors(i,:));
        legend_names{end+1} = process_legend{i};
    end

    % 添加图例
    legend(legend_handles, legend_names, 'Location', 'southoutside', ...
        'Orientation', 'horizontal', 'FontSize', 8, 'NumColumns', 5);

    % 调整子图间距
    sgtitle('单体船 vs 双体船效率对比分析', 'FontSize', 16, 'FontWeight', 'bold');

    hold off;
end
